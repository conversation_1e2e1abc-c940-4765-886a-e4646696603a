# Database Setup Guide

## Fixing the "relation 'public.categories' does not exist" Error

This error occurs because the necessary database tables for categories and collections have not been created yet. Follow these steps to set up the required database schema:

## Option 1: Using the Supabase Dashboard (Easiest)

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Select your project
3. Navigate to the SQL Editor
4. Copy and paste the contents of each migration file from the `supabase/migrations` directory
5. Execute the SQL statements in order:
   - First: `20240601000001_create_categories_table.sql`
   - Second: `20240601000002_create_collections_tables.sql`
   - Third: `20240601000003_create_collection_functions.sql`
   - Fourth: `20240601000004_update_videos_table.sql`

## Option 2: Using the Supabase CLI

1. Make sure you have the Supabase CLI installed:
   ```
   npm install -g supabase
   ```

2. Log in to your Supabase account:
   ```
   supabase login
   ```

3. Link your project (if not already linked):
   ```
   supabase link --project-ref your-project-ref
   ```
   Replace `your-project-ref` with your actual Supabase project reference ID.

4. Apply the migrations:
   ```
   supabase db push
   ```

## Option 3: Manual SQL Execution

If you prefer to run the SQL statements manually, here's what you need to execute:

### 1. Create the categories table

```sql
CREATE TABLE IF NOT EXISTS public.categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add some default categories
INSERT INTO public.categories (name, slug, description)
VALUES
  ('Hot', 'hot', 'Popular and trending videos'),
  ('Trending', 'trending', 'Videos gaining popularity right now'),
  ('New', 'new', 'Recently uploaded videos')
ON CONFLICT (slug) DO NOTHING;

-- Enable RLS on categories table
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;

-- Create policies for categories
CREATE POLICY "Anyone can view categories"
ON public.categories
FOR SELECT
USING (true);
```

### 2. Create the collections tables

```sql
-- Create collections table
CREATE TABLE IF NOT EXISTS public.collections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  thumbnail_url TEXT,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  is_public BOOLEAN DEFAULT true NOT NULL,
  video_count INTEGER DEFAULT 0 NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create collection_videos junction table
CREATE TABLE IF NOT EXISTS public.collection_videos (
  collection_id UUID NOT NULL REFERENCES public.collections(id) ON DELETE CASCADE,
  video_id UUID NOT NULL REFERENCES public.videos(id) ON DELETE CASCADE,
  position INTEGER NOT NULL DEFAULT 0,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  PRIMARY KEY (collection_id, video_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS collection_videos_collection_id_idx ON public.collection_videos (collection_id);
CREATE INDEX IF NOT EXISTS collection_videos_video_id_idx ON public.collection_videos (video_id);

-- Enable RLS on collections tables
ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collection_videos ENABLE ROW LEVEL SECURITY;

-- Create policies for collections
CREATE POLICY "Users can view their own collections and public collections"
ON public.collections
FOR SELECT
USING (
  auth.uid() = user_id OR is_public = true
);

CREATE POLICY "Users can insert their own collections"
ON public.collections
FOR INSERT
TO authenticated
WITH CHECK (
  auth.uid() = user_id
);

CREATE POLICY "Users can update their own collections"
ON public.collections
FOR UPDATE
TO authenticated
USING (
  auth.uid() = user_id
);

CREATE POLICY "Users can delete their own collections"
ON public.collections
FOR DELETE
TO authenticated
USING (
  auth.uid() = user_id
);

-- Create policies for collection_videos
CREATE POLICY "Users can view videos in their collections and public collections"
ON public.collection_videos
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = collection_id AND (c.user_id = auth.uid() OR c.is_public = true)
  )
);

CREATE POLICY "Users can insert videos into their own collections"
ON public.collection_videos
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = collection_id AND c.user_id = auth.uid()
  )
);

CREATE POLICY "Users can update videos in their own collections"
ON public.collection_videos
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = collection_id AND c.user_id = auth.uid()
  )
);

CREATE POLICY "Users can delete videos from their own collections"
ON public.collection_videos
FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = collection_id AND c.user_id = auth.uid()
  )
);
```

### 3. Create the collection functions

```sql
-- Function to increment collection video count
CREATE OR REPLACE FUNCTION increment_collection_video_count(collection_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.collections
  SET video_count = video_count + 1,
      updated_at = now()
  WHERE id = collection_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrement collection video count
CREATE OR REPLACE FUNCTION decrement_collection_video_count(collection_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.collections
  SET video_count = GREATEST(0, video_count - 1),
      updated_at = now()
  WHERE id = collection_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reorder collection videos
CREATE OR REPLACE FUNCTION reorder_collection_videos(updates_json JSONB)
RETURNS void AS $$
DECLARE
  update_item JSONB;
BEGIN
  FOR update_item IN SELECT * FROM jsonb_array_elements(updates_json)
  LOOP
    UPDATE public.collection_videos
    SET position = (update_item->>'position')::INTEGER
    WHERE collection_id = (update_item->>'collection_id')::UUID
      AND video_id = (update_item->>'video_id')::UUID;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 4. Update the videos table

```sql
-- Add category field to videos table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'videos'
    AND column_name = 'category'
  ) THEN
    ALTER TABLE public.videos
    ADD COLUMN category TEXT DEFAULT 'uncategorized' NOT NULL;
  END IF;
END $$;
```

## Troubleshooting

If you continue to experience issues after applying these migrations, check the following:

1. Make sure your Supabase project has the `uuid-ossp` extension enabled
2. Verify that the `profiles` and `videos` tables already exist
3. Check that you have the necessary permissions to create tables and functions
4. Look for any error messages in the Supabase dashboard or CLI output

After applying these migrations, restart your application and the error should be resolved.
