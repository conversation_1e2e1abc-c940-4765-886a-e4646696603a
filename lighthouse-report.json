{"lighthouseVersion": "12.6.0", "requestedUrl": "https://bluefilm-video-platform.vercel.app/", "mainDocumentUrl": "https://bluefilm-video-platform.vercel.app/", "finalDisplayedUrl": "https://bluefilm-video-platform.vercel.app/", "finalUrl": "https://bluefilm-video-platform.vercel.app/", "fetchTime": "2025-05-07T19:05:22.286Z", "gatherMode": "navigation", "runtimeError": {"code": "ERRORED_DOCUMENT_REQUEST", "message": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)"}, "runWarnings": ["Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)"], "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "environment": {"hostUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "benchmarkIndex": 2958.5, "credits": {}}, "audits": {"is-on-https": {"id": "is-on-https", "title": "Uses HTTPS", "description": "All sites should be protected with HTTPS, even ones that don't handle sensitive data. This includes avoiding [mixed content](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), where some resources are loaded over HTTP despite the initial request being served over HTTPS. HTTPS prevents intruders from tampering with or passively listening in on the communications between your app and your users, and is a prerequisite for HTTP/2 and many new web platform APIs. [Learn more about HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "redirects-http": {"id": "redirects-http", "title": "Redirects HTTP traffic to HTTPS", "description": "Make sure that you redirect all HTTP traffic to HTTPS in order to enable secure web features for all your users. [Learn more](https://developer.chrome.com/docs/lighthouse/pwa/redirects-http/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "viewport": {"id": "viewport", "title": "Has a `<meta name=\"viewport\">` tag with `width` or `initial-scale`", "description": "A `<meta name=\"viewport\">` not only optimizes your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "first-contentful-paint": {"id": "first-contentful-paint", "title": "First Contentful Paint", "description": "First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "largest-contentful-paint": {"id": "largest-contentful-paint", "title": "Largest Contentful Paint", "description": "Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "first-meaningful-paint": {"id": "first-meaningful-paint", "title": "First Meaningful Paint", "description": "First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "speed-index": {"id": "speed-index", "title": "Speed Index", "description": "Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "screenshot-thumbnails": {"id": "screenshot-thumbnails", "title": "Screenshot Thumbnails", "description": "This is what the load of your site looked like.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "final-screenshot": {"id": "final-screenshot", "title": "Final Screenshot", "description": "The last screenshot captured of the pageload.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "total-blocking-time": {"id": "total-blocking-time", "title": "Total Blocking Time", "description": "Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "max-potential-fid": {"id": "max-potential-fid", "title": "Max Potential First Input Delay", "description": "The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "cumulative-layout-shift": {"id": "cumulative-layout-shift", "title": "Cumulative Layout Shift", "description": "Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "errors-in-console": {"id": "errors-in-console", "title": "No browser errors logged to the console", "description": "Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "server-response-time": {"id": "server-response-time", "title": "Initial server response time was short", "description": "Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1}, "interactive": {"id": "interactive", "title": "Time to Interactive", "description": "Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "user-timings": {"id": "user-timings", "title": "User Timing marks and measures", "description": "Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "critical-request-chains": {"id": "critical-request-chains", "title": "Avoid chaining critical requests", "description": "The Critical Request Chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1}, "redirects": {"id": "redirects", "title": "Avoid multiple page redirects", "description": "Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "image-aspect-ratio": {"id": "image-aspect-ratio", "title": "Displays images with correct aspect ratio", "description": "Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "image-size-responsive": {"id": "image-size-responsive", "title": "Serves images with appropriate resolution", "description": "Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "deprecations": {"id": "deprecations", "title": "Avoids deprecated APIs", "description": "Deprecated APIs will eventually be removed from the browser. [Learn more about deprecated APIs](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "third-party-cookies": {"id": "third-party-cookies", "title": "Avoids third-party cookies", "description": "Third-party cookies may be blocked in some contexts. [Learn more about preparing for third-party cookie restrictions](https://privacysandbox.google.com/cookies/prepare/overview).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "mainthread-work-breakdown": {"id": "mainthread-work-breakdown", "title": "Minimizes main-thread work", "description": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimize main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1}, "bootup-time": {"id": "bootup-time", "title": "JavaScript execution time", "description": "Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1}, "uses-rel-preconnect": {"id": "uses-rel-preconnect", "title": "Preconnect to required origins", "description": "Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "font-display": {"id": "font-display", "title": "All text remains visible during webfont loads", "description": "Leverage the `font-display` CSS feature to ensure text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "diagnostics": {"id": "diagnostics", "title": "Diagnostics", "description": "Collection of useful page vitals.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "network-requests": {"id": "network-requests", "title": "Network Requests", "description": "Lists the network requests that were made during page load.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "network-rtt": {"id": "network-rtt", "title": "Network Round Trip Times", "description": "Network round trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "network-server-latency": {"id": "network-server-latency", "title": "Server Backend Latencies", "description": "Server latencies can impact web performance. If the server latency of an origin is high, it's an indication the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "main-thread-tasks": {"id": "main-thread-tasks", "title": "Tasks", "description": "Lists the toplevel main thread tasks that executed during page load.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "metrics": {"id": "metrics", "title": "Metrics", "description": "Collects all available metrics.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "resource-summary": {"id": "resource-summary", "title": "Resources Summary", "description": "Aggregates all network requests and groups them by type", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "third-party-summary": {"id": "third-party-summary", "title": "Minimize third-party usage", "description": "Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimize third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1}, "third-party-facades": {"id": "third-party-facades", "title": "Lazy load third-party resources with facades", "description": "Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "largest-contentful-paint-element": {"id": "largest-contentful-paint-element", "title": "Largest Contentful Paint element", "description": "This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1}, "lcp-lazy-loaded": {"id": "lcp-lazy-loaded", "title": "Largest Contentful Paint image was not lazily loaded", "description": "Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the largest contentful paint. [Learn more about optimal lazy loading](https://web.dev/articles/lcp-lazy-loading).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "layout-shifts": {"id": "layout-shifts", "title": "Avoid large layout shifts", "description": "These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "long-tasks": {"id": "long-tasks", "title": "Avoid long main-thread tasks", "description": "Lists the longest tasks on the main thread, useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/articles/optimize-long-tasks)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1}, "non-composited-animations": {"id": "non-composited-animations", "title": "Avoid non-composited animations", "description": "Animations which are not composited can be janky and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "unsized-images": {"id": "unsized-images", "title": "Image elements have explicit `width` and `height`", "description": "Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/articles/optimize-cls#images_without_dimensions)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 4}, "valid-source-maps": {"id": "valid-source-maps", "title": "Page has valid source maps", "description": "Source maps translate minified code to the original source code. This helps developers debug in production. In addition, Lighthouse is able to provide further insights. Consider deploying source maps to take advantage of these benefits. [Learn more about source maps](https://developer.chrome.com/docs/devtools/javascript/source-maps/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "prioritize-lcp-image": {"id": "prioritize-lcp-image", "title": "Preload Largest Contentful Paint image", "description": "If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 4}, "csp-xss": {"id": "csp-xss", "title": "Ensure CSP is effective against XSS attacks", "description": "A strong Content Security Policy (CSP) significantly reduces the risk of cross-site scripting (XSS) attacks. [Learn how to use a CSP to prevent XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "has-hsts": {"id": "has-hsts", "title": "Use a strong HSTS policy", "description": "Deployment of the HSTS header significantly reduces the risk of downgrading HTTP connections and eavesdropping attacks. A rollout in stages, starting with a low max-age is recommended. [Learn more about using a strong HSTS policy.](https://developer.chrome.com/docs/lighthouse/best-practices/has-hsts)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "origin-isolation": {"id": "origin-isolation", "title": "Ensure proper origin isolation with COOP", "description": "The Cross-Origin-Opener-Policy (COOP) can be used to isolate the top-level window from other documents such as pop-ups. [Learn more about deploying the COOP header.](https://web.dev/articles/why-coop-coep#coop)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "clickjacking-mitigation": {"id": "clickjacking-mitigation", "title": "Mitigate clickjacking with XFO or CSP", "description": "The `X-Frame-Options` (XFO) header or the `frame-ancestors` directive in the `Content-Security-Policy` (CSP) header control where a page can be embedded. These can mitigate clickjacking attacks by blocking some or all sites from embedding the page. [Learn more about mitigating clickjacking](https://developer.chrome.com/docs/lighthouse/best-practices/clickjacking-mitigation).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "script-treemap-data": {"id": "script-treemap-data", "title": "Script Treemap Data", "description": "Used for treemap app", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "accesskeys": {"id": "accesskeys", "title": "`[accesskey]` values are unique", "description": "Access keys let users quickly focus a part of the page. For proper navigation, each access key must be unique. [Learn more about access keys](https://dequeuniversity.com/rules/axe/4.10/accesskeys).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-allowed-attr": {"id": "aria-allowed-attr", "title": "`[aria-*]` attributes match their roles", "description": "Each ARIA `role` supports a specific subset of `aria-*` attributes. Mismatching these invalidates the `aria-*` attributes. [Learn how to match ARIA attributes to their roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-attr).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-allowed-role": {"id": "aria-allowed-role", "title": "Uses ARIA roles only on compatible elements", "description": "Many HTML elements can only be assigned certain ARIA roles. Using ARIA roles where they are not allowed can interfere with the accessibility of the web page. [Learn more about ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-role).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-command-name": {"id": "aria-command-name", "title": "`button`, `link`, and `menuitem` elements have accessible names", "description": "When an element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to make command elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-command-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-conditional-attr": {"id": "aria-conditional-attr", "title": "ARIA attributes are used as specified for the element's role", "description": "Some ARIA attributes are only allowed on an element under certain conditions. [Learn more about conditional ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-conditional-attr).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-deprecated-role": {"id": "aria-deprecated-role", "title": "Deprecated ARIA roles were not used", "description": "Deprecated ARIA roles may not be processed correctly by assistive technology. [Learn more about deprecated ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-deprecated-role).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-dialog-name": {"id": "aria-dialog-name", "title": "Elements with `role=\"dialog\"` or `role=\"alertdialog\"` have accessible names.", "description": "ARIA dialog elements without accessible names may prevent screen readers users from discerning the purpose of these elements. [Learn how to make ARIA dialog elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-dialog-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-hidden-body": {"id": "aria-hidden-body", "title": "`[aria-hidden=\"true\"]` is not present on the document `<body>`", "description": "Assistive technologies, like screen readers, work inconsistently when `aria-hidden=\"true\"` is set on the document `<body>`. [Learn how `aria-hidden` affects the document body](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-body).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-hidden-focus": {"id": "aria-hidden-focus", "title": "`[aria-hidden=\"true\"]` elements do not contain focusable descendents", "description": "Focusable descendents within an `[aria-hidden=\"true\"]` element prevent those interactive elements from being available to users of assistive technologies like screen readers. [Learn how `aria-hidden` affects focusable elements](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-focus).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-input-field-name": {"id": "aria-input-field-name", "title": "ARIA input fields have accessible names", "description": "When an input field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about input field labels](https://dequeuniversity.com/rules/axe/4.10/aria-input-field-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-meter-name": {"id": "aria-meter-name", "title": "ARIA `meter` elements have accessible names", "description": "When a meter element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `meter` elements](https://dequeuniversity.com/rules/axe/4.10/aria-meter-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-progressbar-name": {"id": "aria-progressbar-name", "title": "ARIA `progressbar` elements have accessible names", "description": "When a `progressbar` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to label `progressbar` elements](https://dequeuniversity.com/rules/axe/4.10/aria-progressbar-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-prohibited-attr": {"id": "aria-prohibited-attr", "title": "Elements use only permitted ARIA attributes", "description": "Using ARIA attributes in roles where they are prohibited can mean that important information is not communicated to users of assistive technologies. [Learn more about prohibited ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-prohibited-attr).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-required-attr": {"id": "aria-required-attr", "title": "`[role]`s have all required `[aria-*]` attributes", "description": "Some ARIA roles have required attributes that describe the state of the element to screen readers. [Learn more about roles and required attributes](https://dequeuniversity.com/rules/axe/4.10/aria-required-attr).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-required-children": {"id": "aria-required-children", "title": "Elements with an ARIA `[role]` that require children to contain a specific `[role]` have all required children.", "description": "Some ARIA parent roles must contain specific child roles to perform their intended accessibility functions. [Learn more about roles and required children elements](https://dequeuniversity.com/rules/axe/4.10/aria-required-children).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-required-parent": {"id": "aria-required-parent", "title": "`[role]`s are contained by their required parent element", "description": "Some ARIA child roles must be contained by specific parent roles to properly perform their intended accessibility functions. [Learn more about ARIA roles and required parent element](https://dequeuniversity.com/rules/axe/4.10/aria-required-parent).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-roles": {"id": "aria-roles", "title": "`[role]` values are valid", "description": "ARIA roles must have valid values in order to perform their intended accessibility functions. [Learn more about valid ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-roles).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-text": {"id": "aria-text", "title": "Elements with the `role=text` attribute do not have focusable descendents.", "description": "Adding `role=text` around a text node split by markup enables VoiceOver to treat it as one phrase, but the element's focusable descendents will not be announced. [Learn more about the `role=text` attribute](https://dequeuniversity.com/rules/axe/4.10/aria-text).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-toggle-field-name": {"id": "aria-toggle-field-name", "title": "ARIA toggle fields have accessible names", "description": "When a toggle field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about toggle fields](https://dequeuniversity.com/rules/axe/4.10/aria-toggle-field-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-tooltip-name": {"id": "aria-tooltip-name", "title": "ARIA `tooltip` elements have accessible names", "description": "When a tooltip element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `tooltip` elements](https://dequeuniversity.com/rules/axe/4.10/aria-tooltip-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-treeitem-name": {"id": "aria-treeitem-name", "title": "ARIA `treeitem` elements have accessible names", "description": "When a `treeitem` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about labeling `treeitem` elements](https://dequeuniversity.com/rules/axe/4.10/aria-treeitem-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-valid-attr-value": {"id": "aria-valid-attr-value", "title": "`[aria-*]` attributes have valid values", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid values. [Learn more about valid values for ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr-value).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "aria-valid-attr": {"id": "aria-valid-attr", "title": "`[aria-*]` attributes are valid and not misspelled", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid names. [Learn more about valid ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "button-name": {"id": "button-name", "title": "Buttons have an accessible name", "description": "When a button doesn't have an accessible name, screen readers announce it as \"button\", making it unusable for users who rely on screen readers. [Learn how to make buttons more accessible](https://dequeuniversity.com/rules/axe/4.10/button-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "bypass": {"id": "bypass", "title": "The page contains a heading, skip link, or landmark region", "description": "Adding ways to bypass repetitive content lets keyboard users navigate the page more efficiently. [Learn more about bypass blocks](https://dequeuniversity.com/rules/axe/4.10/bypass).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "color-contrast": {"id": "color-contrast", "title": "Background and foreground colors have a sufficient contrast ratio", "description": "Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "definition-list": {"id": "definition-list", "title": "`<dl>`'s contain only properly-ordered `<dt>` and `<dd>` groups, `<script>`, `<template>` or `<div>` elements.", "description": "When definition lists are not properly marked up, screen readers may produce confusing or inaccurate output. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/definition-list).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "dlitem": {"id": "dlitem", "title": "Definition list items are wrapped in `<dl>` elements", "description": "Definition list items (`<dt>` and `<dd>`) must be wrapped in a parent `<dl>` element to ensure that screen readers can properly announce them. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/dlitem).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "document-title": {"id": "document-title", "title": "Document has a `<title>` element", "description": "The title gives screen reader users an overview of the page, and search engine users rely on it heavily to determine if a page is relevant to their search. [Learn more about document titles](https://dequeuniversity.com/rules/axe/4.10/document-title).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "duplicate-id-aria": {"id": "duplicate-id-aria", "title": "ARIA IDs are unique", "description": "The value of an ARIA ID must be unique to prevent other instances from being overlooked by assistive technologies. [Learn how to fix duplicate ARIA IDs](https://dequeuniversity.com/rules/axe/4.10/duplicate-id-aria).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "empty-heading": {"id": "empty-heading", "title": "All heading elements contain content.", "description": "A heading with no content or inaccessible text prevent screen reader users from accessing information on the page's structure. [Learn more about headings](https://dequeuniversity.com/rules/axe/4.10/empty-heading).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "form-field-multiple-labels": {"id": "form-field-multiple-labels", "title": "No form fields have multiple labels", "description": "Form fields with multiple labels can be confusingly announced by assistive technologies like screen readers which use either the first, the last, or all of the labels. [Learn how to use form labels](https://dequeuniversity.com/rules/axe/4.10/form-field-multiple-labels).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "frame-title": {"id": "frame-title", "title": "`<frame>` or `<iframe>` elements have a title", "description": "Screen reader users rely on frame titles to describe the contents of frames. [Learn more about frame titles](https://dequeuniversity.com/rules/axe/4.10/frame-title).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "heading-order": {"id": "heading-order", "title": "Heading elements appear in a sequentially-descending order", "description": "Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "html-has-lang": {"id": "html-has-lang", "title": "`<html>` element has a `[lang]` attribute", "description": "If a page doesn't specify a `lang` attribute, a screen reader assumes that the page is in the default language that the user chose when setting up the screen reader. If the page isn't actually in the default language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-has-lang).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "html-lang-valid": {"id": "html-lang-valid", "title": "`<html>` element has a valid value for its `[lang]` attribute", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) helps screen readers announce text properly. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-lang-valid).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "html-xml-lang-mismatch": {"id": "html-xml-lang-mismatch", "title": "`<html>` element has an `[xml:lang]` attribute with the same base language as the `[lang]` attribute.", "description": "If the webpage does not specify a consistent language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-xml-lang-mismatch).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "identical-links-same-purpose": {"id": "identical-links-same-purpose", "title": "Identical links have the same purpose.", "description": "Links with the same destination should have the same description, to help users understand the link's purpose and decide whether to follow it. [Learn more about identical links](https://dequeuniversity.com/rules/axe/4.10/identical-links-same-purpose).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "image-alt": {"id": "image-alt", "title": "Image elements have `[alt]` attributes", "description": "Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "image-redundant-alt": {"id": "image-redundant-alt", "title": "Image elements do not have `[alt]` attributes that are redundant text.", "description": "Informative elements should aim for short, descriptive alternative text. Alternative text that is exactly the same as the text adjacent to the link or image is potentially confusing for screen reader users, because the text will be read twice. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-redundant-alt).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "input-button-name": {"id": "input-button-name", "title": "Input buttons have discernible text.", "description": "Adding discernable and accessible text to input buttons may help screen reader users understand the purpose of the input button. [Learn more about input buttons](https://dequeuniversity.com/rules/axe/4.10/input-button-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "input-image-alt": {"id": "input-image-alt", "title": "`<input type=\"image\">` elements have `[alt]` text", "description": "When an image is being used as an `<input>` button, providing alternative text can help screen reader users understand the purpose of the button. [Learn about input image alt text](https://dequeuniversity.com/rules/axe/4.10/input-image-alt).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "label-content-name-mismatch": {"id": "label-content-name-mismatch", "title": "Elements with visible text labels have matching accessible names.", "description": "Visible text labels that do not match the accessible name can result in a confusing experience for screen reader users. [Learn more about accessible names](https://dequeuniversity.com/rules/axe/4.10/label-content-name-mismatch).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "label": {"id": "label", "title": "Form elements have associated labels", "description": "Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "landmark-one-main": {"id": "landmark-one-main", "title": "Document has a main landmark.", "description": "One main landmark helps screen reader users navigate a web page. [Learn more about landmarks](https://dequeuniversity.com/rules/axe/4.10/landmark-one-main).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "link-name": {"id": "link-name", "title": "Links have a discernible name", "description": "Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "link-in-text-block": {"id": "link-in-text-block", "title": "Links are distinguishable without relying on color.", "description": "Low-contrast text is difficult or impossible for many users to read. Link text that is discernible improves the experience for users with low vision. [Learn how to make links distinguishable](https://dequeuniversity.com/rules/axe/4.10/link-in-text-block).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "list": {"id": "list", "title": "Lists contain only `<li>` elements and script supporting elements (`<script>` and `<template>`).", "description": "Screen readers have a specific way of announcing lists. Ensuring proper list structure aids screen reader output. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/list).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "listitem": {"id": "listitem", "title": "List items (`<li>`) are contained within `<ul>`, `<ol>` or `<menu>` parent elements", "description": "Screen readers require list items (`<li>`) to be contained within a parent `<ul>`, `<ol>` or `<menu>` to be announced properly. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/listitem).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "meta-refresh": {"id": "meta-refresh", "title": "The document does not use `<meta http-equiv=\"refresh\">`", "description": "Users do not expect a page to refresh automatically, and doing so will move focus back to the top of the page. This may create a frustrating or confusing experience. [Learn more about the refresh meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-refresh).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "meta-viewport": {"id": "meta-viewport", "title": "`[user-scalable=\"no\"]` is not used in the `<meta name=\"viewport\">` element and the `[maximum-scale]` attribute is not less than 5.", "description": "Disabling zooming is problematic for users with low vision who rely on screen magnification to properly see the contents of a web page. [Learn more about the viewport meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-viewport).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "object-alt": {"id": "object-alt", "title": "`<object>` elements have alternate text", "description": "Screen readers cannot translate non-text content. Adding alternate text to `<object>` elements helps screen readers convey meaning to users. [Learn more about alt text for `object` elements](https://dequeuniversity.com/rules/axe/4.10/object-alt).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "select-name": {"id": "select-name", "title": "Select elements have associated label elements.", "description": "Form elements without effective labels can create frustrating experiences for screen reader users. [Learn more about the `select` element](https://dequeuniversity.com/rules/axe/4.10/select-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "skip-link": {"id": "skip-link", "title": "Skip links are focusable.", "description": "Including a skip link can help users skip to the main content to save time. [Learn more about skip links](https://dequeuniversity.com/rules/axe/4.10/skip-link).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "tabindex": {"id": "tabindex", "title": "No element has a `[tabindex]` value greater than 0", "description": "A value greater than 0 implies an explicit navigation ordering. Although technically valid, this often creates frustrating experiences for users who rely on assistive technologies. [Learn more about the `tabindex` attribute](https://dequeuniversity.com/rules/axe/4.10/tabindex).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "table-duplicate-name": {"id": "table-duplicate-name", "title": "Tables have different content in the summary attribute and `<caption>`.", "description": "The summary attribute should describe the table structure, while `<caption>` should have the onscreen title. Accurate table mark-up helps users of screen readers. [Learn more about summary and caption](https://dequeuniversity.com/rules/axe/4.10/table-duplicate-name).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "table-fake-caption": {"id": "table-fake-caption", "title": "Tables use `<caption>` instead of cells with the `[colspan]` attribute to indicate a caption.", "description": "Screen readers have features to make navigating tables easier. Ensuring that tables use the actual caption element instead of cells with the `[colspan]` attribute may improve the experience for screen reader users. [Learn more about captions](https://dequeuniversity.com/rules/axe/4.10/table-fake-caption).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "target-size": {"id": "target-size", "title": "Touch targets have sufficient size and spacing.", "description": "Touch targets with sufficient size and spacing help users who may have difficulty targeting small controls to activate the targets. [Learn more about touch targets](https://dequeuniversity.com/rules/axe/4.10/target-size).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "td-has-header": {"id": "td-has-header", "title": "`<td>` elements in a large `<table>` have one or more table headers.", "description": "Screen readers have features to make navigating tables easier. Ensuring that `<td>` elements in a large table (3 or more cells in width and height) have an associated table header may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/td-has-header).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "td-headers-attr": {"id": "td-headers-attr", "title": "Cells in a `<table>` element that use the `[headers]` attribute refer to table cells within the same table.", "description": "Screen readers have features to make navigating tables easier. Ensuring `<td>` cells using the `[headers]` attribute only refer to other cells in the same table may improve the experience for screen reader users. [Learn more about the `headers` attribute](https://dequeuniversity.com/rules/axe/4.10/td-headers-attr).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "th-has-data-cells": {"id": "th-has-data-cells", "title": "`<th>` elements and elements with `[role=\"columnheader\"/\"rowheader\"]` have data cells they describe.", "description": "Screen readers have features to make navigating tables easier. Ensuring table headers always refer to some set of cells may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/th-has-data-cells).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "valid-lang": {"id": "valid-lang", "title": "`[lang]` attributes have a valid value", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) on elements helps ensure that text is pronounced correctly by a screen reader. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/valid-lang).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "video-caption": {"id": "video-caption", "title": "`<video>` elements contain a `<track>` element with `[kind=\"captions\"]`", "description": "When a video provides a caption it is easier for deaf and hearing impaired users to access its information. [Learn more about video captions](https://dequeuniversity.com/rules/axe/4.10/video-caption).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "custom-controls-labels": {"id": "custom-controls-labels", "title": "Custom controls have associated labels", "description": "Custom interactive controls have associated labels, provided by aria-label or aria-labelledby. [Learn more about custom controls and labels](https://developer.chrome.com/docs/lighthouse/accessibility/custom-controls-labels/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "custom-controls-roles": {"id": "custom-controls-roles", "title": "Custom controls have ARIA roles", "description": "Custom interactive controls have appropriate ARIA roles. [Learn how to add roles to custom controls](https://developer.chrome.com/docs/lighthouse/accessibility/custom-control-roles/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "focus-traps": {"id": "focus-traps", "title": "User focus is not accidentally trapped in a region", "description": "A user can tab into and out of any control or region without accidentally trapping their focus. [Learn how to avoid focus traps](https://developer.chrome.com/docs/lighthouse/accessibility/focus-traps/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "focusable-controls": {"id": "focusable-controls", "title": "Interactive controls are keyboard focusable", "description": "Custom interactive controls are keyboard focusable and display a focus indicator. [Learn how to make custom controls focusable](https://developer.chrome.com/docs/lighthouse/accessibility/focusable-controls/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "interactive-element-affordance": {"id": "interactive-element-affordance", "title": "Interactive elements indicate their purpose and state", "description": "Interactive elements, such as links and buttons, should indicate their state and be distinguishable from non-interactive elements. [Learn how to decorate interactive elements with affordance hints](https://developer.chrome.com/docs/lighthouse/accessibility/interactive-element-affordance/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "logical-tab-order": {"id": "logical-tab-order", "title": "The page has a logical tab order", "description": "Tabbing through the page follows the visual layout. Users cannot focus elements that are offscreen. [Learn more about logical tab ordering](https://developer.chrome.com/docs/lighthouse/accessibility/logical-tab-order/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "managed-focus": {"id": "managed-focus", "title": "The user's focus is directed to new content added to the page", "description": "If new content, such as a dialog, is added to the page, the user's focus is directed to it. [Learn how to direct focus to new content](https://developer.chrome.com/docs/lighthouse/accessibility/managed-focus/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "offscreen-content-hidden": {"id": "offscreen-content-hidden", "title": "Offscreen content is hidden from assistive technology", "description": "Offscreen content is hidden with display: none or aria-hidden=true. [Learn how to properly hide offscreen content](https://developer.chrome.com/docs/lighthouse/accessibility/offscreen-content-hidden/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "use-landmarks": {"id": "use-landmarks", "title": "HTML5 landmark elements are used to improve navigation", "description": "Landmark elements (`<main>`, `<nav>`, etc.) are used to improve the keyboard navigation of the page for assistive technology. [Learn more about landmark elements](https://developer.chrome.com/docs/lighthouse/accessibility/use-landmarks/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "visual-order-follows-dom": {"id": "visual-order-follows-dom", "title": "Visual order on the page follows DOM order", "description": "DOM order matches the visual order, improving navigation for assistive technology. [Learn more about DOM and visual ordering](https://developer.chrome.com/docs/lighthouse/accessibility/visual-order-follows-dom/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "uses-long-cache-ttl": {"id": "uses-long-cache-ttl", "title": "Uses efficient cache policy on static assets", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "total-byte-weight": {"id": "total-byte-weight", "title": "Avoids enormous network payloads", "description": "Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1}, "offscreen-images": {"id": "offscreen-images", "title": "Defer offscreen images", "description": "Consider lazy-loading offscreen and hidden images after all critical resources have finished loading to lower time to interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "render-blocking-resources": {"id": "render-blocking-resources", "title": "Eliminate render-blocking resources", "description": "Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "unminified-css": {"id": "unminified-css", "title": "Minify CSS", "description": "Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "unminified-javascript": {"id": "unminified-javascript", "title": "Minify JavaScript", "description": "Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "unused-css-rules": {"id": "unused-css-rules", "title": "Reduce unused CSS", "description": "Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1}, "unused-javascript": {"id": "unused-javascript", "title": "Reduce unused JavaScript", "description": "Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1}, "modern-image-formats": {"id": "modern-image-formats", "title": "Serve images in next-gen formats", "description": "Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "uses-optimized-images": {"id": "uses-optimized-images", "title": "Efficiently encode images", "description": "Optimized images load faster and consume less cellular data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "uses-text-compression": {"id": "uses-text-compression", "title": "Enable text compression", "description": "Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "uses-responsive-images": {"id": "uses-responsive-images", "title": "Properly size images", "description": "Serve images that are appropriately-sized to save cellular data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "efficient-animated-content": {"id": "efficient-animated-content", "title": "Use video formats for animated content", "description": "Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "duplicated-javascript": {"id": "duplicated-javascript", "title": "Remove duplicate modules in JavaScript bundles", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. ", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "legacy-javascript": {"id": "legacy-javascript", "title": "Avoid serving legacy JavaScript to modern browsers", "description": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/baseline) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "doctype": {"id": "doctype", "title": "<PERSON> has the HTML doctype", "description": "Specifying a doctype prevents the browser from switching to quirks-mode. [Learn more about the doctype declaration](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "charset": {"id": "charset", "title": "<PERSON><PERSON><PERSON> defines charset", "description": "A character encoding declaration is required. It can be done with a `<meta>` tag in the first 1024 bytes of the HTML or in the Content-Type HTTP response header. [Learn more about declaring the character encoding](https://developer.chrome.com/docs/lighthouse/best-practices/charset/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "dom-size": {"id": "dom-size", "title": "Avoids an excessive DOM size", "description": "A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1}, "geolocation-on-start": {"id": "geolocation-on-start", "title": "Avoids requesting the geolocation permission on page load", "description": "Users are mistrustful of or confused by sites that request their location without context. Consider tying the request to a user action instead. [Learn more about the geolocation permission](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "inspector-issues": {"id": "inspector-issues", "title": "No issues in the `Issues` panel in Chrome Devtools", "description": "Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "no-document-write": {"id": "no-document-write", "title": "Avoids `document.write()`", "description": "For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "js-libraries": {"id": "js-libraries", "title": "Detected JavaScript libraries", "description": "All front-end JavaScript libraries detected on the page. [Learn more about this JavaScript library detection diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "notification-on-start": {"id": "notification-on-start", "title": "Avoids requesting the notification permission on page load", "description": "Users are mistrustful of or confused by sites that request to send notifications without context. Consider tying the request to user gestures instead. [Learn more about responsibly getting permission for notifications](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "paste-preventing-inputs": {"id": "paste-preventing-inputs", "title": "Allows users to paste into input fields", "description": "Preventing input pasting is a bad practice for the UX, and weakens security by blocking password managers.[Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "uses-http2": {"id": "uses-http2", "title": "Use HTTP/2", "description": "HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "uses-passive-event-listeners": {"id": "uses-passive-event-listeners", "title": "Uses passive listeners to improve scrolling performance", "description": "Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "meta-description": {"id": "meta-description", "title": "Document has a meta description", "description": "Meta descriptions may be included in search results to concisely summarize page content. [Learn more about the meta description](https://developer.chrome.com/docs/lighthouse/seo/meta-description/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "http-status-code": {"id": "http-status-code", "title": "Page has successful HTTP status code", "description": "Pages with unsuccessful HTTP status codes may not be indexed properly. [Learn more about HTTP status codes](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "font-size": {"id": "font-size", "title": "Document uses legible font sizes", "description": "Font sizes less than 12px are too small to be legible and require mobile visitors to “pinch to zoom” in order to read. Strive to have >60% of page text ≥12px. [Learn more about legible font sizes](https://developer.chrome.com/docs/lighthouse/seo/font-size/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "link-text": {"id": "link-text", "title": "Links have descriptive text", "description": "Descriptive link text helps search engines understand your content. [Learn how to make links more accessible](https://developer.chrome.com/docs/lighthouse/seo/link-text/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "crawlable-anchors": {"id": "crawlable-anchors", "title": "Links are crawlable", "description": "Search engines may use `href` attributes on links to crawl websites. Ensure that the `href` attribute of anchor elements links to an appropriate destination, so more pages of the site can be discovered. [Learn how to make links crawlable](https://support.google.com/webmasters/answer/9112205)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "is-crawlable": {"id": "is-crawlable", "title": "Page isn’t blocked from indexing", "description": "Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "robots-txt": {"id": "robots-txt", "title": "robots.txt is valid", "description": "If your robots.txt file is malformed, crawlers may not be able to understand how you want your website to be crawled or indexed. [Learn more about robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "hreflang": {"id": "hreflang", "title": "Document has a valid `hreflang`", "description": "hreflang links tell search engines what version of a page they should list in search results for a given language or region. [Learn more about `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "canonical": {"id": "canonical", "title": "Document has a valid `rel=canonical`", "description": "Canonical links suggest which URL to show in search results. [Learn more about canonical links](https://developer.chrome.com/docs/lighthouse/seo/canonical/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "structured-data": {"id": "structured-data", "title": "Structured data is valid", "description": "Run the [Structured Data Testing Tool](https://search.google.com/structured-data/testing-tool/) and the [Structured Data Linter](http://linter.structured-data.org/) to validate structured data. [Learn more about Structured Data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1"}, "bf-cache": {"id": "bf-cache", "title": "Page didn't prevent back/forward cache restoration", "description": "Many navigations are performed by going back to a previous page, or forwards again. The back/forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 4}, "cache-insight": {"id": "cache-insight", "title": "Use efficient cache lifetimes", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more](https://web.dev/uses-long-cache-ttl/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["uses-long-cache-ttl"]}, "cls-culprits-insight": {"id": "cls-culprits-insight", "title": "Layout shift culprits", "description": "Layout shifts occur when elements move absent any user interaction. [Investigate the causes of layout shifts](https://web.dev/articles/optimize-cls), such as elements being added, removed, or their fonts changing as the page loads.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["layout-shifts", "non-composited-animations", "unsized-images"]}, "document-latency-insight": {"id": "document-latency-insight", "title": "Document request latency", "description": "Your first network request is the most important.  Reduce its latency by avoiding redirects, ensuring a fast server response, and enabling text compression.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["redirects", "server-response-time", "uses-text-compression"]}, "dom-size-insight": {"id": "dom-size-insight", "title": "Optimize DOM size", "description": "A large DOM can increase the duration of style calculations and layout reflows, impacting page responsiveness. A large DOM will also increase memory usage. [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["dom-size"]}, "duplicated-javascript-insight": {"id": "duplicated-javascript-insight", "title": "Duplicated JavaScript", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2, "replacesAudits": ["duplicated-javascript"]}, "font-display-insight": {"id": "font-display-insight", "title": "Font display", "description": "Consider setting [font-display](https://developer.chrome.com/blog/font-display) to swap or optional to ensure text is consistently visible. swap can be further optimized to mitigate layout shifts with [font metric overrides](https://developer.chrome.com/blog/font-fallbacks).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["font-display"]}, "forced-reflow-insight": {"id": "forced-reflow-insight", "title": "Forced reflow", "description": "Many APIs, typically reading layout geometry, force the rendering engine to pause script execution in order to calculate the style and layout. Learn more about [forced reflow](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) and its mitigations.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "image-delivery-insight": {"id": "image-delivery-insight", "title": "Improve image delivery", "description": "Reducing the download time of images can improve the perceived load time of the page and LCP. [Learn more about optimizing image size](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["modern-image-formats", "uses-optimized-images", "efficient-animated-content", "uses-responsive-images"]}, "interaction-to-next-paint-insight": {"id": "interaction-to-next-paint-insight", "title": "INP by phase", "description": "Start investigating with the longest phase. [Delays can be minimized](https://web.dev/articles/optimize-inp#optimize_interactions). To reduce processing duration, [optimize the main-thread costs](https://web.dev/articles/optimize-long-tasks), often JS.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["work-during-interaction"]}, "lcp-discovery-insight": {"id": "lcp-discovery-insight", "title": "LCP request discovery", "description": "Optimize LCP by making the LCP image [discoverable](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) from the HTML immediately, and [avoiding lazy-loading](https://web.dev/articles/lcp-lazy-loading)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["prioritize-lcp-image", "lcp-lazy-loaded"]}, "lcp-phases-insight": {"id": "lcp-phases-insight", "title": "LCP by phase", "description": "Each [phase has specific improvement strategies](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideally, most of the LCP time should be spent on loading the resources, not within delays.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["largest-contentful-paint-element"]}, "legacy-javascript-insight": {"id": "legacy-javascript-insight", "title": "Legacy JavaScript", "description": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/articles/baseline-and-polyfills) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 2}, "modern-http-insight": {"id": "modern-http-insight", "title": "Modern HTTP", "description": "HTTP/2 and HTTP/3 offer many benefits over HTTP/1.1, such as multiplexing. [Learn more about using modern HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3}, "network-dependency-tree-insight": {"id": "network-dependency-tree-insight", "title": "Network dependency tree", "description": "[Avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) by reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 1, "replacesAudits": ["critical-request-chains"]}, "render-blocking-insight": {"id": "render-blocking-insight", "title": "Render blocking requests", "description": "Requests are blocking the page's initial render, which may delay LCP. [Deferring or inlining](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources/) can move these network requests out of the critical path.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["render-blocking-resources"]}, "third-parties-insight": {"id": "third-parties-insight", "title": "3rd parties", "description": "3rd party code can significantly impact load performance. [Reduce and defer loading of 3rd party code](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/) to prioritize your page's content.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["third-party-summary"]}, "viewport-insight": {"id": "viewport-insight", "title": "Optimize viewport for mobile", "description": "Tap interactions may be [delayed by up to 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) if the viewport is not optimized for mobile.", "score": null, "scoreDisplayMode": "error", "errorMessage": "Lighthouse was unable to reliably load the page you requested. Make sure you are testing the correct URL and that the server is properly responding to all requests. (Status code: 404)", "errorStack": "LighthouseError: ERRORED_DOCUMENT_REQUEST\n    at getNetworkError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:61:14)\n    at getPageLoadError (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/lib/navigation-error.js:162:24)\n    at _computeNavigationResult (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:146:7)\n    at async gatherFn (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:300:23)\n    at async Runner.gather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/runner.js:211:25)\n    at async navigationGather (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/gather/navigation-runner.js:306:21)\n    at async navigation (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/core/index.js:58:24)\n    at async runLighthouse (file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/run.js:210:26)\n    at async file:///Users/<USER>/.npm/_npx/0f94ee7615faf582/node_modules/lighthouse/cli/index.js:10:1", "guidanceLevel": 3, "replacesAudits": ["viewport"]}}, "configSettings": {"output": ["json"], "maxWaitForFcp": 30000, "maxWaitForLoad": 45000, "pauseAfterFcpMs": 1000, "pauseAfterLoadMs": 1000, "networkQuietThresholdMs": 1000, "cpuQuietThresholdMs": 1000, "formFactor": "mobile", "throttling": {"rttMs": 150, "throughputKbps": 1638.4, "requestLatencyMs": 562.5, "downloadThroughputKbps": 1474.5600000000002, "uploadThroughputKbps": 675, "cpuSlowdownMultiplier": 4}, "throttlingMethod": "simulate", "screenEmulation": {"mobile": true, "width": 412, "height": 823, "deviceScaleFactor": 1.75, "disabled": false}, "emulatedUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "auditMode": false, "gatherMode": false, "clearStorageTypes": ["file_systems", "shader_cache", "service_workers", "cache_storage"], "disableStorageReset": false, "debugNavigation": false, "channel": "cli", "usePassiveGathering": false, "disableFullPageScreenshot": false, "skipAboutBlank": false, "blankPage": "about:blank", "ignoreStatusCode": false, "locale": "en-US", "blockedUrlPatterns": null, "additionalTraceCategories": null, "extraHeaders": null, "precomputedLanternData": null, "onlyAudits": null, "onlyCategories": null, "skipAudits": null}, "categories": {"performance": {"title": "Performance", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "first-contentful-paint", "weight": 10, "group": "metrics", "acronym": "FCP"}, {"id": "largest-contentful-paint", "weight": 25, "group": "metrics", "acronym": "LCP"}, {"id": "total-blocking-time", "weight": 30, "group": "metrics", "acronym": "TBT"}, {"id": "cumulative-layout-shift", "weight": 25, "group": "metrics", "acronym": "CLS"}, {"id": "speed-index", "weight": 10, "group": "metrics", "acronym": "SI"}, {"id": "cache-insight", "weight": 0, "group": "hidden"}, {"id": "cls-culprits-insight", "weight": 0, "group": "hidden"}, {"id": "document-latency-insight", "weight": 0, "group": "hidden"}, {"id": "dom-size-insight", "weight": 0, "group": "hidden"}, {"id": "duplicated-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "font-display-insight", "weight": 0, "group": "hidden"}, {"id": "forced-reflow-insight", "weight": 0, "group": "hidden"}, {"id": "image-delivery-insight", "weight": 0, "group": "hidden"}, {"id": "interaction-to-next-paint-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-discovery-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-phases-insight", "weight": 0, "group": "hidden"}, {"id": "legacy-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "modern-http-insight", "weight": 0, "group": "hidden"}, {"id": "network-dependency-tree-insight", "weight": 0, "group": "hidden"}, {"id": "render-blocking-insight", "weight": 0, "group": "hidden"}, {"id": "third-parties-insight", "weight": 0, "group": "hidden"}, {"id": "viewport-insight", "weight": 0, "group": "hidden"}, {"id": "interactive", "weight": 0, "group": "hidden", "acronym": "TTI"}, {"id": "max-potential-fid", "weight": 0, "group": "hidden"}, {"id": "first-meaningful-paint", "weight": 0, "acronym": "FMP", "group": "hidden"}, {"id": "render-blocking-resources", "weight": 0, "group": "diagnostics"}, {"id": "uses-responsive-images", "weight": 0, "group": "diagnostics"}, {"id": "offscreen-images", "weight": 0, "group": "diagnostics"}, {"id": "unminified-css", "weight": 0, "group": "diagnostics"}, {"id": "unminified-javascript", "weight": 0, "group": "diagnostics"}, {"id": "unused-css-rules", "weight": 0, "group": "diagnostics"}, {"id": "unused-javascript", "weight": 0, "group": "diagnostics"}, {"id": "uses-optimized-images", "weight": 0, "group": "diagnostics"}, {"id": "modern-image-formats", "weight": 0, "group": "diagnostics"}, {"id": "uses-text-compression", "weight": 0, "group": "diagnostics"}, {"id": "uses-rel-preconnect", "weight": 0, "group": "diagnostics"}, {"id": "server-response-time", "weight": 0, "group": "diagnostics"}, {"id": "redirects", "weight": 0, "group": "diagnostics"}, {"id": "uses-http2", "weight": 0, "group": "diagnostics"}, {"id": "efficient-animated-content", "weight": 0, "group": "diagnostics"}, {"id": "duplicated-javascript", "weight": 0, "group": "diagnostics"}, {"id": "legacy-javascript", "weight": 0, "group": "diagnostics"}, {"id": "prioritize-lcp-image", "weight": 0, "group": "diagnostics"}, {"id": "total-byte-weight", "weight": 0, "group": "diagnostics"}, {"id": "uses-long-cache-ttl", "weight": 0, "group": "diagnostics"}, {"id": "dom-size", "weight": 0, "group": "diagnostics"}, {"id": "critical-request-chains", "weight": 0, "group": "diagnostics"}, {"id": "user-timings", "weight": 0, "group": "diagnostics"}, {"id": "bootup-time", "weight": 0, "group": "diagnostics"}, {"id": "mainthread-work-breakdown", "weight": 0, "group": "diagnostics"}, {"id": "font-display", "weight": 0, "group": "diagnostics"}, {"id": "third-party-summary", "weight": 0, "group": "diagnostics"}, {"id": "third-party-facades", "weight": 0, "group": "diagnostics"}, {"id": "largest-contentful-paint-element", "weight": 0, "group": "diagnostics"}, {"id": "lcp-lazy-loaded", "weight": 0, "group": "diagnostics"}, {"id": "layout-shifts", "weight": 0, "group": "diagnostics"}, {"id": "uses-passive-event-listeners", "weight": 0, "group": "diagnostics"}, {"id": "no-document-write", "weight": 0, "group": "diagnostics"}, {"id": "long-tasks", "weight": 0, "group": "diagnostics"}, {"id": "non-composited-animations", "weight": 0, "group": "diagnostics"}, {"id": "unsized-images", "weight": 0, "group": "diagnostics"}, {"id": "viewport", "weight": 0, "group": "diagnostics"}, {"id": "bf-cache", "weight": 0, "group": "diagnostics"}, {"id": "network-requests", "weight": 0, "group": "hidden"}, {"id": "network-rtt", "weight": 0, "group": "hidden"}, {"id": "network-server-latency", "weight": 0, "group": "hidden"}, {"id": "main-thread-tasks", "weight": 0, "group": "hidden"}, {"id": "diagnostics", "weight": 0, "group": "hidden"}, {"id": "metrics", "weight": 0, "group": "hidden"}, {"id": "screenshot-thumbnails", "weight": 0, "group": "hidden"}, {"id": "final-screenshot", "weight": 0, "group": "hidden"}, {"id": "script-treemap-data", "weight": 0, "group": "hidden"}, {"id": "resource-summary", "weight": 0, "group": "hidden"}], "id": "performance", "score": null}, "accessibility": {"title": "Accessibility", "description": "These checks highlight opportunities to [improve the accessibility of your web app](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatic detection can only detect a subset of issues and does not guarantee the accessibility of your web app, so [manual testing](https://web.dev/articles/how-to-review) is also encouraged.", "manualDescription": "These items address areas which an automated testing tool cannot cover. Learn more in our guide on [conducting an accessibility review](https://web.dev/articles/how-to-review).", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "accesskeys", "weight": 7, "group": "a11y-navigation"}, {"id": "aria-allowed-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-allowed-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-command-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-conditional-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-deprecated-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-dialog-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-hidden-body", "weight": 10, "group": "a11y-aria"}, {"id": "aria-hidden-focus", "weight": 7, "group": "a11y-aria"}, {"id": "aria-input-field-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-meter-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-progressbar-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-prohibited-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-required-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-required-children", "weight": 10, "group": "a11y-aria"}, {"id": "aria-required-parent", "weight": 10, "group": "a11y-aria"}, {"id": "aria-roles", "weight": 7, "group": "a11y-aria"}, {"id": "aria-text", "weight": 7, "group": "a11y-aria"}, {"id": "aria-toggle-field-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-tooltip-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-treeitem-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-valid-attr-value", "weight": 10, "group": "a11y-aria"}, {"id": "aria-valid-attr", "weight": 10, "group": "a11y-aria"}, {"id": "button-name", "weight": 10, "group": "a11y-names-labels"}, {"id": "bypass", "weight": 7, "group": "a11y-navigation"}, {"id": "color-contrast", "weight": 7, "group": "a11y-color-contrast"}, {"id": "definition-list", "weight": 7, "group": "a11y-tables-lists"}, {"id": "dlitem", "weight": 7, "group": "a11y-tables-lists"}, {"id": "document-title", "weight": 7, "group": "a11y-names-labels"}, {"id": "duplicate-id-aria", "weight": 10, "group": "a11y-aria"}, {"id": "form-field-multiple-labels", "weight": 3, "group": "a11y-names-labels"}, {"id": "frame-title", "weight": 7, "group": "a11y-names-labels"}, {"id": "heading-order", "weight": 3, "group": "a11y-navigation"}, {"id": "html-has-lang", "weight": 7, "group": "a11y-language"}, {"id": "html-lang-valid", "weight": 7, "group": "a11y-language"}, {"id": "html-xml-lang-mismatch", "weight": 3, "group": "a11y-language"}, {"id": "image-alt", "weight": 10, "group": "a11y-names-labels"}, {"id": "image-redundant-alt", "weight": 1, "group": "a11y-names-labels"}, {"id": "input-button-name", "weight": 10, "group": "a11y-names-labels"}, {"id": "input-image-alt", "weight": 10, "group": "a11y-names-labels"}, {"id": "label", "weight": 7, "group": "a11y-names-labels"}, {"id": "link-in-text-block", "weight": 7, "group": "a11y-color-contrast"}, {"id": "link-name", "weight": 7, "group": "a11y-names-labels"}, {"id": "list", "weight": 7, "group": "a11y-tables-lists"}, {"id": "listitem", "weight": 7, "group": "a11y-tables-lists"}, {"id": "meta-refresh", "weight": 10, "group": "a11y-best-practices"}, {"id": "meta-viewport", "weight": 10, "group": "a11y-best-practices"}, {"id": "object-alt", "weight": 7, "group": "a11y-names-labels"}, {"id": "select-name", "weight": 7, "group": "a11y-names-labels"}, {"id": "skip-link", "weight": 3, "group": "a11y-names-labels"}, {"id": "tabindex", "weight": 7, "group": "a11y-navigation"}, {"id": "table-duplicate-name", "weight": 1, "group": "a11y-tables-lists"}, {"id": "target-size", "weight": 7, "group": "a11y-best-practices"}, {"id": "td-headers-attr", "weight": 7, "group": "a11y-tables-lists"}, {"id": "th-has-data-cells", "weight": 7, "group": "a11y-tables-lists"}, {"id": "valid-lang", "weight": 7, "group": "a11y-language"}, {"id": "video-caption", "weight": 10, "group": "a11y-audio-video"}, {"id": "focusable-controls", "weight": 0}, {"id": "interactive-element-affordance", "weight": 0}, {"id": "logical-tab-order", "weight": 0}, {"id": "visual-order-follows-dom", "weight": 0}, {"id": "focus-traps", "weight": 0}, {"id": "managed-focus", "weight": 0}, {"id": "use-landmarks", "weight": 0}, {"id": "offscreen-content-hidden", "weight": 0}, {"id": "custom-controls-labels", "weight": 0}, {"id": "custom-controls-roles", "weight": 0}, {"id": "empty-heading", "weight": 0, "group": "hidden"}, {"id": "identical-links-same-purpose", "weight": 0, "group": "hidden"}, {"id": "landmark-one-main", "weight": 0, "group": "hidden"}, {"id": "label-content-name-mismatch", "weight": 0, "group": "hidden"}, {"id": "table-fake-caption", "weight": 0, "group": "hidden"}, {"id": "td-has-header", "weight": 0, "group": "hidden"}], "id": "accessibility", "score": null}, "best-practices": {"title": "Best Practices", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "is-on-https", "weight": 5, "group": "best-practices-trust-safety"}, {"id": "redirects-http", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "geolocation-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "notification-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "csp-xss", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "has-hsts", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "origin-isolation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "clickjacking-mitigation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "paste-preventing-inputs", "weight": 3, "group": "best-practices-ux"}, {"id": "image-aspect-ratio", "weight": 1, "group": "best-practices-ux"}, {"id": "image-size-responsive", "weight": 1, "group": "best-practices-ux"}, {"id": "viewport", "weight": 1, "group": "best-practices-ux"}, {"id": "font-size", "weight": 1, "group": "best-practices-ux"}, {"id": "doctype", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "charset", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "js-libraries", "weight": 0, "group": "best-practices-general"}, {"id": "deprecations", "weight": 5, "group": "best-practices-general"}, {"id": "third-party-cookies", "weight": 5, "group": "best-practices-general"}, {"id": "errors-in-console", "weight": 1, "group": "best-practices-general"}, {"id": "valid-source-maps", "weight": 0, "group": "best-practices-general"}, {"id": "inspector-issues", "weight": 1, "group": "best-practices-general"}], "id": "best-practices", "score": null}, "seo": {"title": "SEO", "description": "These checks ensure that your page is following basic search engine optimization advice. There are many additional factors Lighthouse does not score here that may affect your search ranking, including performance on [Core Web Vitals](https://web.dev/explore/vitals). [Learn more about Google Search Essentials](https://support.google.com/webmasters/answer/35769).", "manualDescription": "Run these additional validators on your site to check additional SEO best practices.", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "is-crawlable", "weight": 4.043478260869565, "group": "seo-crawl"}, {"id": "document-title", "weight": 1, "group": "seo-content"}, {"id": "meta-description", "weight": 1, "group": "seo-content"}, {"id": "http-status-code", "weight": 1, "group": "seo-crawl"}, {"id": "link-text", "weight": 1, "group": "seo-content"}, {"id": "crawlable-anchors", "weight": 1, "group": "seo-crawl"}, {"id": "robots-txt", "weight": 1, "group": "seo-crawl"}, {"id": "image-alt", "weight": 1, "group": "seo-content"}, {"id": "hreflang", "weight": 1, "group": "seo-content"}, {"id": "canonical", "weight": 1, "group": "seo-content"}, {"id": "structured-data", "weight": 0}], "id": "seo", "score": null}}, "categoryGroups": {"metrics": {"title": "Metrics"}, "insights": {"title": "Insights", "description": "These insights are also available in the Chrome DevTools Performance Panel - [record a trace](https://developer.chrome.com/docs/devtools/performance/reference) to view more detailed information."}, "diagnostics": {"title": "Diagnostics", "description": "More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the Performance score."}, "a11y-best-practices": {"title": "Best practices", "description": "These items highlight common accessibility best practices."}, "a11y-color-contrast": {"title": "Contrast", "description": "These are opportunities to improve the legibility of your content."}, "a11y-names-labels": {"title": "Names and labels", "description": "These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-navigation": {"title": "Navigation", "description": "These are opportunities to improve keyboard navigation in your application."}, "a11y-aria": {"title": "ARIA", "description": "These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-language": {"title": "Internationalization and localization", "description": "These are opportunities to improve the interpretation of your content by users in different locales."}, "a11y-audio-video": {"title": "Audio and video", "description": "These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."}, "a11y-tables-lists": {"title": "Tables and lists", "description": "These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."}, "seo-mobile": {"title": "Mobile Friendly", "description": "Make sure your pages are mobile friendly so users don’t have to pinch or zoom in order to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."}, "seo-content": {"title": "Content Best Practices", "description": "Format your HTML in a way that enables crawlers to better understand your app’s content."}, "seo-crawl": {"title": "Crawling and Indexing", "description": "To appear in search results, crawlers need access to your app."}, "best-practices-trust-safety": {"title": "Trust and Safety"}, "best-practices-ux": {"title": "User Experience"}, "best-practices-browser-compat": {"title": "Browser Compatibility"}, "best-practices-general": {"title": "General"}, "hidden": {"title": ""}}, "stackPacks": [], "timing": {"entries": [{"startTime": 28901.55, "name": "lh:config", "duration": 900.97, "entryType": "measure"}, {"startTime": 28917.83, "name": "lh:config:resolveArtifactsToDefns", "duration": 144.3, "entryType": "measure"}, {"startTime": 29802.65, "name": "lh:runner:gather", "duration": 5466.89, "entryType": "measure"}, {"startTime": 30345.55, "name": "lh:driver:connect", "duration": 8.76, "entryType": "measure"}, {"startTime": 30354.49, "name": "lh:driver:navigate", "duration": 60.95, "entryType": "measure"}, {"startTime": 30416.01, "name": "lh:gather:getBenchmarkIndex", "duration": 1010.9, "entryType": "measure"}, {"startTime": 31427.03, "name": "lh:gather:getVersion", "duration": 0.6, "entryType": "measure"}, {"startTime": 31436.81, "name": "lh:prepare:navigationMode", "duration": 66.88, "entryType": "measure"}, {"startTime": 31461.46, "name": "lh:storage:clearDataForOrigin", "duration": 29.55, "entryType": "measure"}, {"startTime": 31491.12, "name": "lh:storage:clearBrowserCaches", "duration": 9.47, "entryType": "measure"}, {"startTime": 31502.91, "name": "lh:gather:prepareThrottlingAndNetwork", "duration": 0.68, "entryType": "measure"}, {"startTime": 31571.36, "name": "lh:driver:navigate", "duration": 3571.96, "entryType": "measure"}, {"startTime": 35234.45, "name": "lh:computed:NetworkRecords", "duration": 0.71, "entryType": "measure"}, {"startTime": 35269.77, "name": "lh:runner:audit", "duration": 107.56, "entryType": "measure"}, {"startTime": 35269.84, "name": "lh:runner:auditing", "duration": 107.03, "entryType": "measure"}, {"startTime": 35270.98, "name": "lh:audit:is-on-https", "duration": 0.79, "entryType": "measure"}, {"startTime": 35271.91, "name": "lh:audit:redirects-http", "duration": 0.51, "entryType": "measure"}, {"startTime": 35272.61, "name": "lh:audit:viewport", "duration": 0.45, "entryType": "measure"}, {"startTime": 35273.23, "name": "lh:audit:first-contentful-paint", "duration": 0.35, "entryType": "measure"}, {"startTime": 35273.71, "name": "lh:audit:largest-contentful-paint", "duration": 0.37, "entryType": "measure"}, {"startTime": 35274.22, "name": "lh:audit:first-meaningful-paint", "duration": 0.35, "entryType": "measure"}, {"startTime": 35274.71, "name": "lh:audit:speed-index", "duration": 0.51, "entryType": "measure"}, {"startTime": 35275.25, "name": "lh:audit:screenshot-thumbnails", "duration": 0.05, "entryType": "measure"}, {"startTime": 35275.32, "name": "lh:audit:final-screenshot", "duration": 0.14, "entryType": "measure"}, {"startTime": 35275.78, "name": "lh:audit:total-blocking-time", "duration": 0.4, "entryType": "measure"}, {"startTime": 35276.33, "name": "lh:audit:max-potential-fid", "duration": 0.48, "entryType": "measure"}, {"startTime": 35276.99, "name": "lh:audit:cumulative-layout-shift", "duration": 0.37, "entryType": "measure"}, {"startTime": 35277.54, "name": "lh:audit:errors-in-console", "duration": 0.68, "entryType": "measure"}, {"startTime": 35278.4, "name": "lh:audit:server-response-time", "duration": 0.47, "entryType": "measure"}, {"startTime": 35279.03, "name": "lh:audit:interactive", "duration": 0.35, "entryType": "measure"}, {"startTime": 35279.54, "name": "lh:audit:user-timings", "duration": 0.62, "entryType": "measure"}, {"startTime": 35288.85, "name": "lh:audit:critical-request-chains", "duration": 0.45, "entryType": "measure"}, {"startTime": 35289.5, "name": "lh:audit:redirects", "duration": 0.3, "entryType": "measure"}, {"startTime": 35289.94, "name": "lh:audit:image-aspect-ratio", "duration": 0.4, "entryType": "measure"}, {"startTime": 35290.49, "name": "lh:audit:image-size-responsive", "duration": 0.41, "entryType": "measure"}, {"startTime": 35291.03, "name": "lh:audit:deprecations", "duration": 0.41, "entryType": "measure"}, {"startTime": 35291.56, "name": "lh:audit:third-party-cookies", "duration": 0.39, "entryType": "measure"}, {"startTime": 35292.09, "name": "lh:audit:mainthread-work-breakdown", "duration": 0.39, "entryType": "measure"}, {"startTime": 35292.62, "name": "lh:audit:bootup-time", "duration": 0.4, "entryType": "measure"}, {"startTime": 35293.14, "name": "lh:audit:uses-rel-preconnect", "duration": 0.29, "entryType": "measure"}, {"startTime": 35293.57, "name": "lh:audit:font-display", "duration": 0.44, "entryType": "measure"}, {"startTime": 35294.03, "name": "lh:audit:diagnostics", "duration": 0.06, "entryType": "measure"}, {"startTime": 35294.1, "name": "lh:audit:network-requests", "duration": 0.03, "entryType": "measure"}, {"startTime": 35294.25, "name": "lh:audit:network-rtt", "duration": 0.36, "entryType": "measure"}, {"startTime": 35294.73, "name": "lh:audit:network-server-latency", "duration": 0.32, "entryType": "measure"}, {"startTime": 35295.07, "name": "lh:audit:main-thread-tasks", "duration": 0.03, "entryType": "measure"}, {"startTime": 35295.11, "name": "lh:audit:metrics", "duration": 0.03, "entryType": "measure"}, {"startTime": 35295.15, "name": "lh:audit:resource-summary", "duration": 0.03, "entryType": "measure"}, {"startTime": 35295.34, "name": "lh:audit:third-party-summary", "duration": 0.41, "entryType": "measure"}, {"startTime": 35295.91, "name": "lh:audit:third-party-facades", "duration": 0.4, "entryType": "measure"}, {"startTime": 35296.42, "name": "lh:audit:largest-contentful-paint-element", "duration": 0.28, "entryType": "measure"}, {"startTime": 35296.85, "name": "lh:audit:lcp-lazy-loaded", "duration": 0.39, "entryType": "measure"}, {"startTime": 35297.38, "name": "lh:audit:layout-shifts", "duration": 0.31, "entryType": "measure"}, {"startTime": 35297.8, "name": "lh:audit:long-tasks", "duration": 0.29, "entryType": "measure"}, {"startTime": 35298.19, "name": "lh:audit:non-composited-animations", "duration": 0.31, "entryType": "measure"}, {"startTime": 35298.65, "name": "lh:audit:unsized-images", "duration": 0.55, "entryType": "measure"}, {"startTime": 35299.66, "name": "lh:audit:valid-source-maps", "duration": 0.45, "entryType": "measure"}, {"startTime": 35300.22, "name": "lh:audit:prioritize-lcp-image", "duration": 0.35, "entryType": "measure"}, {"startTime": 35300.71, "name": "lh:audit:csp-xss", "duration": 0.41, "entryType": "measure"}, {"startTime": 35301.23, "name": "lh:audit:has-hsts", "duration": 0.31, "entryType": "measure"}, {"startTime": 35301.64, "name": "lh:audit:origin-isolation", "duration": 0.27, "entryType": "measure"}, {"startTime": 35302.02, "name": "lh:audit:clickjacking-mitigation", "duration": 0.3, "entryType": "measure"}, {"startTime": 35302.34, "name": "lh:audit:script-treemap-data", "duration": 0.03, "entryType": "measure"}, {"startTime": 35302.53, "name": "lh:audit:accesskeys", "duration": 0.38, "entryType": "measure"}, {"startTime": 35303.04, "name": "lh:audit:aria-allowed-attr", "duration": 0.38, "entryType": "measure"}, {"startTime": 35303.55, "name": "lh:audit:aria-allowed-role", "duration": 1.08, "entryType": "measure"}, {"startTime": 35304.87, "name": "lh:audit:aria-command-name", "duration": 1.77, "entryType": "measure"}, {"startTime": 35307.01, "name": "lh:audit:aria-conditional-attr", "duration": 1.15, "entryType": "measure"}, {"startTime": 35308.58, "name": "lh:audit:aria-deprecated-role", "duration": 0.92, "entryType": "measure"}, {"startTime": 35309.65, "name": "lh:audit:aria-dialog-name", "duration": 1.07, "entryType": "measure"}, {"startTime": 35310.86, "name": "lh:audit:aria-hidden-body", "duration": 0.62, "entryType": "measure"}, {"startTime": 35311.64, "name": "lh:audit:aria-hidden-focus", "duration": 0.44, "entryType": "measure"}, {"startTime": 35312.27, "name": "lh:audit:aria-input-field-name", "duration": 0.49, "entryType": "measure"}, {"startTime": 35312.91, "name": "lh:audit:aria-meter-name", "duration": 0.43, "entryType": "measure"}, {"startTime": 35313.49, "name": "lh:audit:aria-progressbar-name", "duration": 0.5, "entryType": "measure"}, {"startTime": 35314.14, "name": "lh:audit:aria-prohibited-attr", "duration": 0.43, "entryType": "measure"}, {"startTime": 35314.72, "name": "lh:audit:aria-required-attr", "duration": 3.24, "entryType": "measure"}, {"startTime": 35318.15, "name": "lh:audit:aria-required-children", "duration": 0.4, "entryType": "measure"}, {"startTime": 35318.65, "name": "lh:audit:aria-required-parent", "duration": 0.41, "entryType": "measure"}, {"startTime": 35319.17, "name": "lh:audit:aria-roles", "duration": 0.33, "entryType": "measure"}, {"startTime": 35319.62, "name": "lh:audit:aria-text", "duration": 0.34, "entryType": "measure"}, {"startTime": 35320.08, "name": "lh:audit:aria-toggle-field-name", "duration": 0.33, "entryType": "measure"}, {"startTime": 35320.53, "name": "lh:audit:aria-tooltip-name", "duration": 0.33, "entryType": "measure"}, {"startTime": 35320.98, "name": "lh:audit:aria-treeitem-name", "duration": 0.33, "entryType": "measure"}, {"startTime": 35321.43, "name": "lh:audit:aria-valid-attr-value", "duration": 0.32, "entryType": "measure"}, {"startTime": 35321.87, "name": "lh:audit:aria-valid-attr", "duration": 0.33, "entryType": "measure"}, {"startTime": 35322.31, "name": "lh:audit:button-name", "duration": 0.32, "entryType": "measure"}, {"startTime": 35322.75, "name": "lh:audit:bypass", "duration": 0.32, "entryType": "measure"}, {"startTime": 35323.19, "name": "lh:audit:color-contrast", "duration": 0.32, "entryType": "measure"}, {"startTime": 35323.63, "name": "lh:audit:definition-list", "duration": 0.65, "entryType": "measure"}, {"startTime": 35324.41, "name": "lh:audit:dlitem", "duration": 0.61, "entryType": "measure"}, {"startTime": 35325.16, "name": "lh:audit:document-title", "duration": 0.39, "entryType": "measure"}, {"startTime": 35325.66, "name": "lh:audit:duplicate-id-aria", "duration": 0.34, "entryType": "measure"}, {"startTime": 35326.12, "name": "lh:audit:empty-heading", "duration": 0.36, "entryType": "measure"}, {"startTime": 35326.59, "name": "lh:audit:form-field-multiple-labels", "duration": 0.33, "entryType": "measure"}, {"startTime": 35327.04, "name": "lh:audit:frame-title", "duration": 0.33, "entryType": "measure"}, {"startTime": 35327.49, "name": "lh:audit:heading-order", "duration": 0.33, "entryType": "measure"}, {"startTime": 35327.94, "name": "lh:audit:html-has-lang", "duration": 0.39, "entryType": "measure"}, {"startTime": 35328.47, "name": "lh:audit:html-lang-valid", "duration": 0.55, "entryType": "measure"}, {"startTime": 35329.14, "name": "lh:audit:html-xml-lang-mismatch", "duration": 0.49, "entryType": "measure"}, {"startTime": 35329.74, "name": "lh:audit:identical-links-same-purpose", "duration": 0.33, "entryType": "measure"}, {"startTime": 35330.18, "name": "lh:audit:image-alt", "duration": 0.34, "entryType": "measure"}, {"startTime": 35330.65, "name": "lh:audit:image-redundant-alt", "duration": 0.6, "entryType": "measure"}, {"startTime": 35331.38, "name": "lh:audit:input-button-name", "duration": 0.49, "entryType": "measure"}, {"startTime": 35332.01, "name": "lh:audit:input-image-alt", "duration": 0.35, "entryType": "measure"}, {"startTime": 35332.47, "name": "lh:audit:label-content-name-mismatch", "duration": 0.35, "entryType": "measure"}, {"startTime": 35332.94, "name": "lh:audit:label", "duration": 0.73, "entryType": "measure"}, {"startTime": 35333.8, "name": "lh:audit:landmark-one-main", "duration": 0.47, "entryType": "measure"}, {"startTime": 35334.37, "name": "lh:audit:link-name", "duration": 0.34, "entryType": "measure"}, {"startTime": 35334.83, "name": "lh:audit:link-in-text-block", "duration": 0.41, "entryType": "measure"}, {"startTime": 35335.38, "name": "lh:audit:list", "duration": 0.38, "entryType": "measure"}, {"startTime": 35335.89, "name": "lh:audit:listitem", "duration": 0.36, "entryType": "measure"}, {"startTime": 35339.55, "name": "lh:audit:meta-refresh", "duration": 0.53, "entryType": "measure"}, {"startTime": 35340.22, "name": "lh:audit:meta-viewport", "duration": 0.36, "entryType": "measure"}, {"startTime": 35340.7, "name": "lh:audit:object-alt", "duration": 0.31, "entryType": "measure"}, {"startTime": 35341.12, "name": "lh:audit:select-name", "duration": 0.3, "entryType": "measure"}, {"startTime": 35341.52, "name": "lh:audit:skip-link", "duration": 0.28, "entryType": "measure"}, {"startTime": 35341.93, "name": "lh:audit:tabindex", "duration": 0.3, "entryType": "measure"}, {"startTime": 35342.34, "name": "lh:audit:table-duplicate-name", "duration": 0.31, "entryType": "measure"}, {"startTime": 35342.76, "name": "lh:audit:table-fake-caption", "duration": 0.33, "entryType": "measure"}, {"startTime": 35343.19, "name": "lh:audit:target-size", "duration": 0.3, "entryType": "measure"}, {"startTime": 35343.61, "name": "lh:audit:td-has-header", "duration": 0.32, "entryType": "measure"}, {"startTime": 35344.03, "name": "lh:audit:td-headers-attr", "duration": 0.33, "entryType": "measure"}, {"startTime": 35344.47, "name": "lh:audit:th-has-data-cells", "duration": 0.32, "entryType": "measure"}, {"startTime": 35344.9, "name": "lh:audit:valid-lang", "duration": 0.3, "entryType": "measure"}, {"startTime": 35345.31, "name": "lh:audit:video-caption", "duration": 0.3, "entryType": "measure"}, {"startTime": 35345.63, "name": "lh:audit:custom-controls-labels", "duration": 0.03, "entryType": "measure"}, {"startTime": 35345.66, "name": "lh:audit:custom-controls-roles", "duration": 0.02, "entryType": "measure"}, {"startTime": 35345.69, "name": "lh:audit:focus-traps", "duration": 0.02, "entryType": "measure"}, {"startTime": 35345.71, "name": "lh:audit:focusable-controls", "duration": 0.02, "entryType": "measure"}, {"startTime": 35345.74, "name": "lh:audit:interactive-element-affordance", "duration": 0.02, "entryType": "measure"}, {"startTime": 35345.76, "name": "lh:audit:logical-tab-order", "duration": 0.02, "entryType": "measure"}, {"startTime": 35345.79, "name": "lh:audit:managed-focus", "duration": 0.02, "entryType": "measure"}, {"startTime": 35345.81, "name": "lh:audit:offscreen-content-hidden", "duration": 0.02, "entryType": "measure"}, {"startTime": 35345.83, "name": "lh:audit:use-landmarks", "duration": 0.02, "entryType": "measure"}, {"startTime": 35345.86, "name": "lh:audit:visual-order-follows-dom", "duration": 0.02, "entryType": "measure"}, {"startTime": 35345.99, "name": "lh:audit:uses-long-cache-ttl", "duration": 0.29, "entryType": "measure"}, {"startTime": 35346.4, "name": "lh:audit:total-byte-weight", "duration": 0.29, "entryType": "measure"}, {"startTime": 35346.77, "name": "lh:audit:offscreen-images", "duration": 0.22, "entryType": "measure"}, {"startTime": 35347.08, "name": "lh:audit:render-blocking-resources", "duration": 0.23, "entryType": "measure"}, {"startTime": 35347.38, "name": "lh:audit:unminified-css", "duration": 0.2, "entryType": "measure"}, {"startTime": 35347.68, "name": "lh:audit:unminified-javascript", "duration": 0.25, "entryType": "measure"}, {"startTime": 35348.03, "name": "lh:audit:unused-css-rules", "duration": 0.26, "entryType": "measure"}, {"startTime": 35348.38, "name": "lh:audit:unused-javascript", "duration": 0.22, "entryType": "measure"}, {"startTime": 35348.68, "name": "lh:audit:modern-image-formats", "duration": 0.26, "entryType": "measure"}, {"startTime": 35349.04, "name": "lh:audit:uses-optimized-images", "duration": 0.27, "entryType": "measure"}, {"startTime": 35349.39, "name": "lh:audit:uses-text-compression", "duration": 0.35, "entryType": "measure"}, {"startTime": 35349.84, "name": "lh:audit:uses-responsive-images", "duration": 0.23, "entryType": "measure"}, {"startTime": 35350.15, "name": "lh:audit:efficient-animated-content", "duration": 0.23, "entryType": "measure"}, {"startTime": 35350.46, "name": "lh:audit:duplicated-javascript", "duration": 0.2, "entryType": "measure"}, {"startTime": 35350.78, "name": "lh:audit:legacy-javascript", "duration": 0.25, "entryType": "measure"}, {"startTime": 35351.15, "name": "lh:audit:doctype", "duration": 0.3, "entryType": "measure"}, {"startTime": 35357.6, "name": "lh:audit:charset", "duration": 0.38, "entryType": "measure"}, {"startTime": 35358.23, "name": "lh:audit:dom-size", "duration": 0.35, "entryType": "measure"}, {"startTime": 35358.76, "name": "lh:audit:geolocation-on-start", "duration": 0.31, "entryType": "measure"}, {"startTime": 35359.2, "name": "lh:audit:inspector-issues", "duration": 0.31, "entryType": "measure"}, {"startTime": 35359.62, "name": "lh:audit:no-document-write", "duration": 0.31, "entryType": "measure"}, {"startTime": 35360.07, "name": "lh:audit:js-libraries", "duration": 0.25, "entryType": "measure"}, {"startTime": 35360.43, "name": "lh:audit:notification-on-start", "duration": 0.33, "entryType": "measure"}, {"startTime": 35360.96, "name": "lh:audit:paste-preventing-inputs", "duration": 0.3, "entryType": "measure"}, {"startTime": 35361.34, "name": "lh:audit:uses-http2", "duration": 0.21, "entryType": "measure"}, {"startTime": 35361.65, "name": "lh:audit:uses-passive-event-listeners", "duration": 0.31, "entryType": "measure"}, {"startTime": 35362.08, "name": "lh:audit:meta-description", "duration": 0.29, "entryType": "measure"}, {"startTime": 35362.47, "name": "lh:audit:http-status-code", "duration": 0.36, "entryType": "measure"}, {"startTime": 35362.97, "name": "lh:audit:font-size", "duration": 0.34, "entryType": "measure"}, {"startTime": 35363.43, "name": "lh:audit:link-text", "duration": 0.32, "entryType": "measure"}, {"startTime": 35363.85, "name": "lh:audit:crawlable-anchors", "duration": 0.49, "entryType": "measure"}, {"startTime": 35364.5, "name": "lh:audit:is-crawlable", "duration": 0.33, "entryType": "measure"}, {"startTime": 35364.93, "name": "lh:audit:robots-txt", "duration": 0.29, "entryType": "measure"}, {"startTime": 35365.33, "name": "lh:audit:hreflang", "duration": 0.32, "entryType": "measure"}, {"startTime": 35365.78, "name": "lh:audit:canonical", "duration": 0.29, "entryType": "measure"}, {"startTime": 35366.17, "name": "lh:audit:structured-data", "duration": 0.23, "entryType": "measure"}, {"startTime": 35366.53, "name": "lh:audit:bf-cache", "duration": 0.3, "entryType": "measure"}, {"startTime": 35366.95, "name": "lh:audit:cache-insight", "duration": 0.29, "entryType": "measure"}, {"startTime": 35367.36, "name": "lh:audit:cls-culprits-insight", "duration": 0.38, "entryType": "measure"}, {"startTime": 35367.86, "name": "lh:audit:document-latency-insight", "duration": 0.3, "entryType": "measure"}, {"startTime": 35368.27, "name": "lh:audit:dom-size-insight", "duration": 0.3, "entryType": "measure"}, {"startTime": 35368.67, "name": "lh:audit:duplicated-javascript-insight", "duration": 0.28, "entryType": "measure"}, {"startTime": 35369.05, "name": "lh:audit:font-display-insight", "duration": 0.3, "entryType": "measure"}, {"startTime": 35369.46, "name": "lh:audit:forced-reflow-insight", "duration": 0.38, "entryType": "measure"}, {"startTime": 35369.94, "name": "lh:audit:image-delivery-insight", "duration": 0.29, "entryType": "measure"}, {"startTime": 35370.32, "name": "lh:audit:interaction-to-next-paint-insight", "duration": 0.29, "entryType": "measure"}, {"startTime": 35370.71, "name": "lh:audit:lcp-discovery-insight", "duration": 0.28, "entryType": "measure"}, {"startTime": 35371.09, "name": "lh:audit:lcp-phases-insight", "duration": 0.28, "entryType": "measure"}, {"startTime": 35371.48, "name": "lh:audit:legacy-javascript-insight", "duration": 0.32, "entryType": "measure"}, {"startTime": 35374.88, "name": "lh:audit:modern-http-insight", "duration": 0.33, "entryType": "measure"}, {"startTime": 35375.33, "name": "lh:audit:network-dependency-tree-insight", "duration": 0.29, "entryType": "measure"}, {"startTime": 35375.73, "name": "lh:audit:render-blocking-insight", "duration": 0.29, "entryType": "measure"}, {"startTime": 35376.14, "name": "lh:audit:third-parties-insight", "duration": 0.34, "entryType": "measure"}, {"startTime": 35376.58, "name": "lh:audit:viewport-insight", "duration": 0.28, "entryType": "measure"}, {"startTime": 35376.87, "name": "lh:runner:generate", "duration": 0.45, "entryType": "measure"}], "total": 5574.450000000001}, "i18n": {"rendererFormattedStrings": {"calculatorLink": "See calculator.", "collapseView": "Collapse view", "crcInitialNavigation": "Initial Navigation", "crcLongestDurationLabel": "Maximum critical path latency:", "dropdownCopyJSON": "Copy JSON", "dropdownDarkTheme": "Toggle Dark Theme", "dropdownPrintExpanded": "Print Expanded", "dropdownPrintSummary": "Print Summary", "dropdownSaveGist": "Save as Gist", "dropdownSaveHTML": "Save as HTML", "dropdownSaveJSON": "Save as JSON", "dropdownViewUnthrottledTrace": "View Unthrottled Trace", "dropdownViewer": "Open in Viewer", "errorLabel": "Error!", "errorMissingAuditInfo": "Report error: no audit information", "expandView": "Expand view", "firstPartyChipLabel": "1st party", "footerIssue": "File an issue", "goBackToAudits": "Go back to audits", "hide": "<PERSON>de", "insightsNotice": "Later this year, insights will replace performance audits. [Learn more and provide feedback here](https://github.com/GoogleChrome/lighthouse/discussions/16462).", "labDataTitle": "Lab Data", "lsPerformanceCategoryDescription": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.", "manualAuditsGroupTitle": "Additional items to manually check", "notApplicableAuditsGroupTitle": "Not applicable", "openInANewTabTooltip": "Open in a new tab", "opportunityResourceColumnLabel": "Opportunity", "opportunitySavingsColumnLabel": "Estimated Savings", "passedAuditsGroupTitle": "Passed audits", "runtimeAnalysisWindow": "Initial page load", "runtimeAnalysisWindowSnapshot": "Point-in-time snapshot", "runtimeAnalysisWindowTimespan": "User interactions timespan", "runtimeCustom": "Custom throttling", "runtimeDesktopEmulation": "Emulated Desktop", "runtimeMobileEmulation": "Emulated Moto G Power", "runtimeNoEmulation": "No emulation", "runtimeSettingsAxeVersion": "Axe version", "runtimeSettingsBenchmark": "Unthrottled CPU/Memory Power", "runtimeSettingsCPUThrottling": "CPU throttling", "runtimeSettingsDevice": "<PERSON><PERSON>", "runtimeSettingsNetworkThrottling": "Network throttling", "runtimeSettingsScreenEmulation": "Screen emulation", "runtimeSettingsUANetwork": "User agent (network)", "runtimeSingleLoad": "Single page session", "runtimeSingleLoadTooltip": "This data is taken from a single page session, as opposed to field data summarizing many sessions.", "runtimeSlow4g": "Slow 4G throttling", "runtimeUnknown": "Unknown", "show": "Show", "showRelevantAudits": "Show audits relevant to:", "snippetCollapseButtonLabel": "Collapse snippet", "snippetExpandButtonLabel": "Expand snippet", "thirdPartyResourcesLabel": "Show 3rd-party resources", "throttlingProvided": "Provided by environment", "toplevelWarningsMessage": "There were issues affecting this run of Lighthouse:", "tryInsights": "Try insights", "unattributable": "Unattributable", "varianceDisclaimer": "Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.", "viewTraceLabel": "View Trace", "viewTreemapLabel": "View Treemap", "warningAuditsGroupTitle": "Passed audits but with warnings", "warningHeader": "Warnings: "}, "icuMessagePaths": {"core/lib/lh-error.js | pageLoadFailedWithStatusCode": [{"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "runtimeError.message"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "runWarnings[0]"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[is-on-https].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[redirects-http].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.viewport.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[first-contentful-paint].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[largest-contentful-paint].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[first-meaningful-paint].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[speed-index].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[screenshot-thumbnails].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[final-screenshot].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[total-blocking-time].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[max-potential-fid].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[cumulative-layout-shift].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[errors-in-console].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[server-response-time].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.interactive.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[user-timings].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[critical-request-chains].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.redirects.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[image-aspect-ratio].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[image-size-responsive].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.deprecations.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[third-party-cookies].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[mainthread-work-breakdown].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[bootup-time].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[uses-rel-preconnect].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[font-display].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.diagnostics.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[network-requests].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[network-rtt].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[network-server-latency].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[main-thread-tasks].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.metrics.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[resource-summary].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[third-party-summary].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[third-party-facades].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[largest-contentful-paint-element].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[lcp-lazy-loaded].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[layout-shifts].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[long-tasks].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[non-composited-animations].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[unsized-images].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[valid-source-maps].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[prioritize-lcp-image].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[csp-xss].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[has-hsts].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[origin-isolation].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[clickjacking-mitigation].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[script-treemap-data].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.accesskeys.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-allowed-attr].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-allowed-role].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-command-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-conditional-attr].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-deprecated-role].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-dialog-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-hidden-body].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-hidden-focus].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-input-field-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-meter-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-progressbar-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-prohibited-attr].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-required-attr].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-required-children].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-required-parent].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-roles].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-text].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-toggle-field-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-tooltip-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-treeitem-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-valid-attr-value].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[aria-valid-attr].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[button-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.bypass.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[color-contrast].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[definition-list].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.dlitem.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[document-title].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[duplicate-id-aria].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[empty-heading].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[form-field-multiple-labels].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[frame-title].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[heading-order].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[html-has-lang].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[html-lang-valid].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[html-xml-lang-mismatch].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[identical-links-same-purpose].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[image-alt].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[image-redundant-alt].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[input-button-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[input-image-alt].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[label-content-name-mismatch].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.label.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[landmark-one-main].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[link-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[link-in-text-block].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.list.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.listitem.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[meta-refresh].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[meta-viewport].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[object-alt].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[select-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[skip-link].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.tabindex.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[table-duplicate-name].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[table-fake-caption].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[target-size].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[td-has-header].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[td-headers-attr].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[th-has-data-cells].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[valid-lang].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[video-caption].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[custom-controls-labels].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[custom-controls-roles].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[focus-traps].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[focusable-controls].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[interactive-element-affordance].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[logical-tab-order].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[managed-focus].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[offscreen-content-hidden].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[use-landmarks].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[visual-order-follows-dom].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[uses-long-cache-ttl].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[total-byte-weight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[offscreen-images].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[render-blocking-resources].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[unminified-css].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[unminified-javascript].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[unused-css-rules].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[unused-javascript].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[modern-image-formats].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[uses-optimized-images].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[uses-text-compression].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[uses-responsive-images].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[efficient-animated-content].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[duplicated-javascript].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[legacy-javascript].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.doctype.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.charset.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[dom-size].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[geolocation-on-start].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[inspector-issues].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[no-document-write].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[js-libraries].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[notification-on-start].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[paste-preventing-inputs].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[uses-http2].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[uses-passive-event-listeners].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[meta-description].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[http-status-code].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[font-size].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[link-text].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[crawlable-anchors].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[is-crawlable].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[robots-txt].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.hreflang.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits.canonical.errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[structured-data].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[bf-cache].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[cache-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[cls-culprits-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[document-latency-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[dom-size-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[duplicated-javascript-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[font-display-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[forced-reflow-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[image-delivery-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[interaction-to-next-paint-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[lcp-discovery-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[lcp-phases-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[legacy-javascript-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[modern-http-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[network-dependency-tree-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[render-blocking-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[third-parties-insight].errorMessage"}, {"values": {"errorCode": "ERRORED_DOCUMENT_REQUEST", "statusCode": "404"}, "path": "audits[viewport-insight].errorMessage"}], "core/audits/is-on-https.js | title": ["audits[is-on-https].title"], "core/audits/is-on-https.js | description": ["audits[is-on-https].description"], "core/audits/redirects-http.js | title": ["audits[redirects-http].title"], "core/audits/redirects-http.js | description": ["audits[redirects-http].description"], "core/audits/viewport.js | title": ["audits.viewport.title"], "core/audits/viewport.js | description": ["audits.viewport.description"], "core/lib/i18n/i18n.js | firstContentfulPaintMetric": ["audits[first-contentful-paint].title"], "core/audits/metrics/first-contentful-paint.js | description": ["audits[first-contentful-paint].description"], "core/lib/i18n/i18n.js | largestContentfulPaintMetric": ["audits[largest-contentful-paint].title"], "core/audits/metrics/largest-contentful-paint.js | description": ["audits[largest-contentful-paint].description"], "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": ["audits[first-meaningful-paint].title"], "core/audits/metrics/first-meaningful-paint.js | description": ["audits[first-meaningful-paint].description"], "core/lib/i18n/i18n.js | speedIndexMetric": ["audits[speed-index].title"], "core/audits/metrics/speed-index.js | description": ["audits[speed-index].description"], "core/lib/i18n/i18n.js | totalBlockingTimeMetric": ["audits[total-blocking-time].title"], "core/audits/metrics/total-blocking-time.js | description": ["audits[total-blocking-time].description"], "core/lib/i18n/i18n.js | maxPotentialFIDMetric": ["audits[max-potential-fid].title"], "core/audits/metrics/max-potential-fid.js | description": ["audits[max-potential-fid].description"], "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": ["audits[cumulative-layout-shift].title"], "core/audits/metrics/cumulative-layout-shift.js | description": ["audits[cumulative-layout-shift].description"], "core/audits/errors-in-console.js | title": ["audits[errors-in-console].title"], "core/audits/errors-in-console.js | description": ["audits[errors-in-console].description"], "core/audits/server-response-time.js | title": ["audits[server-response-time].title"], "core/audits/server-response-time.js | description": ["audits[server-response-time].description"], "core/lib/i18n/i18n.js | interactiveMetric": ["audits.interactive.title"], "core/audits/metrics/interactive.js | description": ["audits.interactive.description"], "core/audits/user-timings.js | title": ["audits[user-timings].title"], "core/audits/user-timings.js | description": ["audits[user-timings].description"], "core/audits/critical-request-chains.js | title": ["audits[critical-request-chains].title"], "core/audits/critical-request-chains.js | description": ["audits[critical-request-chains].description"], "core/audits/redirects.js | title": ["audits.redirects.title"], "core/audits/redirects.js | description": ["audits.redirects.description"], "core/audits/image-aspect-ratio.js | title": ["audits[image-aspect-ratio].title"], "core/audits/image-aspect-ratio.js | description": ["audits[image-aspect-ratio].description"], "core/audits/image-size-responsive.js | title": ["audits[image-size-responsive].title"], "core/audits/image-size-responsive.js | description": ["audits[image-size-responsive].description"], "core/audits/deprecations.js | title": ["audits.deprecations.title"], "core/audits/deprecations.js | description": ["audits.deprecations.description"], "core/audits/third-party-cookies.js | title": ["audits[third-party-cookies].title"], "core/audits/third-party-cookies.js | description": ["audits[third-party-cookies].description"], "core/audits/mainthread-work-breakdown.js | title": ["audits[mainthread-work-breakdown].title"], "core/audits/mainthread-work-breakdown.js | description": ["audits[mainthread-work-breakdown].description"], "core/audits/bootup-time.js | title": ["audits[bootup-time].title"], "core/audits/bootup-time.js | description": ["audits[bootup-time].description"], "core/audits/uses-rel-preconnect.js | title": ["audits[uses-rel-preconnect].title"], "core/audits/uses-rel-preconnect.js | description": ["audits[uses-rel-preconnect].description"], "core/audits/font-display.js | title": ["audits[font-display].title"], "core/audits/font-display.js | description": ["audits[font-display].description"], "core/audits/network-rtt.js | title": ["audits[network-rtt].title"], "core/audits/network-rtt.js | description": ["audits[network-rtt].description"], "core/audits/network-server-latency.js | title": ["audits[network-server-latency].title"], "core/audits/network-server-latency.js | description": ["audits[network-server-latency].description"], "core/audits/third-party-summary.js | title": ["audits[third-party-summary].title"], "core/audits/third-party-summary.js | description": ["audits[third-party-summary].description"], "core/audits/third-party-facades.js | title": ["audits[third-party-facades].title"], "core/audits/third-party-facades.js | description": ["audits[third-party-facades].description"], "core/audits/largest-contentful-paint-element.js | title": ["audits[largest-contentful-paint-element].title"], "core/audits/largest-contentful-paint-element.js | description": ["audits[largest-contentful-paint-element].description"], "core/audits/lcp-lazy-loaded.js | title": ["audits[lcp-lazy-loaded].title"], "core/audits/lcp-lazy-loaded.js | description": ["audits[lcp-lazy-loaded].description"], "core/audits/layout-shifts.js | title": ["audits[layout-shifts].title"], "core/audits/layout-shifts.js | description": ["audits[layout-shifts].description"], "core/audits/long-tasks.js | title": ["audits[long-tasks].title"], "core/audits/long-tasks.js | description": ["audits[long-tasks].description"], "core/audits/non-composited-animations.js | title": ["audits[non-composited-animations].title"], "core/audits/non-composited-animations.js | description": ["audits[non-composited-animations].description"], "core/audits/unsized-images.js | title": ["audits[unsized-images].title"], "core/audits/unsized-images.js | description": ["audits[unsized-images].description"], "core/audits/valid-source-maps.js | title": ["audits[valid-source-maps].title"], "core/audits/valid-source-maps.js | description": ["audits[valid-source-maps].description"], "core/audits/prioritize-lcp-image.js | title": ["audits[prioritize-lcp-image].title"], "core/audits/prioritize-lcp-image.js | description": ["audits[prioritize-lcp-image].description"], "core/audits/csp-xss.js | title": ["audits[csp-xss].title"], "core/audits/csp-xss.js | description": ["audits[csp-xss].description"], "core/audits/has-hsts.js | title": ["audits[has-hsts].title"], "core/audits/has-hsts.js | description": ["audits[has-hsts].description"], "core/audits/origin-isolation.js | title": ["audits[origin-isolation].title"], "core/audits/origin-isolation.js | description": ["audits[origin-isolation].description"], "core/audits/clickjacking-mitigation.js | title": ["audits[clickjacking-mitigation].title"], "core/audits/clickjacking-mitigation.js | description": ["audits[clickjacking-mitigation].description"], "core/audits/accessibility/accesskeys.js | title": ["audits.accesskeys.title"], "core/audits/accessibility/accesskeys.js | description": ["audits.accesskeys.description"], "core/audits/accessibility/aria-allowed-attr.js | title": ["audits[aria-allowed-attr].title"], "core/audits/accessibility/aria-allowed-attr.js | description": ["audits[aria-allowed-attr].description"], "core/audits/accessibility/aria-allowed-role.js | title": ["audits[aria-allowed-role].title"], "core/audits/accessibility/aria-allowed-role.js | description": ["audits[aria-allowed-role].description"], "core/audits/accessibility/aria-command-name.js | title": ["audits[aria-command-name].title"], "core/audits/accessibility/aria-command-name.js | description": ["audits[aria-command-name].description"], "core/audits/accessibility/aria-conditional-attr.js | title": ["audits[aria-conditional-attr].title"], "core/audits/accessibility/aria-conditional-attr.js | description": ["audits[aria-conditional-attr].description"], "core/audits/accessibility/aria-deprecated-role.js | title": ["audits[aria-deprecated-role].title"], "core/audits/accessibility/aria-deprecated-role.js | description": ["audits[aria-deprecated-role].description"], "core/audits/accessibility/aria-dialog-name.js | title": ["audits[aria-dialog-name].title"], "core/audits/accessibility/aria-dialog-name.js | description": ["audits[aria-dialog-name].description"], "core/audits/accessibility/aria-hidden-body.js | title": ["audits[aria-hidden-body].title"], "core/audits/accessibility/aria-hidden-body.js | description": ["audits[aria-hidden-body].description"], "core/audits/accessibility/aria-hidden-focus.js | title": ["audits[aria-hidden-focus].title"], "core/audits/accessibility/aria-hidden-focus.js | description": ["audits[aria-hidden-focus].description"], "core/audits/accessibility/aria-input-field-name.js | title": ["audits[aria-input-field-name].title"], "core/audits/accessibility/aria-input-field-name.js | description": ["audits[aria-input-field-name].description"], "core/audits/accessibility/aria-meter-name.js | title": ["audits[aria-meter-name].title"], "core/audits/accessibility/aria-meter-name.js | description": ["audits[aria-meter-name].description"], "core/audits/accessibility/aria-progressbar-name.js | title": ["audits[aria-progressbar-name].title"], "core/audits/accessibility/aria-progressbar-name.js | description": ["audits[aria-progressbar-name].description"], "core/audits/accessibility/aria-prohibited-attr.js | title": ["audits[aria-prohibited-attr].title"], "core/audits/accessibility/aria-prohibited-attr.js | description": ["audits[aria-prohibited-attr].description"], "core/audits/accessibility/aria-required-attr.js | title": ["audits[aria-required-attr].title"], "core/audits/accessibility/aria-required-attr.js | description": ["audits[aria-required-attr].description"], "core/audits/accessibility/aria-required-children.js | title": ["audits[aria-required-children].title"], "core/audits/accessibility/aria-required-children.js | description": ["audits[aria-required-children].description"], "core/audits/accessibility/aria-required-parent.js | title": ["audits[aria-required-parent].title"], "core/audits/accessibility/aria-required-parent.js | description": ["audits[aria-required-parent].description"], "core/audits/accessibility/aria-roles.js | title": ["audits[aria-roles].title"], "core/audits/accessibility/aria-roles.js | description": ["audits[aria-roles].description"], "core/audits/accessibility/aria-text.js | title": ["audits[aria-text].title"], "core/audits/accessibility/aria-text.js | description": ["audits[aria-text].description"], "core/audits/accessibility/aria-toggle-field-name.js | title": ["audits[aria-toggle-field-name].title"], "core/audits/accessibility/aria-toggle-field-name.js | description": ["audits[aria-toggle-field-name].description"], "core/audits/accessibility/aria-tooltip-name.js | title": ["audits[aria-tooltip-name].title"], "core/audits/accessibility/aria-tooltip-name.js | description": ["audits[aria-tooltip-name].description"], "core/audits/accessibility/aria-treeitem-name.js | title": ["audits[aria-treeitem-name].title"], "core/audits/accessibility/aria-treeitem-name.js | description": ["audits[aria-treeitem-name].description"], "core/audits/accessibility/aria-valid-attr-value.js | title": ["audits[aria-valid-attr-value].title"], "core/audits/accessibility/aria-valid-attr-value.js | description": ["audits[aria-valid-attr-value].description"], "core/audits/accessibility/aria-valid-attr.js | title": ["audits[aria-valid-attr].title"], "core/audits/accessibility/aria-valid-attr.js | description": ["audits[aria-valid-attr].description"], "core/audits/accessibility/button-name.js | title": ["audits[button-name].title"], "core/audits/accessibility/button-name.js | description": ["audits[button-name].description"], "core/audits/accessibility/bypass.js | title": ["audits.bypass.title"], "core/audits/accessibility/bypass.js | description": ["audits.bypass.description"], "core/audits/accessibility/color-contrast.js | title": ["audits[color-contrast].title"], "core/audits/accessibility/color-contrast.js | description": ["audits[color-contrast].description"], "core/audits/accessibility/definition-list.js | title": ["audits[definition-list].title"], "core/audits/accessibility/definition-list.js | description": ["audits[definition-list].description"], "core/audits/accessibility/dlitem.js | title": ["audits.dlitem.title"], "core/audits/accessibility/dlitem.js | description": ["audits.dlitem.description"], "core/audits/accessibility/document-title.js | title": ["audits[document-title].title"], "core/audits/accessibility/document-title.js | description": ["audits[document-title].description"], "core/audits/accessibility/duplicate-id-aria.js | title": ["audits[duplicate-id-aria].title"], "core/audits/accessibility/duplicate-id-aria.js | description": ["audits[duplicate-id-aria].description"], "core/audits/accessibility/empty-heading.js | title": ["audits[empty-heading].title"], "core/audits/accessibility/empty-heading.js | description": ["audits[empty-heading].description"], "core/audits/accessibility/form-field-multiple-labels.js | title": ["audits[form-field-multiple-labels].title"], "core/audits/accessibility/form-field-multiple-labels.js | description": ["audits[form-field-multiple-labels].description"], "core/audits/accessibility/frame-title.js | title": ["audits[frame-title].title"], "core/audits/accessibility/frame-title.js | description": ["audits[frame-title].description"], "core/audits/accessibility/heading-order.js | title": ["audits[heading-order].title"], "core/audits/accessibility/heading-order.js | description": ["audits[heading-order].description"], "core/audits/accessibility/html-has-lang.js | title": ["audits[html-has-lang].title"], "core/audits/accessibility/html-has-lang.js | description": ["audits[html-has-lang].description"], "core/audits/accessibility/html-lang-valid.js | title": ["audits[html-lang-valid].title"], "core/audits/accessibility/html-lang-valid.js | description": ["audits[html-lang-valid].description"], "core/audits/accessibility/html-xml-lang-mismatch.js | title": ["audits[html-xml-lang-mismatch].title"], "core/audits/accessibility/html-xml-lang-mismatch.js | description": ["audits[html-xml-lang-mismatch].description"], "core/audits/accessibility/identical-links-same-purpose.js | title": ["audits[identical-links-same-purpose].title"], "core/audits/accessibility/identical-links-same-purpose.js | description": ["audits[identical-links-same-purpose].description"], "core/audits/accessibility/image-alt.js | title": ["audits[image-alt].title"], "core/audits/accessibility/image-alt.js | description": ["audits[image-alt].description"], "core/audits/accessibility/image-redundant-alt.js | title": ["audits[image-redundant-alt].title"], "core/audits/accessibility/image-redundant-alt.js | description": ["audits[image-redundant-alt].description"], "core/audits/accessibility/input-button-name.js | title": ["audits[input-button-name].title"], "core/audits/accessibility/input-button-name.js | description": ["audits[input-button-name].description"], "core/audits/accessibility/input-image-alt.js | title": ["audits[input-image-alt].title"], "core/audits/accessibility/input-image-alt.js | description": ["audits[input-image-alt].description"], "core/audits/accessibility/label-content-name-mismatch.js | title": ["audits[label-content-name-mismatch].title"], "core/audits/accessibility/label-content-name-mismatch.js | description": ["audits[label-content-name-mismatch].description"], "core/audits/accessibility/label.js | title": ["audits.label.title"], "core/audits/accessibility/label.js | description": ["audits.label.description"], "core/audits/accessibility/landmark-one-main.js | title": ["audits[landmark-one-main].title"], "core/audits/accessibility/landmark-one-main.js | description": ["audits[landmark-one-main].description"], "core/audits/accessibility/link-name.js | title": ["audits[link-name].title"], "core/audits/accessibility/link-name.js | description": ["audits[link-name].description"], "core/audits/accessibility/link-in-text-block.js | title": ["audits[link-in-text-block].title"], "core/audits/accessibility/link-in-text-block.js | description": ["audits[link-in-text-block].description"], "core/audits/accessibility/list.js | title": ["audits.list.title"], "core/audits/accessibility/list.js | description": ["audits.list.description"], "core/audits/accessibility/listitem.js | title": ["audits.listitem.title"], "core/audits/accessibility/listitem.js | description": ["audits.listitem.description"], "core/audits/accessibility/meta-refresh.js | title": ["audits[meta-refresh].title"], "core/audits/accessibility/meta-refresh.js | description": ["audits[meta-refresh].description"], "core/audits/accessibility/meta-viewport.js | title": ["audits[meta-viewport].title"], "core/audits/accessibility/meta-viewport.js | description": ["audits[meta-viewport].description"], "core/audits/accessibility/object-alt.js | title": ["audits[object-alt].title"], "core/audits/accessibility/object-alt.js | description": ["audits[object-alt].description"], "core/audits/accessibility/select-name.js | title": ["audits[select-name].title"], "core/audits/accessibility/select-name.js | description": ["audits[select-name].description"], "core/audits/accessibility/skip-link.js | title": ["audits[skip-link].title"], "core/audits/accessibility/skip-link.js | description": ["audits[skip-link].description"], "core/audits/accessibility/tabindex.js | title": ["audits.tabindex.title"], "core/audits/accessibility/tabindex.js | description": ["audits.tabindex.description"], "core/audits/accessibility/table-duplicate-name.js | title": ["audits[table-duplicate-name].title"], "core/audits/accessibility/table-duplicate-name.js | description": ["audits[table-duplicate-name].description"], "core/audits/accessibility/table-fake-caption.js | title": ["audits[table-fake-caption].title"], "core/audits/accessibility/table-fake-caption.js | description": ["audits[table-fake-caption].description"], "core/audits/accessibility/target-size.js | title": ["audits[target-size].title"], "core/audits/accessibility/target-size.js | description": ["audits[target-size].description"], "core/audits/accessibility/td-has-header.js | title": ["audits[td-has-header].title"], "core/audits/accessibility/td-has-header.js | description": ["audits[td-has-header].description"], "core/audits/accessibility/td-headers-attr.js | title": ["audits[td-headers-attr].title"], "core/audits/accessibility/td-headers-attr.js | description": ["audits[td-headers-attr].description"], "core/audits/accessibility/th-has-data-cells.js | title": ["audits[th-has-data-cells].title"], "core/audits/accessibility/th-has-data-cells.js | description": ["audits[th-has-data-cells].description"], "core/audits/accessibility/valid-lang.js | title": ["audits[valid-lang].title"], "core/audits/accessibility/valid-lang.js | description": ["audits[valid-lang].description"], "core/audits/accessibility/video-caption.js | title": ["audits[video-caption].title"], "core/audits/accessibility/video-caption.js | description": ["audits[video-caption].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": ["audits[uses-long-cache-ttl].title"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": ["audits[uses-long-cache-ttl].description"], "core/audits/byte-efficiency/total-byte-weight.js | title": ["audits[total-byte-weight].title"], "core/audits/byte-efficiency/total-byte-weight.js | description": ["audits[total-byte-weight].description"], "core/audits/byte-efficiency/offscreen-images.js | title": ["audits[offscreen-images].title"], "core/audits/byte-efficiency/offscreen-images.js | description": ["audits[offscreen-images].description"], "core/audits/byte-efficiency/render-blocking-resources.js | title": ["audits[render-blocking-resources].title"], "core/audits/byte-efficiency/render-blocking-resources.js | description": ["audits[render-blocking-resources].description"], "core/audits/byte-efficiency/unminified-css.js | title": ["audits[unminified-css].title"], "core/audits/byte-efficiency/unminified-css.js | description": ["audits[unminified-css].description"], "core/audits/byte-efficiency/unminified-javascript.js | title": ["audits[unminified-javascript].title"], "core/audits/byte-efficiency/unminified-javascript.js | description": ["audits[unminified-javascript].description"], "core/audits/byte-efficiency/unused-css-rules.js | title": ["audits[unused-css-rules].title"], "core/audits/byte-efficiency/unused-css-rules.js | description": ["audits[unused-css-rules].description"], "core/audits/byte-efficiency/unused-javascript.js | title": ["audits[unused-javascript].title"], "core/audits/byte-efficiency/unused-javascript.js | description": ["audits[unused-javascript].description"], "core/audits/byte-efficiency/modern-image-formats.js | title": ["audits[modern-image-formats].title"], "core/audits/byte-efficiency/modern-image-formats.js | description": ["audits[modern-image-formats].description"], "core/audits/byte-efficiency/uses-optimized-images.js | title": ["audits[uses-optimized-images].title"], "core/audits/byte-efficiency/uses-optimized-images.js | description": ["audits[uses-optimized-images].description"], "core/audits/byte-efficiency/uses-text-compression.js | title": ["audits[uses-text-compression].title"], "core/audits/byte-efficiency/uses-text-compression.js | description": ["audits[uses-text-compression].description"], "core/audits/byte-efficiency/uses-responsive-images.js | title": ["audits[uses-responsive-images].title"], "core/audits/byte-efficiency/uses-responsive-images.js | description": ["audits[uses-responsive-images].description"], "core/audits/byte-efficiency/efficient-animated-content.js | title": ["audits[efficient-animated-content].title"], "core/audits/byte-efficiency/efficient-animated-content.js | description": ["audits[efficient-animated-content].description"], "core/audits/byte-efficiency/duplicated-javascript.js | title": ["audits[duplicated-javascript].title"], "core/audits/byte-efficiency/duplicated-javascript.js | description": ["audits[duplicated-javascript].description"], "core/audits/byte-efficiency/legacy-javascript.js | title": ["audits[legacy-javascript].title"], "core/audits/byte-efficiency/legacy-javascript.js | description": ["audits[legacy-javascript].description"], "core/audits/dobetterweb/doctype.js | title": ["audits.doctype.title"], "core/audits/dobetterweb/doctype.js | description": ["audits.doctype.description"], "core/audits/dobetterweb/charset.js | title": ["audits.charset.title"], "core/audits/dobetterweb/charset.js | description": ["audits.charset.description"], "core/audits/dobetterweb/dom-size.js | title": ["audits[dom-size].title"], "core/audits/dobetterweb/dom-size.js | description": ["audits[dom-size].description"], "core/audits/dobetterweb/geolocation-on-start.js | title": ["audits[geolocation-on-start].title"], "core/audits/dobetterweb/geolocation-on-start.js | description": ["audits[geolocation-on-start].description"], "core/audits/dobetterweb/inspector-issues.js | title": ["audits[inspector-issues].title"], "core/audits/dobetterweb/inspector-issues.js | description": ["audits[inspector-issues].description"], "core/audits/dobetterweb/no-document-write.js | title": ["audits[no-document-write].title"], "core/audits/dobetterweb/no-document-write.js | description": ["audits[no-document-write].description"], "core/audits/dobetterweb/js-libraries.js | title": ["audits[js-libraries].title"], "core/audits/dobetterweb/js-libraries.js | description": ["audits[js-libraries].description"], "core/audits/dobetterweb/notification-on-start.js | title": ["audits[notification-on-start].title"], "core/audits/dobetterweb/notification-on-start.js | description": ["audits[notification-on-start].description"], "core/audits/dobetterweb/paste-preventing-inputs.js | title": ["audits[paste-preventing-inputs].title"], "core/audits/dobetterweb/paste-preventing-inputs.js | description": ["audits[paste-preventing-inputs].description"], "core/audits/dobetterweb/uses-http2.js | title": ["audits[uses-http2].title"], "core/audits/dobetterweb/uses-http2.js | description": ["audits[uses-http2].description"], "core/audits/dobetterweb/uses-passive-event-listeners.js | title": ["audits[uses-passive-event-listeners].title"], "core/audits/dobetterweb/uses-passive-event-listeners.js | description": ["audits[uses-passive-event-listeners].description"], "core/audits/seo/meta-description.js | title": ["audits[meta-description].title"], "core/audits/seo/meta-description.js | description": ["audits[meta-description].description"], "core/audits/seo/http-status-code.js | title": ["audits[http-status-code].title"], "core/audits/seo/http-status-code.js | description": ["audits[http-status-code].description"], "core/audits/seo/font-size.js | title": ["audits[font-size].title"], "core/audits/seo/font-size.js | description": ["audits[font-size].description"], "core/audits/seo/link-text.js | title": ["audits[link-text].title"], "core/audits/seo/link-text.js | description": ["audits[link-text].description"], "core/audits/seo/crawlable-anchors.js | title": ["audits[crawlable-anchors].title"], "core/audits/seo/crawlable-anchors.js | description": ["audits[crawlable-anchors].description"], "core/audits/seo/is-crawlable.js | title": ["audits[is-crawlable].title"], "core/audits/seo/is-crawlable.js | description": ["audits[is-crawlable].description"], "core/audits/seo/robots-txt.js | title": ["audits[robots-txt].title"], "core/audits/seo/robots-txt.js | description": ["audits[robots-txt].description"], "core/audits/seo/hreflang.js | title": ["audits.hreflang.title"], "core/audits/seo/hreflang.js | description": ["audits.hreflang.description"], "core/audits/seo/canonical.js | title": ["audits.canonical.title"], "core/audits/seo/canonical.js | description": ["audits.canonical.description"], "core/audits/seo/manual/structured-data.js | title": ["audits[structured-data].title"], "core/audits/seo/manual/structured-data.js | description": ["audits[structured-data].description"], "core/audits/bf-cache.js | title": ["audits[bf-cache].title"], "core/audits/bf-cache.js | description": ["audits[bf-cache].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | title": ["audits[cache-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | description": ["audits[cache-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title": ["audits[cls-culprits-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description": ["audits[cls-culprits-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title": ["audits[document-latency-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description": ["audits[document-latency-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title": ["audits[dom-size-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description": ["audits[dom-size-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title": ["audits[duplicated-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description": ["audits[duplicated-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title": ["audits[font-display-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description": ["audits[font-display-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title": ["audits[forced-reflow-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description": ["audits[forced-reflow-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title": ["audits[image-delivery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description": ["audits[image-delivery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | title": ["audits[interaction-to-next-paint-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | description": ["audits[interaction-to-next-paint-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title": ["audits[lcp-discovery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description": ["audits[lcp-discovery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | title": ["audits[lcp-phases-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | description": ["audits[lcp-phases-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | title": ["audits[legacy-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | description": ["audits[legacy-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | title": ["audits[modern-http-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | description": ["audits[modern-http-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title": ["audits[network-dependency-tree-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description": ["audits[network-dependency-tree-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title": ["audits[render-blocking-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description": ["audits[render-blocking-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title": ["audits[third-parties-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description": ["audits[third-parties-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title": ["audits[viewport-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description": ["audits[viewport-insight].description"], "core/config/default-config.js | performanceCategoryTitle": ["categories.performance.title"], "core/config/default-config.js | a11yCategoryTitle": ["categories.accessibility.title"], "core/config/default-config.js | a11yCategoryDescription": ["categories.accessibility.description"], "core/config/default-config.js | a11yCategoryManualDescription": ["categories.accessibility.manualDescription"], "core/config/default-config.js | bestPracticesCategoryTitle": ["categories[best-practices].title"], "core/config/default-config.js | seoCategoryTitle": ["categories.seo.title"], "core/config/default-config.js | seoCategoryDescription": ["categories.seo.description"], "core/config/default-config.js | seoCategoryManualDescription": ["categories.seo.manualDescription"], "core/config/default-config.js | metricGroupTitle": ["categoryGroups.metrics.title"], "core/config/default-config.js | insightsGroupTitle": ["categoryGroups.insights.title"], "core/config/default-config.js | insightsGroupDescription": ["categoryGroups.insights.description"], "core/config/default-config.js | diagnosticsGroupTitle": ["categoryGroups.diagnostics.title"], "core/config/default-config.js | diagnosticsGroupDescription": ["categoryGroups.diagnostics.description"], "core/config/default-config.js | a11yBestPracticesGroupTitle": ["categoryGroups[a11y-best-practices].title"], "core/config/default-config.js | a11yBestPracticesGroupDescription": ["categoryGroups[a11y-best-practices].description"], "core/config/default-config.js | a11yColorContrastGroupTitle": ["categoryGroups[a11y-color-contrast].title"], "core/config/default-config.js | a11yColorContrastGroupDescription": ["categoryGroups[a11y-color-contrast].description"], "core/config/default-config.js | a11yNamesLabelsGroupTitle": ["categoryGroups[a11y-names-labels].title"], "core/config/default-config.js | a11yNamesLabelsGroupDescription": ["categoryGroups[a11y-names-labels].description"], "core/config/default-config.js | a11yNavigationGroupTitle": ["categoryGroups[a11y-navigation].title"], "core/config/default-config.js | a11yNavigationGroupDescription": ["categoryGroups[a11y-navigation].description"], "core/config/default-config.js | a11yAriaGroupTitle": ["categoryGroups[a11y-aria].title"], "core/config/default-config.js | a11yAriaGroupDescription": ["categoryGroups[a11y-aria].description"], "core/config/default-config.js | a11yLanguageGroupTitle": ["categoryGroups[a11y-language].title"], "core/config/default-config.js | a11yLanguageGroupDescription": ["categoryGroups[a11y-language].description"], "core/config/default-config.js | a11yAudioVideoGroupTitle": ["categoryGroups[a11y-audio-video].title"], "core/config/default-config.js | a11yAudioVideoGroupDescription": ["categoryGroups[a11y-audio-video].description"], "core/config/default-config.js | a11yTablesListsVideoGroupTitle": ["categoryGroups[a11y-tables-lists].title"], "core/config/default-config.js | a11yTablesListsVideoGroupDescription": ["categoryGroups[a11y-tables-lists].description"], "core/config/default-config.js | seoMobileGroupTitle": ["categoryGroups[seo-mobile].title"], "core/config/default-config.js | seoMobileGroupDescription": ["categoryGroups[seo-mobile].description"], "core/config/default-config.js | seoContentGroupTitle": ["categoryGroups[seo-content].title"], "core/config/default-config.js | seoContentGroupDescription": ["categoryGroups[seo-content].description"], "core/config/default-config.js | seoCrawlingGroupTitle": ["categoryGroups[seo-crawl].title"], "core/config/default-config.js | seoCrawlingGroupDescription": ["categoryGroups[seo-crawl].description"], "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": ["categoryGroups[best-practices-trust-safety].title"], "core/config/default-config.js | bestPracticesUXGroupTitle": ["categoryGroups[best-practices-ux].title"], "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": ["categoryGroups[best-practices-browser-compat].title"], "core/config/default-config.js | bestPracticesGeneralGroupTitle": ["categoryGroups[best-practices-general].title"]}}}