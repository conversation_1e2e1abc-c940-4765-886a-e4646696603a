<?php
/**
 * Simple Authentication API
 * Replaces Supabase Auth functionality
 */

require_once 'config/database.php';

setCORSHeaders();

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

switch ($method) {
    case 'POST':
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'login':
                login($db, $input);
                break;
            case 'register':
                register($db, $input);
                break;
            case 'logout':
                logout();
                break;
            default:
                errorResponse('Invalid action');
        }
        break;
    
    case 'GET':
        getCurrentUserInfo($db);
        break;
    
    default:
        errorResponse('Method not allowed', 405);
}

function login($db, $input) {
    if (!isset($input['username']) || !isset($input['password'])) {
        errorResponse('Username and password are required');
    }
    
    try {
        $query = "SELECT id, username, password_hash FROM profiles WHERE username = :username";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':username', $input['username']);
        $stmt->execute();
        
        $user = $stmt->fetch();
        
        if (!$user || !password_verify($input['password'], $user['password_hash'])) {
            errorResponse('Invalid credentials', 401);
        }
        
        loginUser($user['id']);
        
        successResponse([
            'user' => [
                'id' => $user['id'],
                'username' => $user['username']
            ],
            'message' => 'Login successful'
        ]);
        
    } catch (Exception $e) {
        error_log("Login error: " . $e->getMessage());
        errorResponse('Login failed', 500);
    }
}

function register($db, $input) {
    if (!isset($input['username']) || !isset($input['password'])) {
        errorResponse('Username and password are required');
    }
    
    if (strlen($input['password']) < 6) {
        errorResponse('Password must be at least 6 characters');
    }
    
    try {
        // Check if username already exists
        $checkQuery = "SELECT id FROM profiles WHERE username = :username";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':username', $input['username']);
        $checkStmt->execute();
        
        if ($checkStmt->fetch()) {
            errorResponse('Username already exists');
        }
        
        // Create new user
        $query = "INSERT INTO profiles (id, username, password_hash, created_at, updated_at) VALUES (:id, :username, :password_hash, NOW(), NOW())";
        $stmt = $db->prepare($query);
        
        $userId = generateUUID();
        $passwordHash = password_hash($input['password'], PASSWORD_DEFAULT);
        
        $stmt->bindParam(':id', $userId);
        $stmt->bindParam(':username', $input['username']);
        $stmt->bindParam(':password_hash', $passwordHash);
        
        $stmt->execute();
        
        loginUser($userId);
        
        successResponse([
            'user' => [
                'id' => $userId,
                'username' => $input['username']
            ],
            'message' => 'Registration successful'
        ]);
        
    } catch (Exception $e) {
        error_log("Registration error: " . $e->getMessage());
        errorResponse('Registration failed', 500);
    }
}

function logout() {
    logoutUser();
    successResponse(['message' => 'Logout successful']);
}

function getCurrentUserInfo($db) {
    $userId = getCurrentUser();
    
    if (!$userId) {
        errorResponse('Not authenticated', 401);
    }
    
    try {
        $query = "SELECT id, username, avatar_url, created_at FROM profiles WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $userId);
        $stmt->execute();
        
        $user = $stmt->fetch();
        
        if (!$user) {
            errorResponse('User not found', 404);
        }
        
        successResponse([
            'user' => $user
        ]);
        
    } catch (Exception $e) {
        error_log("Get user error: " . $e->getMessage());
        errorResponse('Failed to get user info', 500);
    }
}
?>
