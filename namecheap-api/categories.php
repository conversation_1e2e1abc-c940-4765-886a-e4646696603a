<?php
/**
 * Categories API endpoint
 */

require_once 'config/database.php';

setCORSHeaders();

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getCategories($db);
        break;
    
    default:
        errorResponse('Method not allowed', 405);
}

function getCategories($db) {
    try {
        $query = "SELECT * FROM categories ORDER BY name ASC";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $categories = $stmt->fetchAll();
        
        successResponse($categories);
        
    } catch (Exception $e) {
        error_log("Error fetching categories: " . $e->getMessage());
        errorResponse('Failed to fetch categories', 500);
    }
}
?>
