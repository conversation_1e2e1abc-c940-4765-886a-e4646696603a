// Script to check if video URLs are accessible
import fetch from 'node-fetch';

// Video URLs from your Supabase storage
const videos = [
    {
        title: "Appreciate a true Nyash Glory Widely Opened and natured by a Queen 1",
        videoUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746147162269-4_5802976136631687807.MP4",
        thumbnailUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746147185564-Screenshot_20250502-005053.jpg"
    },
    {
        title: "Just Enjoy the Exotic View 1",
        videoUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746151672757-1732811566180.mp4",
        thumbnailUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746151714983-Screenshot_20250502-020728.jpg"
    },
    {
        title: "Chantelle Kenyan Hottie 2",
        videoUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746083876786-VID_20250127_125639_549.mp4",
        thumbnailUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746083914163-thumbnail-1746083872518.jpg"
    }
];

async function checkUrl(url, type) {
    try {
        // Make a HEAD request to check if the URL is accessible
        const response = await fetch(url, { method: 'HEAD' });
        
        if (response.ok) {
            const contentType = response.headers.get('content-type');
            const contentLength = response.headers.get('content-length');
            const formattedSize = contentLength ? `${(parseInt(contentLength) / (1024 * 1024)).toFixed(2)} MB` : 'Unknown size';
            
            console.log(`✅ ${type} accessible: ${url}`);
            console.log(`   Content-Type: ${contentType}`);
            console.log(`   Size: ${formattedSize}`);
            return true;
        } else {
            console.log(`❌ ${type} not accessible: ${url}`);
            console.log(`   Status: ${response.status} ${response.statusText}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ Error checking ${type}: ${url}`);
        console.log(`   Error: ${error.message}`);
        return false;
    }
}

async function checkAllUrls() {
    console.log('Checking video and thumbnail URLs...\n');
    
    let videoSuccessCount = 0;
    let thumbnailSuccessCount = 0;
    
    for (const video of videos) {
        console.log(`\n=== ${video.title} ===`);
        
        // Check video URL
        const videoSuccess = await checkUrl(video.videoUrl, 'Video');
        if (videoSuccess) videoSuccessCount++;
        
        // Check thumbnail URL
        const thumbnailSuccess = await checkUrl(video.thumbnailUrl, 'Thumbnail');
        if (thumbnailSuccess) thumbnailSuccessCount++;
    }
    
    console.log('\n=== Summary ===');
    console.log(`Videos: ${videoSuccessCount}/${videos.length} accessible`);
    console.log(`Thumbnails: ${thumbnailSuccessCount}/${videos.length} accessible`);
}

checkAllUrls();
