#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://vsnsglgyapexhwyfylic.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzbnNnbGd5YXBleGh3eWZ5bGljIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExOTEsImV4cCI6MjA2MDgzNzE5MX0.6CQWpMT14h2kaIOk1_LMECuJrfRdmiGRo3vGyEDW9tM'
);

async function checkDatabase() {
  console.log('🔍 Checking database for videos with Supabase URLs...');
  
  try {
    const { data: videos, error } = await supabase
      .from('videos')
      .select('id, title, video_url, thumbnail_url')
      .ilike('video_url', '%supabase%')
      .limit(20);
    
    if (error) {
      console.error('❌ Error:', error);
      return;
    }
    
    console.log(`📊 Found ${videos.length} videos with Supabase URLs:`);
    console.log('=' .repeat(60));
    
    videos.forEach((video, i) => {
      console.log(`${i+1}. ${video.title}`);
      console.log(`   Video: ${video.video_url}`);
      console.log(`   Thumb: ${video.thumbnail_url || 'No thumbnail'}`);
      console.log('');
    });
    
    // Also check storage buckets
    console.log('🗂️  Checking Supabase storage buckets...');
    
    const { data: videoFiles, error: videoError } = await supabase.storage
      .from('videos')
      .list('', { limit: 10 });
    
    if (videoError) {
      console.error('❌ Videos bucket error:', videoError);
    } else {
      console.log(`📹 Videos bucket: ${videoFiles.length} files`);
      videoFiles.forEach((file, i) => {
        console.log(`  ${i+1}. ${file.name} (${(file.metadata?.size / 1024 / 1024).toFixed(2)} MB)`);
      });
    }
    
    const { data: thumbnailFiles, error: thumbnailError } = await supabase.storage
      .from('thumbnails')
      .list('', { limit: 10 });
    
    if (thumbnailError) {
      console.error('❌ Thumbnails bucket error:', thumbnailError);
    } else {
      console.log(`🖼️  Thumbnails bucket: ${thumbnailFiles.length} files`);
      thumbnailFiles.forEach((file, i) => {
        console.log(`  ${i+1}. ${file.name} (${(file.metadata?.size / 1024).toFixed(2)} KB)`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
  }
}

checkDatabase();
