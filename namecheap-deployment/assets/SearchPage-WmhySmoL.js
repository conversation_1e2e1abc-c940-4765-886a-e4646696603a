import{r as e,j as s}from"./react-core-B9nwsbCA.js";import{V as t}from"./VideoGrid-_iQm6QDa.js";import{b as a,S as r}from"./main-1loL2DJY.js";import{u as i,b as d}from"./router-BDggJ1ol.js";import{S as c}from"./icons-BWE0bDFO.js";import"./ui-components-Ct00E1u3.js";import"./utils-D7twGpBa.js";import"./state-4gBW_4Nx.js";const l=()=>{const l=i(),m=d(),{searchQuery:o,searchResults:n,isLoading:x,error:h,setSearchQuery:b,search:j}=a();e.useEffect((()=>{const e=new URLSearchParams(m.search).get("q");e&&(b(e),j(e))}),[m.search,b,j]);return s.jsxs("div",{className:"container mx-auto px-4 py-4 md:py-8",children:[s.jsxs("div",{className:"mb-6",children:[s.jsx("div",{className:"relative search-bar-container px-1 mb-4",children:s.jsx(r,{className:"w-full mobile-search-bar search-page-search"})}),s.jsxs("h1",{className:"text-xl md:text-3xl font-bold text-white mb-2 flex items-center",children:[s.jsx(c,{size:20,className:"mr-2 text-blue-700"}),"Search Results"]}),o&&s.jsxs("p",{className:"text-gray-400 text-sm md:text-base",children:["Showing results for ",s.jsxs("span",{className:"text-white font-medium",children:['"',o,'"']})]})]}),h&&s.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 mb-8",children:[s.jsx("h3",{className:"text-white font-bold mb-2",children:"Error"}),s.jsx("p",{className:"text-red-200",children:h}),h.includes("column")&&s.jsxs("div",{className:"mt-3 text-sm",children:[s.jsx("p",{className:"text-white",children:"This appears to be a database schema issue. The search is trying to access columns that don't exist."}),s.jsx("p",{className:"text-gray-300 mt-2",children:"We've updated the search to only look in title and description fields."}),s.jsx("button",{onClick:()=>j(o),className:"mt-3 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md",children:"Try Again"})]})]}),x?s.jsx("div",{className:"flex justify-center py-12",children:s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"})}):n.length>0?s.jsx(t,{videos:n,onVideoClick:e=>{return s=e.id,void l(`/video/${s}`);var s}}):o?s.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 md:p-8 text-center",children:[s.jsx(c,{size:36,className:"mx-auto text-gray-600 mb-3"}),s.jsx("h2",{className:"text-lg md:text-xl font-bold text-white mb-2",children:"No results found"}),s.jsxs("p",{className:"text-gray-400 mb-3 text-sm md:text-base",children:["We couldn't find any videos matching \"",o,'"']}),s.jsx("p",{className:"text-gray-500 text-xs md:text-sm",children:"Try different keywords or check for spelling mistakes"})]}):s.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 md:p-8 text-center",children:[s.jsx(c,{size:36,className:"mx-auto text-gray-600 mb-3"}),s.jsx("h2",{className:"text-lg md:text-xl font-bold text-white mb-2",children:"Search for videos"}),s.jsx("p",{className:"text-gray-400 text-sm md:text-base",children:"Use the search bar above to find videos"})]})]})};export{l as default};
