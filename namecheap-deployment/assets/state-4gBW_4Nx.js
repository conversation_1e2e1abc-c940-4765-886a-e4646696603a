import{r as e,g as t,a as n}from"./react-core-B9nwsbCA.js";const r=e=>{let t;const n=new Set,r=(e,r)=>{const o="function"==typeof e?e(t):e;if(!Object.is(o,t)){const e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach((n=>n(t,e)))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{n.clear()}},a=t=e(r,o,i);return i};var o={exports:{}},i={},a={exports:{}},s={},u=e;var c="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=u.useState,f=u.useEffect,d=u.useLayoutEffect,v=u.useDebugValue;function S(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!c(e,n)}catch(r){return!0}}var g="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=l({inst:{value:n,getSnapshot:t}}),o=r[0].inst,i=r[1];return d((function(){o.value=n,o.getSnapshot=t,S(o)&&i({inst:o})}),[e,n,t]),f((function(){return S(o)&&i({inst:o}),e((function(){S(o)&&i({inst:o})}))}),[e]),v(n),n};s.useSyncExternalStore=void 0!==u.useSyncExternalStore?u.useSyncExternalStore:g,a.exports=s;var h=e,m=a.exports;
/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var p="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},y=m.useSyncExternalStore,I=h.useRef,E=h.useEffect,b=h.useMemo,O=h.useDebugValue;i.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var i=I(null);if(null===i.current){var a={hasValue:!1,value:null};i.current=a}else a=i.current;i=b((function(){function e(e){if(!u){if(u=!0,i=e,e=r(e),void 0!==o&&a.hasValue){var t=a.value;if(o(t,e))return s=t}return s=e}if(t=s,p(i,e))return t;var n=r(e);return void 0!==o&&o(t,n)?t:(i=e,s=n)}var i,s,u=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]}),[t,n,r,o]);var s=y(e,i[0],i[1]);return E((function(){a.hasValue=!0,a.value=s}),[s]),O(s),s},o.exports=i;const w=t(o.exports),x={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_STORAGE_BASE_URL:"https://www.bluefilmx.com",VITE_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzbnNnbGd5YXBleGh3eWZ5bGljIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExOTEsImV4cCI6MjA2MDgzNzE5MX0.6CQWpMT14h2kaIOk1_LMECuJrfRdmiGRo3vGyEDW9tM",VITE_SUPABASE_URL:"https://vsnsglgyapexhwyfylic.supabase.co"},{useDebugValue:z}=n,{useSyncExternalStoreWithSelector:j}=w;let J=!1;const R=e=>e;const V=e=>{const t="function"==typeof e?(e=>e?r(e):r)(e):e,n=(e,n)=>function(e,t=R,n){"production"!==(x?"production":void 0)&&n&&!J&&(J=!0);const r=j(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return z(r),r}(t,e,n);return Object.assign(n,t),n},N=e=>e?V(e):V;function _(e,t){let n;try{n=e()}catch(u){return}return{getItem:e=>{var t;const r=e=>null===e?null:JSON.parse(e,void 0),o=null!=(t=n.getItem(e))?t:null;return o instanceof Promise?o.then(r):r(o)},setItem:(e,t)=>n.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>n.removeItem(e)}}const A=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then:e=>A(e)(n),catch(e){return this}}}catch(u){return{then(e){return this},catch:t=>A(t)(u)}}},D=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((e,t)=>(n,r,o)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},a=!1;const s=new Set,c=new Set;let l;try{l=i.getStorage()}catch(u){}if(!l)return e(((...e)=>{n(...e)}),r,o);const f=A(i.serialize),d=()=>{const e=i.partialize({...r()});let t;const n=f({state:e,version:i.version}).then((e=>l.setItem(i.name,e))).catch((e=>{t=e}));if(t)throw t;return n},v=o.setState;o.setState=(e,t)=>{v(e,t),d()};const S=e(((...e)=>{n(...e),d()}),r,o);let g;const h=()=>{var e;if(!l)return;a=!1,s.forEach((e=>e(r())));const t=(null==(e=i.onRehydrateStorage)?void 0:e.call(i,r()))||void 0;return A(l.getItem.bind(l))(i.name).then((e=>{if(e)return i.deserialize(e)})).then((e=>{if(e){if("number"!=typeof e.version||e.version===i.version)return e.state;if(i.migrate)return i.migrate(e.state,e.version)}})).then((e=>{var t;return g=i.merge(e,null!=(t=r())?t:S),n(g,!0),d()})).then((()=>{null==t||t(g,void 0),a=!0,c.forEach((e=>e(g)))})).catch((e=>{null==t||t(void 0,e)}))};return o.persist={setOptions:e=>{i={...i,...e},e.getStorage&&(l=e.getStorage())},clearStorage:()=>{null==l||l.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>h(),hasHydrated:()=>a,onHydrate:e=>(s.add(e),()=>{s.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},h(),g||S})(e,t):((e,t)=>(n,r,o)=>{let i={storage:_((()=>localStorage)),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},a=!1;const s=new Set,u=new Set;let c=i.storage;if(!c)return e(((...e)=>{n(...e)}),r,o);const l=()=>{const e=i.partialize({...r()});return c.setItem(i.name,{state:e,version:i.version})},f=o.setState;o.setState=(e,t)=>{f(e,t),l()};const d=e(((...e)=>{n(...e),l()}),r,o);let v;o.getInitialState=()=>d;const S=()=>{var e,t;if(!c)return;a=!1,s.forEach((e=>{var t;return e(null!=(t=r())?t:d)}));const o=(null==(t=i.onRehydrateStorage)?void 0:t.call(i,null!=(e=r())?e:d))||void 0;return A(c.getItem.bind(c))(i.name).then((e=>{if(e){if("number"!=typeof e.version||e.version===i.version)return e.state;if(i.migrate)return i.migrate(e.state,e.version)}})).then((e=>{var t;return v=i.merge(e,null!=(t=r())?t:d),n(v,!0),l()})).then((()=>{null==o||o(v,void 0),v=r(),a=!0,u.forEach((e=>e(v)))})).catch((e=>{null==o||o(void 0,e)}))};return o.persist={setOptions:e=>{i={...i,...e},e.storage&&(c=e.storage)},clearStorage:()=>{null==c||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>S(),hasHydrated:()=>a,onHydrate:e=>(s.add(e),()=>{s.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},i.skipHydration||S(),v||d})(e,t);export{N as c,D as p};
