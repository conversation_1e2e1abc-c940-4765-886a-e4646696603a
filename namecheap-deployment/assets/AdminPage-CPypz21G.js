import{r as e,j as s}from"./react-core-B9nwsbCA.js";import{u as r}from"./utils-D7twGpBa.js";import{B as a}from"./ui-components-Ct00E1u3.js";import{u as t}from"./router-BDggJ1ol.js";import{d as l,K as i,N as d}from"./icons-BWE0bDFO.js";import"./state-4gBW_4Nx.js";const c=()=>{const c=t(),{user:n,isLoading:o}=r(),[x,m]=e.useState(!1),[p,u]=e.useState(!0),[h,j]=e.useState([]),[f,b]=e.useState(!1),[y,N]=e.useState(null),[g,v]=e.useState(null);e.useEffect((()=>{(async()=>{if(n)try{const{data:e,error:s}=await supabase.from("user_roles").select("role").eq("user_id",n.id).single();m(!s&&"admin"===e?.role)}catch(e){m(!1)}finally{u(!1)}else u(!1)})()}),[n]),e.useEffect((()=>{o||n||c("/")}),[n,o,c]),e.useEffect((()=>{x&&!p&&(async()=>{if(x){b(!0),N(null);try{const{data:e,error:s}=await supabase.from("profiles").select("*").eq("is_approved",!1).order("created_at",{ascending:!1});if(s)throw s;j(e||[])}catch(e){N("Failed to load pending users. Please try again.")}finally{b(!1)}}})()}),[x,p]);return o||p?s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsx("div",{className:"flex justify-center items-center h-64",children:s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})})}):n?x?s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsx("h1",{className:"text-2xl md:text-3xl font-bold mb-6",children:"Admin Dashboard"}),s.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[s.jsxs("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[s.jsx(i,{className:"mr-2"})," Pending User Approvals"]}),y&&s.jsx("div",{className:"bg-red-900/30 border border-red-500 text-red-300 px-4 py-3 rounded mb-4",children:y}),g&&s.jsx("div",{className:"bg-green-900/30 border border-green-500 text-green-300 px-4 py-3 rounded mb-4",children:g}),f?s.jsx("div",{className:"flex justify-center py-8",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"})}):0===h.length?s.jsx("div",{className:"text-center py-8 text-gray-400",children:"No pending users to approve"}):s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"w-full",children:[s.jsx("thead",{children:s.jsxs("tr",{className:"border-b border-gray-700",children:[s.jsx("th",{className:"px-4 py-2 text-left",children:"Username"}),s.jsx("th",{className:"px-4 py-2 text-left",children:"Email"}),s.jsx("th",{className:"px-4 py-2 text-left",children:"Registered"}),s.jsx("th",{className:"px-4 py-2 text-right",children:"Actions"})]})}),s.jsx("tbody",{children:h.map((e=>s.jsxs("tr",{className:"border-b border-gray-700",children:[s.jsx("td",{className:"px-4 py-3",children:e.username}),s.jsx("td",{className:"px-4 py-3",children:e.email||"N/A"}),s.jsx("td",{className:"px-4 py-3",children:new Date(e.created_at).toLocaleDateString()}),s.jsx("td",{className:"px-4 py-3 text-right",children:s.jsx(a,{variant:"success",size:"sm",leftIcon:s.jsx(d,{size:16}),onClick:()=>(async e=>{try{N(null);const{error:s}=await supabase.from("profiles").update({is_approved:!0}).eq("id",e);if(s)throw s;j(h.filter((s=>s.id!==e))),v("User approved successfully"),setTimeout((()=>v(null)),3e3)}catch(s){N("Failed to approve user. Please try again.")}})(e.id),children:"Approve"})})]},e.id)))})]})})]})]}):s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:s.jsxs("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[s.jsx(l,{size:48,className:"text-red-500 mb-4"}),s.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Access Denied"}),s.jsx("p",{className:"text-gray-400 max-w-md",children:"You don't have permission to access the admin area."})]})})}):null};export{c as default};
