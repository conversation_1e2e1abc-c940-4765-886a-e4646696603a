#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Configuration
const SUPABASE_URL = 'https://vsnsglgyapexhwyfylic.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzbnNnbGd5YXBleGh3eWZ5bGljIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExOTEsImV4cCI6MjA2MDgzNzE5MX0.6CQWpMT14h2kaIOk1_LMECuJrfRdmiGRo3vGyEDW9tM';
const NAMECHEAP_DOMAIN = 'www.bluefilmx.com';

// SSH Configuration
const SSH_CONFIG = {
  host: '**************',
  port: '21098',
  username: 'bluerpcm',
  keyPath: '~/.ssh/namecheap_rsa'
};

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Create temp directory
const TEMP_DIR = './temp-migration';
if (!fs.existsSync(TEMP_DIR)) {
  fs.mkdirSync(TEMP_DIR, { recursive: true });
}

let stats = {
  totalVideos: 0,
  migratedVideos: 0,
  failedVideos: 0,
  totalThumbnails: 0,
  migratedThumbnails: 0,
  failedThumbnails: 0,
  updatedRecords: 0
};

/**
 * Download file from Supabase storage
 */
async function downloadFromSupabase(bucket, filePath) {
  try {
    console.log(`⬇️  Downloading ${bucket}/${filePath}...`);
    
    const { data, error } = await supabase.storage
      .from(bucket)
      .download(filePath);
    
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error(`❌ Error downloading ${filePath}:`, error.message);
    throw error;
  }
}

/**
 * Upload file to Namecheap via SCP
 */
async function uploadToNamecheap(fileBuffer, remotePath, fileName) {
  try {
    // Save to temp file
    const tempFile = path.join(TEMP_DIR, fileName);
    const arrayBuffer = await fileBuffer.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    fs.writeFileSync(tempFile, buffer);
    
    console.log(`⬆️  Uploading ${fileName} to Namecheap...`);
    
    // Create remote directory if needed
    const remoteDir = path.dirname(remotePath);
    await execAsync(`ssh -p ${SSH_CONFIG.port} -i ${SSH_CONFIG.keyPath} ${SSH_CONFIG.username}@${SSH_CONFIG.host} "mkdir -p public_html/${remoteDir}"`);
    
    // Upload file
    const scpCommand = `scp -P ${SSH_CONFIG.port} -i ${SSH_CONFIG.keyPath} "${tempFile}" ${SSH_CONFIG.username}@${SSH_CONFIG.host}:public_html/${remotePath}`;
    await execAsync(scpCommand);
    
    // Clean up temp file
    fs.unlinkSync(tempFile);
    
    const newUrl = `http://${NAMECHEAP_DOMAIN}/${remotePath}`;
    console.log(`✅ Uploaded: ${newUrl}`);
    
    return newUrl;
  } catch (error) {
    console.error(`❌ Error uploading ${fileName}:`, error.message);
    throw error;
  }
}

/**
 * Update database URL
 */
async function updateDatabaseUrl(videoId, field, newUrl) {
  try {
    const updateData = {};
    updateData[field] = newUrl;
    
    const { data, error } = await supabase
      .from('videos')
      .update(updateData)
      .eq('id', videoId)
      .select();
    
    if (error) {
      throw error;
    }
    
    console.log(`🔄 Updated ${field} for video ${videoId}`);
    return data;
  } catch (error) {
    console.error(`❌ Error updating database:`, error.message);
    throw error;
  }
}

/**
 * Extract file path from Supabase URL
 */
function extractFilePathFromUrl(url) {
  if (!url) return null;
  
  // Clean URL (remove %0A characters)
  const cleanUrl = url.replace(/%0A/g, '');
  
  // Extract path after /storage/v1/object/public/bucket/
  const match = cleanUrl.match(/\/storage\/v1\/object\/public\/([^\/]+)\/(.+)$/);
  if (match) {
    return {
      bucket: match[1],
      filePath: match[2]
    };
  }
  
  return null;
}

/**
 * Migrate a single video
 */
async function migrateVideo(video) {
  console.log(`\n📹 Migrating: ${video.title}`);
  console.log('=' .repeat(50));
  
  try {
    // Migrate video file
    if (video.video_url && video.video_url.includes('supabase')) {
      const videoInfo = extractFilePathFromUrl(video.video_url);
      if (videoInfo) {
        const videoBlob = await downloadFromSupabase(videoInfo.bucket, videoInfo.filePath);
        const videoFileName = path.basename(videoInfo.filePath);
        const remoteVideoPath = `media/videos/${videoInfo.filePath}`;
        const newVideoUrl = await uploadToNamecheap(videoBlob, remoteVideoPath, videoFileName);
        
        await updateDatabaseUrl(video.id, 'video_url', newVideoUrl);
        stats.migratedVideos++;
      }
    }
    
    // Migrate thumbnail file
    if (video.thumbnail_url && video.thumbnail_url.includes('supabase')) {
      const thumbInfo = extractFilePathFromUrl(video.thumbnail_url);
      if (thumbInfo) {
        const thumbBlob = await downloadFromSupabase(thumbInfo.bucket, thumbInfo.filePath);
        const thumbFileName = path.basename(thumbInfo.filePath);
        const remoteThumbPath = `media/thumbnails/${thumbInfo.filePath}`;
        const newThumbUrl = await uploadToNamecheap(thumbBlob, remoteThumbPath, thumbFileName);
        
        await updateDatabaseUrl(video.id, 'thumbnail_url', newThumbUrl);
        stats.migratedThumbnails++;
      }
    }
    
    stats.updatedRecords++;
    console.log(`✅ Successfully migrated: ${video.title}`);
    
  } catch (error) {
    console.error(`❌ Failed to migrate: ${video.title}`, error.message);
    stats.failedVideos++;
  }
}

/**
 * Main migration function
 */
async function runMigration() {
  console.log('🚀 Starting Storage Migration: Supabase → Namecheap');
  console.log('=' .repeat(60));
  
  try {
    // Get all videos with Supabase URLs
    const { data: videos, error } = await supabase
      .from('videos')
      .select('id, title, video_url, thumbnail_url')
      .ilike('video_url', '%supabase%');
    
    if (error) {
      throw error;
    }
    
    stats.totalVideos = videos.length;
    stats.totalThumbnails = videos.filter(v => v.thumbnail_url && v.thumbnail_url.includes('supabase')).length;
    
    console.log(`📊 Found ${stats.totalVideos} videos to migrate`);
    console.log(`📊 Found ${stats.totalThumbnails} thumbnails to migrate`);
    
    // Migrate each video
    for (let i = 0; i < videos.length; i++) {
      const video = videos[i];
      console.log(`\n[${i + 1}/${videos.length}] Processing: ${video.title}`);
      await migrateVideo(video);
      
      // Small delay to avoid overwhelming the servers
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Print summary
    console.log('\n' + '=' .repeat(60));
    console.log('📊 Migration Summary:');
    console.log('=' .repeat(60));
    console.log(`📹 Videos: ${stats.migratedVideos}/${stats.totalVideos} migrated (${stats.failedVideos} failed)`);
    console.log(`🖼️  Thumbnails: ${stats.migratedThumbnails}/${stats.totalThumbnails} migrated`);
    console.log(`🔄 Database records updated: ${stats.updatedRecords}`);
    console.log('=' .repeat(60));
    
    if (stats.failedVideos === 0) {
      console.log('🎉 Migration completed successfully!');
    } else {
      console.log('⚠️  Migration completed with some failures. Check logs above.');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    // Cleanup
    if (fs.existsSync(TEMP_DIR)) {
      fs.rmSync(TEMP_DIR, { recursive: true, force: true });
    }
  }
}

// Run migration
runMigration();
