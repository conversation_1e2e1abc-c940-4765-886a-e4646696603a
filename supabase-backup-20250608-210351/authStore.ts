import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { User } from '@supabase/supabase-js';

interface Profile {
  id: string;
  username: string;
  avatar_url?: string;
  is_approved: boolean;
  created_at?: string;
  updated_at?: string;
}

interface AuthState {
  user: User | null;
  profile: Profile | null;
  isLoading: boolean;
  isApproved: boolean;
  signUp: (email: string, password: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  loadUser: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  profile: null,
  isLoading: true,
  isApproved: false,

  signUp: async (email: string, password: string) => {
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
    });

    if (authError) throw authError;

    if (authData.user) {
      // Create profile with is_approved set to false by default
      const username = email.split('@')[0];
      const { error: profileError } = await supabase
        .from('profiles')
        .insert([{
          id: authData.user.id,
          username: username,
          is_approved: false
        }]);

      if (profileError) throw profileError;

      // Set the user and fetch profile after successful signup
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authData.user.id)
        .single();

      set({
        user: authData.user,
        profile,
        isApproved: profile?.is_approved || false
      });
    }
  },

  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error('Sign in error:', error);
      throw error;
    }

    if (data.user) {
      // Check if profile exists
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single();

      // If profile doesn't exist, create one
      if (profileError && profileError.code === 'PGRST116') {
        console.log('🆕 Profile not found, creating new profile...');

        // Profile doesn't exist, create it
        const username = email.split('@')[0];
        const { data: newProfile, error: insertError } = await supabase
          .from('profiles')
          .insert([{
            id: data.user.id,
            username: username,
            is_approved: false // New users are not approved by default
          }])
          .select()
          .single();

        console.log('📝 Profile creation result:', { newProfile, insertError });

        if (insertError) {
          console.error('❌ Error creating profile:', insertError);
          // Continue anyway, as the trigger might create the profile
        }

        // Try to fetch the profile again
        console.log('🔄 Retrying profile fetch...');
        const { data: retryProfile, error: retryError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', data.user.id)
          .single();

        console.log('🔄 Retry profile fetch result:', { retryProfile, retryError });

        const finalProfile = retryProfile || newProfile;
        console.log('✅ Final profile for new user:', finalProfile);

        set({
          user: data.user,
          profile: finalProfile,
          isApproved: finalProfile?.is_approved || false,
          isLoading: false
        });
      } else if (profileError) {
        console.error('❌ Unexpected profile error:', profileError);
        // Set user but no profile
        set({
          user: data.user,
          profile: null,
          isApproved: false,
          isLoading: false
        });
      } else {
        // Profile exists
        console.log('✅ Profile found for existing user:', profile);
        set({
          user: data.user,
          profile,
          isApproved: profile?.is_approved || false,
          isLoading: false
        });
      }
    }

    console.log('🎯 Sign in process completed');
  },

  signOut: async () => {
    try {
      console.log('Starting sign out process...');

      // Clear local state first for immediate UI feedback
      set({ user: null, profile: null, isApproved: false, isLoading: false });

      // Then sign out from Supabase
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('Supabase signOut error:', error);
        throw error;
      }

      console.log('Sign out completed successfully');
    } catch (error) {
      console.error('Error during sign out:', error);
      // Even if Supabase signOut fails, keep local state cleared
      set({ user: null, profile: null, isApproved: false, isLoading: false });
      throw error;
    }
  },

  loadUser: async () => {
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('loadUser timeout after 10 seconds')), 10000);
      });

      // First check the session with timeout
      const sessionResult = await Promise.race([
        supabase.auth.getSession(),
        timeoutPromise
      ]);

      const { data: { session } } = sessionResult as any;

      if (!session) {
        console.log('❌ No session found, clearing user state');
        set({ user: null, profile: null, isLoading: false, isApproved: false });
        return;
      }

      console.log('👤 About to get user...');
      const userResult = await Promise.race([
        supabase.auth.getUser(),
        timeoutPromise
      ]);

      const { data: { user } } = userResult as any;
      console.log('👤 User from session:', { userId: user?.id, email: user?.email });

      if (user) {
        console.log('🔍 Fetching profile for user:', user.id);

        // Try to get the profile with timeout
        console.log('📋 About to fetch profile...');
        const profileResult = await Promise.race([
          supabase.from('profiles').select('*').eq('id', user.id).single(),
          timeoutPromise
        ]);

        const { data: profile, error: profileError } = profileResult as any;
        console.log('📋 Profile fetch in loadUser:', { profile, profileError });

        // If profile doesn't exist, create one
        if (profileError && profileError.code === 'PGRST116') {
          console.log('🆕 Profile not found in loadUser, creating one...');

          // Create a username from the email
          const username = user.email ? user.email.split('@')[0] : `user_${Date.now()}`;

          // Try to create the profile
          const { data: newProfile, error: insertError } = await supabase
            .from('profiles')
            .insert([{
              id: user.id,
              username: username,
              is_approved: false // New users are not approved by default
            }])
            .select()
            .single();

          console.log('📝 Profile creation in loadUser:', { newProfile, insertError });

          if (insertError) {
            console.error('❌ Error creating profile in loadUser:', insertError);
            // Continue anyway, as the trigger might create the profile
          }

          // Try to fetch the profile again
          console.log('🔄 Retrying profile fetch in loadUser...');
          const { data: retryProfile, error: retryError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();

          console.log('🔄 Retry result in loadUser:', { retryProfile, retryError });

          const finalProfile = retryProfile || newProfile;
          console.log('✅ Setting final profile in loadUser:', finalProfile);

          set({
            user,
            profile: finalProfile,
            isLoading: false,
            isApproved: finalProfile?.is_approved || false
          });
        } else if (profileError) {
          console.error('❌ Unexpected profile error in loadUser:', profileError);
          set({
            user,
            profile: null,
            isLoading: false,
            isApproved: false
          });
        } else {
          // Profile exists
          console.log('✅ Profile found in loadUser:', profile);
          set({
            user,
            profile,
            isLoading: false,
            isApproved: profile?.is_approved || false
          });
        }
      } else {
        console.log('❌ No user found, clearing state');
        set({ user: null, profile: null, isLoading: false, isApproved: false });
      }
    } catch (error) {
      console.error('❌ Error loading user:', error);
      set({ user: null, profile: null, isLoading: false, isApproved: false });
    }
  },
}));

// Set up authentication state listener with mobile-specific handling
supabase.auth.onAuthStateChange(async (event, session) => {
  console.log('Auth state changed:', event, session?.user?.id);

  const { loadUser } = useAuthStore.getState();

  // For mobile devices, add a small delay to prevent rapid state changes
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  if (event === 'SIGNED_OUT' || !session) {
    // User signed out or session expired
    const updateState = () => {
      useAuthStore.setState({
        user: null,
        profile: null,
        isApproved: false,
        isLoading: false
      });
    };

    if (isMobile) {
      // Add a small delay on mobile to prevent race conditions
      setTimeout(updateState, 100);
    } else {
      updateState();
    }
  } else if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
    // User signed in or token refreshed
    try {
      if (isMobile) {
        // Add a small delay on mobile to ensure session is stable
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      await loadUser();
    } catch (error) {
      console.error('Error loading user after auth state change:', error);
      // Don't clear the state on mobile if there's a temporary error
      if (!isMobile) {
        useAuthStore.setState({
          user: null,
          profile: null,
          isApproved: false,
          isLoading: false
        });
      }
    }
  }
});