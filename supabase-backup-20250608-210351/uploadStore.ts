import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { useAuthStore } from './authStore';
import { uploadFileInChunks, validateFileForUpload, checkNetworkConditions, isMobileDevice } from '../utils/uploadUtils';

// Helper function to check if a bucket exists
const checkBucketExists = async (bucketName: string): Promise<boolean> => {
  try {
    // Try to list files in the bucket - if it works, the bucket exists
    const { data, error } = await supabase.storage.from(bucketName).list();

    if (error && error.message.includes('bucket not found')) {
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error checking if bucket ${bucketName} exists:`, error);
    // Assume bucket doesn't exist if we can't check
    return false;
  }
};

interface UploadVideoParams {
  title: string;
  description: string;
  category: string;
  tags: string[];
  videoFile: File;
  thumbnailFile: File | null;
}

interface UploadState {
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
  uploadVideo: (params: UploadVideoParams) => Promise<string | null>;
}

export const useUploadStore = create<UploadState>((set, get) => ({
  isUploading: false,
  uploadProgress: 0,
  error: null,

  uploadVideo: async (params: UploadVideoParams) => {
    const {
      title,
      description,
      category,
      tags,
      videoFile,
      thumbnailFile
    } = params;
    const { user } = useAuthStore.getState();

    if (!user) {
      set({ error: 'You must be logged in to upload videos' });
      return null;
    }

    console.log('User ID for upload:', user.id);

    // Check network conditions first
    const networkConditions = checkNetworkConditions();
    if (!networkConditions.isOnline) {
      set({ error: 'No internet connection. Please check your network and try again.' });
      return null;
    }

    // Validate files with mobile-specific checks
    const videoValidation = validateFileForUpload(videoFile);
    if (!videoValidation.isValid) {
      set({ error: videoValidation.error || 'Invalid video file' });
      return null;
    }

    // Check thumbnail file if provided
    if (thumbnailFile) {
      const THUMBNAIL_MAX_SIZE = 5 * 1024 * 1024; // 5MB
      if (thumbnailFile.size > THUMBNAIL_MAX_SIZE) {
        set({ error: `Thumbnail file is too large. Maximum size is 5MB. Your file is ${(thumbnailFile.size / (1024 * 1024)).toFixed(2)}MB.` });
        return null;
      }
    }

    // Show warning for slow connections
    if (networkConditions.isSlowConnection) {
      console.warn('Slow connection detected, upload may take longer');
    }

    set({ isUploading: true, uploadProgress: 0, error: null });

    try {
      // Check if buckets exist
      const videoBucketExists = await checkBucketExists('videos');
      const thumbnailBucketExists = await checkBucketExists('thumbnails');

      // If buckets don't exist, show an error message
      if (!videoBucketExists || !thumbnailBucketExists) {
        throw new Error(
          "Storage buckets don't exist. Please contact the administrator to set up the required storage buckets."
        );
      }

      set({ uploadProgress: 5 });

      // 1. Upload video file to storage
      // Sanitize the file name to avoid issues with special characters
      const sanitizedFileName = videoFile.name
        .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
        .replace(/_{2,}/g, '_') // Replace multiple underscores with single
        .toLowerCase();

      const videoFileName = `${Date.now()}-${sanitizedFileName}`;
      // Make sure the path format matches what the RLS policy expects
      // The policy expects the first folder to be the user ID
      const videoPath = `${user.id}/${videoFileName}`;

      console.log('Generated video path:', videoPath);
      console.log('Original filename:', videoFile.name);
      console.log('Sanitized filename:', sanitizedFileName);

      // Try to upload with optimized settings for mobile
      try {
        console.log('Uploading video to path:', videoPath);
        console.log('Video file details:', {
          name: videoFile.name,
          size: videoFile.size,
          type: videoFile.type
        });

        // Check authentication state before upload
        const { user: currentUser } = useAuthStore.getState();
        if (!currentUser) {
          throw new Error('Authentication lost during upload preparation');
        }

        const isMobile = isMobileDevice();
        const isLargeFile = videoFile.size > 10 * 1024 * 1024; // 10MB threshold

        let videoUrl: any;

        // Use chunked upload for large files on mobile devices
        if (isMobile && isLargeFile) {
          console.log('Using optimized upload for mobile device with large file');

          videoUrl = await uploadFileInChunks(
            videoFile,
            videoPath,
            (progress) => {
              const percentage = Math.round(progress * 75);
              set({ uploadProgress: 5 + percentage });
            }
          );
        } else {
          // Standard upload for smaller files or desktop
          const uploadOptions: any = {
            cacheControl: '3600',
            upsert: false,
          };

          // Add content type explicitly for better compatibility
          if (videoFile.type) {
            uploadOptions.contentType = videoFile.type;
          }

          const { error: videoUploadError, data: videoData } = await supabase.storage
            .from('videos')
            .upload(videoPath, videoFile, uploadOptions);

          if (videoUploadError) {
            throw new Error(`Video upload failed: ${videoUploadError.message}`);
          }

          // Get the public URL for the uploaded video
          const { data: urlData } = supabase.storage
            .from('videos')
            .getPublicUrl(videoPath);

          videoUrl = urlData;
        }
      } catch (error) {
        if (error instanceof Error) {
          throw error; // Re-throw our custom errors
        } else {
          throw new Error(`Unexpected error uploading video: ${String(error)}`);
        }
      }

      // Get the public URL for the video
      const { data: videoUrl } = supabase.storage
        .from('videos')
        .getPublicUrl(videoPath);

      // 2. Upload thumbnail if provided, or use a default
      let thumbnailUrl = null;

      if (thumbnailFile) {
        // Sanitize the thumbnail file name to avoid issues with special characters
        const sanitizedThumbnailName = thumbnailFile.name
          .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
          .replace(/_{2,}/g, '_') // Replace multiple underscores with single
          .toLowerCase();

        const thumbnailFileName = `${Date.now()}-${sanitizedThumbnailName}`;
        // Make sure the path format matches what the RLS policy expects
        // The policy expects the first folder to be the user ID
        const thumbnailPath = `${user.id}/${thumbnailFileName}`;

        console.log('Generated thumbnail path:', thumbnailPath);

        let thumbnailUploaded = false;

        try {
          console.log('Uploading thumbnail to path:', thumbnailPath);
          const { error: thumbnailUploadError } = await supabase.storage
            .from('thumbnails')
            .upload(thumbnailPath, thumbnailFile, {
              cacheControl: '3600',
              upsert: false,
            });

          if (thumbnailUploadError) {
            // Handle specific error cases but don't fail the whole upload
            console.error(`Error uploading thumbnail: ${thumbnailUploadError.message}`);
            // Continue without thumbnail instead of failing
          } else {
            thumbnailUploaded = true;
          }
        } catch (thumbnailError) {
          // Log thumbnail error but continue with the upload
          console.error('Thumbnail upload failed:', thumbnailError);
          // We'll continue without the thumbnail
        }

        // Only get the URL if the thumbnail was successfully uploaded
        if (thumbnailUploaded) {
          const { data: thumbnailUrlData } = supabase.storage
            .from('thumbnails')
            .getPublicUrl(thumbnailPath);

          thumbnailUrl = thumbnailUrlData.publicUrl;
        }
      }

      set({ uploadProgress: 90 });

      // 3. Insert video record in the database
      try {
        // Create the base video record data
        const videoData: any = {
          title,
          description,
          video_url: videoUrl.publicUrl,
          thumbnail_url: thumbnailUrl,
          user_id: user.id,
          category,
          is_hd: true, // Assuming all uploads are HD for now
        };

        // Insert the video record
        const { data: videoRecord, error: insertError } = await supabase
          .from('videos')
          .insert(videoData)
          .select()
          .single();

        if (insertError) {
          // Check for foreign key violation
          if (insertError.message.includes('foreign key constraint') && insertError.message.includes('user_id')) {
            throw new Error(
              'Your user profile does not exist in the database. Please contact the administrator to set up your profile.'
            );
          }

          console.error('Error inserting video record:', insertError);
          throw new Error(`Error saving video: ${insertError.message}`);
        }

        const videoId = videoRecord.id;

        // 4. Insert tags if provided
        if (tags.length > 0 && videoId) {
          const tagObjects = tags.map(tag => ({
            video_id: videoId,
            tag
          }));

          const { error: tagsError } = await supabase
            .from('video_tags')
            .insert(tagObjects);

          if (tagsError) {
            console.error('Error saving tags:', tagsError);
            // Continue even if tags fail
          }
        }



        return videoId;
      } catch (error) {
        if (error instanceof Error) {
          throw error; // Re-throw our custom errors
        } else {
          throw new Error(`Unexpected error saving video: ${String(error)}`);
        }
      }

      // The return statement is now inside the try/catch block above
      set({ uploadProgress: 100 });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      console.error('Upload error:', error);
      set({ error: errorMessage });
      return null;
    } finally {
      // Reset state after a delay to show 100% completion
      setTimeout(() => {
        set({ isUploading: false, uploadProgress: 0 });
      }, 1000);
    }
  },
}));
