import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/supabase';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Validate environment variables
if (!supabaseUrl) {
  console.error('Missing VITE_SUPABASE_URL environment variable');
  throw new Error('Missing VITE_SUPABASE_URL environment variable');
}

if (!supabaseAnonKey) {
  console.error('Missing VITE_SUPABASE_ANON_KEY environment variable');
  throw new Error('Missing VITE_SUPABASE_ANON_KEY environment variable');
}

// Supabase configuration validated

// Create a singleton instance of the Supabase client
let supabaseInstance: SupabaseClient<Database> | null = null;

// Function to get the Supabase client instance
export const getSupabaseClient = (): SupabaseClient<Database> => {
  if (supabaseInstance) {
    return supabaseInstance;
  }

  // Create a new client with optimized settings
  supabaseInstance = createClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
    global: {
      // Limit concurrent connections
      headers: {
        'X-Client-Info': 'supabase-js/2.x',
      },
    },
    // Configure fetch behavior for better performance and timeout handling
    fetch: (url, options) => {
      // Create abort controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.warn('⏰ Supabase request timeout after 15 seconds:', url);
        controller.abort();
      }, 15000); // 15 second timeout

      const fetchOptions = {
        ...options,
        signal: controller.signal,
        // Add cache control headers for better caching
        headers: {
          ...options?.headers,
          'Cache-Control': 'no-cache',
        },
      };

      return fetch(url, fetchOptions).finally(() => {
        clearTimeout(timeoutId);
      });
    },
  });

  return supabaseInstance;
};

// Export the client for backward compatibility
export const supabase = getSupabaseClient();

// Helper function to standardize video queries with user information
export const getVideoQuery = (query: any) => {
  return query.select(`
    id,
    title,
    description,
    thumbnail_url,
    video_url,
    duration,
    views,
    likes,
    is_hd,
    user_id,
    created_at,
    updated_at,
    category,
    is_part_of_multi_upload,
    is_first_in_multi_upload,
    total_parts_in_multi_upload,
    tags,
    creator:profiles(id, username, avatar_url)
  `);
};