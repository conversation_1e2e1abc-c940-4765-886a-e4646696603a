import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const CACHE_TTL = 300 // 5 minutes in seconds
const STALE_WHILE_REVALIDATE = 60 // 1 minute in seconds

interface RequestParams {
  limit?: number
  offset?: number
  last_views?: number
  last_id?: string
  use_keyset?: boolean
}

serve(async (req) => {
  // Parse request
  const url = new URL(req.url)
  const params: RequestParams = {}
  
  // Get query parameters
  params.limit = parseInt(url.searchParams.get('limit') || '10')
  params.offset = parseInt(url.searchParams.get('offset') || '0')
  params.last_views = url.searchParams.get('last_views') ? 
    parseInt(url.searchParams.get('last_views')!) : undefined
  params.last_id = url.searchParams.get('last_id') || undefined
  params.use_keyset = url.searchParams.get('use_keyset') === 'true'
  
  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )
  
  try {
    let result
    
    if (params.use_keyset && params.last_views !== undefined && params.last_id !== undefined) {
      // Use keyset pagination
      const { data, error } = await supabaseClient.rpc(
        'get_top_videos_keyset',
        {
          last_views: params.last_views,
          last_id: params.last_id,
          page_size: params.limit
        }
      )
      
      if (error) throw error
      result = data
    } else {
      // Use offset pagination with materialized view
      const { data, error } = await supabaseClient.rpc(
        'get_top_videos_with_profiles',
        {
          video_limit: params.limit,
          video_offset: params.offset
        }
      )
      
      if (error) {
        // Try fallback if materialized view query fails
        console.error('Materialized view query failed, using fallback:', error.message)
        const { data: fallbackData, error: fallbackError } = await supabaseClient.rpc(
          'get_top_videos_fallback',
          {
            video_limit: params.limit,
            video_offset: params.offset
          }
        )
        
        if (fallbackError) throw fallbackError
        result = fallbackData
      } else {
        result = data
      }
    }
    
    // Set cache headers
    const headers = new Headers()
    headers.set('Content-Type', 'application/json')
    headers.set('Cache-Control', `public, max-age=${CACHE_TTL}, stale-while-revalidate=${STALE_WHILE_REVALIDATE}`)
    
    // Generate ETag based on content
    const etag = await generateETag(JSON.stringify(result))
    headers.set('ETag', etag)
    
    // Check if client has a cached version
    const ifNoneMatch = req.headers.get('if-none-match')
    if (ifNoneMatch === etag) {
      return new Response(null, { status: 304, headers })
    }
    
    return new Response(
      JSON.stringify(result),
      { headers }
    )
  } catch (error) {
    console.error('Error fetching top videos:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500 }
    )
  }
})

// Helper function to generate ETag
async function generateETag(content: string): Promise<string> {
  const msgUint8 = new TextEncoder().encode(content)
  const hashBuffer = await crypto.subtle.digest('SHA-256', msgUint8)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  return `"${hashHex}"`
}
