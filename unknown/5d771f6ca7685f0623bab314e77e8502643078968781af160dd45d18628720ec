#!/usr/bin/env node

/**
 * Complete Storage Migration: Supabase → Namecheap
 * Migrates all videos and thumbnails from Supabase storage to Namecheap hosting
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import https from 'https';
import http from 'http';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const SUPABASE_URL = 'https://vsnsglgyapexhwyfylic.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzbnNnbGd5YXBleGh3eWZ5bGljIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExOTEsImV4cCI6MjA2MDgzNzE5MX0.6CQWpMT14h2kaIOk1_LMECuJrfRdmiGRo3vGyEDW9tM';
const NAMECHEAP_DOMAIN = 'www.bluefilmx.com';

// SSH Configuration
const SSH_CONFIG = {
  host: '**************',
  port: 21098,
  username: 'bluerpcm',
  keyPath: '~/.ssh/namecheap_rsa'
};

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Migration state
let migrationStats = {
  totalVideos: 0,
  totalThumbnails: 0,
  migratedVideos: 0,
  migratedThumbnails: 0,
  failedVideos: 0,
  failedThumbnails: 0,
  updatedRecords: 0
};

/**
 * Step 1: List all files in Supabase storage
 */
async function listSupabaseFiles() {
  console.log('🔍 Scanning Supabase storage...');
  
  try {
    // List videos
    const { data: videoFiles, error: videoError } = await supabase.storage
      .from('videos')
      .list('', { limit: 1000, sortBy: { column: 'name', order: 'asc' } });
    
    if (videoError) {
      console.error('❌ Error listing videos:', videoError);
      return null;
    }

    // List thumbnails
    const { data: thumbnailFiles, error: thumbnailError } = await supabase.storage
      .from('thumbnails')
      .list('', { limit: 1000, sortBy: { column: 'name', order: 'asc' } });
    
    if (thumbnailError) {
      console.error('❌ Error listing thumbnails:', thumbnailError);
      return null;
    }

    migrationStats.totalVideos = videoFiles?.length || 0;
    migrationStats.totalThumbnails = thumbnailFiles?.length || 0;

    console.log(`📊 Found ${migrationStats.totalVideos} videos and ${migrationStats.totalThumbnails} thumbnails`);
    
    return { videoFiles, thumbnailFiles };
  } catch (error) {
    console.error('❌ Error scanning Supabase storage:', error);
    return null;
  }
}

/**
 * Step 2: Download file from Supabase
 */
async function downloadFromSupabase(bucket, filePath) {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .download(filePath);
    
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error(`❌ Error downloading ${filePath} from ${bucket}:`, error);
    throw error;
  }
}

/**
 * Step 3: Upload file to Namecheap via SSH
 */
async function uploadToNamecheap(fileBuffer, remotePath, fileName) {
  // For now, we'll save locally and use SCP
  const tempDir = path.join(__dirname, 'temp-migration');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }
  
  const localPath = path.join(tempDir, fileName);
  fs.writeFileSync(localPath, fileBuffer);
  
  // Use SCP to upload
  const { exec } = await import('child_process');
  const { promisify } = await import('util');
  const execAsync = promisify(exec);
  
  try {
    const scpCommand = `scp -P ${SSH_CONFIG.port} -i ${SSH_CONFIG.keyPath} "${localPath}" ${SSH_CONFIG.username}@${SSH_CONFIG.host}:public_html/${remotePath}`;
    await execAsync(scpCommand);
    
    // Clean up local file
    fs.unlinkSync(localPath);
    
    return `http://${NAMECHEAP_DOMAIN}/${remotePath}`;
  } catch (error) {
    console.error(`❌ Error uploading ${fileName}:`, error);
    throw error;
  }
}

/**
 * Step 4: Update database URLs
 */
async function updateDatabaseUrls(oldUrl, newUrl) {
  try {
    // Update video URLs
    const { data: videoUpdates, error: videoError } = await supabase
      .from('videos')
      .update({ video_url: newUrl })
      .eq('video_url', oldUrl)
      .select();
    
    // Update thumbnail URLs
    const { data: thumbnailUpdates, error: thumbnailError } = await supabase
      .from('videos')
      .update({ thumbnail_url: newUrl })
      .eq('thumbnail_url', oldUrl)
      .select();
    
    if (videoError) console.error('❌ Error updating video URLs:', videoError);
    if (thumbnailError) console.error('❌ Error updating thumbnail URLs:', thumbnailError);
    
    return (videoUpdates?.length || 0) + (thumbnailUpdates?.length || 0);
  } catch (error) {
    console.error('❌ Error updating database:', error);
    return 0;
  }
}

/**
 * Main migration function
 */
async function runMigration() {
  console.log('🚀 Starting Storage Migration: Supabase → Namecheap');
  console.log('=' .repeat(60));
  
  // Step 1: List files
  const files = await listSupabaseFiles();
  if (!files) {
    console.error('❌ Failed to list files. Aborting migration.');
    return;
  }
  
  const { videoFiles, thumbnailFiles } = files;
  
  // Step 2: Migrate videos
  console.log('\n📹 Migrating videos...');
  for (const file of videoFiles || []) {
    try {
      console.log(`⬇️  Downloading video: ${file.name}`);
      const fileBuffer = await downloadFromSupabase('videos', file.name);
      
      console.log(`⬆️  Uploading video: ${file.name}`);
      const newUrl = await uploadToNamecheap(fileBuffer, `media/videos/${file.name}`, file.name);
      
      console.log(`🔄 Updating database for: ${file.name}`);
      const oldUrl = `${SUPABASE_URL}/storage/v1/object/public/videos/${file.name}`;
      const updatedRecords = await updateDatabaseUrls(oldUrl, newUrl);
      
      migrationStats.migratedVideos++;
      migrationStats.updatedRecords += updatedRecords;
      
      console.log(`✅ Migrated video: ${file.name} (${updatedRecords} records updated)`);
    } catch (error) {
      console.error(`❌ Failed to migrate video: ${file.name}`, error);
      migrationStats.failedVideos++;
    }
  }
  
  // Step 3: Migrate thumbnails
  console.log('\n🖼️  Migrating thumbnails...');
  for (const file of thumbnailFiles || []) {
    try {
      console.log(`⬇️  Downloading thumbnail: ${file.name}`);
      const fileBuffer = await downloadFromSupabase('thumbnails', file.name);
      
      console.log(`⬆️  Uploading thumbnail: ${file.name}`);
      const newUrl = await uploadToNamecheap(fileBuffer, `media/thumbnails/${file.name}`, file.name);
      
      console.log(`🔄 Updating database for: ${file.name}`);
      const oldUrl = `${SUPABASE_URL}/storage/v1/object/public/thumbnails/${file.name}`;
      const updatedRecords = await updateDatabaseUrls(oldUrl, newUrl);
      
      migrationStats.migratedThumbnails++;
      migrationStats.updatedRecords += updatedRecords;
      
      console.log(`✅ Migrated thumbnail: ${file.name} (${updatedRecords} records updated)`);
    } catch (error) {
      console.error(`❌ Failed to migrate thumbnail: ${file.name}`, error);
      migrationStats.failedThumbnails++;
    }
  }
  
  // Step 4: Print summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Migration Summary:');
  console.log('=' .repeat(60));
  console.log(`📹 Videos: ${migrationStats.migratedVideos}/${migrationStats.totalVideos} migrated (${migrationStats.failedVideos} failed)`);
  console.log(`🖼️  Thumbnails: ${migrationStats.migratedThumbnails}/${migrationStats.totalThumbnails} migrated (${migrationStats.failedThumbnails} failed)`);
  console.log(`🔄 Database records updated: ${migrationStats.updatedRecords}`);
  console.log('=' .repeat(60));
  
  if (migrationStats.failedVideos === 0 && migrationStats.failedThumbnails === 0) {
    console.log('🎉 Migration completed successfully!');
  } else {
    console.log('⚠️  Migration completed with some failures. Check logs above.');
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigration().catch(console.error);
}

export { runMigration, listSupabaseFiles, migrationStats };
