{"rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/:path*\\.(jpg|jpeg|gif|png|svg|webp)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}, {"source": "/assets/:path*\\.(js|css)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}, {"source": "/assets/:path*\\.(woff|woff2|ttf|otf|eot)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}, {"source": "/api/:path*", "headers": [{"key": "Cache-Control", "value": "public, s-maxage=60, stale-while-revalidate=600"}]}, {"source": "/index.html", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "/sw-unregister.js", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "/:path*", "headers": [{"key": "Cache-Control", "value": "public, s-maxage=60, stale-while-revalidate=300"}]}], "buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite"}