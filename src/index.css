@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .mask-fade-right {
    -webkit-mask-image: linear-gradient(to right, black 90%, transparent 100%);
    mask-image: linear-gradient(to right, black 90%, transparent 100%);
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .z-60 {
    z-index: 60;
  }

  /* Custom scrollbar styles */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.5) rgba(31, 41, 55, 0.5);
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 130, 246, 0.5);
    border-radius: 10px;
    border: 2px solid transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 130, 246, 0.8);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

body {
  background-color: #121212;
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* Ensure videos maintain aspect ratio */
.aspect-video {
  aspect-ratio: 16 / 9;
}

/* Fullscreen video styles */
:fullscreen .video-container,
:-webkit-full-screen .video-container,
:-moz-full-screen .video-container,
:-ms-fullscreen .video-container {
  width: 100%;
  height: 100%;
  max-height: none;
  background-color: black;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  position: relative;
}

:fullscreen video,
:-webkit-full-screen video,
:-moz-full-screen video,
:-ms-fullscreen video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: black;
  z-index: 9999;
  position: relative;
}

/* Native video player styles */
video {
  width: 100%;
  height: auto;
  max-height: 70vh;
  /* Prevent blue highlight on tap in mobile browsers */
  -webkit-tap-highlight-color: transparent;
  tap-highlight-color: transparent;
}

/* Specific styles for our standardized native video player */
.native-video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: black;
  /* Ensure consistent behavior across platforms */
  -webkit-user-select: none;
  user-select: none;
  /* Fix for iOS Safari */
  position: relative;
  z-index: 1;
}

/* Ensure video controls are visible and accessible */
video::-webkit-media-controls {
  z-index: 2147483647;
  opacity: 1 !important;
  display: flex !important;
}

/* Style the control panel */
video::-webkit-media-controls-panel {
  background-color: rgba(0, 0, 0, 0.7);
  /* Ensure consistent size across platforms */
  min-height: 40px;
}

/* Style the play button */
video::-webkit-media-controls-play-button {
  color: #3b82f6;
  /* Increase touch target size */
  min-width: 40px;
  min-height: 40px;
}

/* Let the browser handle native video control styling */

/* Hide scrollbar for category tabs on mobile */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* iOS specific fixes */
@supports (-webkit-touch-callout: none) {
  .native-video-player {
    /* Fix for iOS Safari fullscreen */
    position: relative;
    z-index: 2;
    /* Fix for iOS Safari video controls */
    -webkit-appearance: none;
  }

  /* Fix for iOS Safari video controls size */
  video::-webkit-media-controls-panel {
    min-height: 44px;
  }
}

/* Android specific fixes */
@supports not (-webkit-touch-callout: none) {
  .native-video-player {
    /* Fix for Android Chrome fullscreen */
    position: relative;
    z-index: 1;
  }
}

/* Specific Android video player styles */
.android-video-container {
  position: relative;
  overflow: visible !important;
}

.android-native-player {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  background-color: black !important;
  z-index: 2 !important;
}

/* Make sure video controls are visible */
video::-webkit-media-controls {
  display: flex !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fix for fullscreen */
:fullscreen video,
:-webkit-full-screen video,
:-moz-full-screen video {
  width: 100vw !important;
  height: 100vh !important;
  object-fit: contain !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 9999 !important;
}

/* Ensure video is responsive */
@media (max-width: 768px) {
  video {
    max-height: 50vh;
  }

  /* Increase touch targets on mobile */
  video::-webkit-media-controls-play-button,
  video::-webkit-media-controls-mute-button,
  video::-webkit-media-controls-fullscreen-button {
    min-width: 44px;
    min-height: 44px;
  }

  /* Make progress bar easier to grab on mobile */
  video::-webkit-media-controls-timeline {
    height: 10px !important;
  }
}

/* Mobile tap highlight removal */
.no-tap-highlight {
  -webkit-tap-highlight-color: transparent;
  tap-highlight-color: transparent;
}

/* Double-tap hint animation */
@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.animate-pulse {
  animation: pulse 2s infinite ease-in-out;
}

/* Search bar glow animation */
@keyframes searchGlow {
  0% {
    box-shadow: 0 0 0 rgba(37, 99, 235, 0);
  }
  50% {
    box-shadow: 0 0 10px rgba(37, 99, 235, 0.5);
  }
  100% {
    box-shadow: 0 0 0 rgba(37, 99, 235, 0);
  }
}

.search-glow {
  animation: searchGlow 2s infinite ease-in-out;
}

/* Mobile search bar optimizations */
@media (max-width: 768px) {
  .search-glow {
    box-shadow: none;
    animation: none; /* Disable glow animation on mobile */
  }

  /* Increase touch targets for search buttons */
  .search-button-mobile {
    min-width: 24px;
    min-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Make search bar more compact on mobile */
  header .search-bar-container {
    height: auto;
    margin-top: 0;
    margin-bottom: 0;
  }

  /* Specific styles for mobile search bar */
  .mobile-search-input {
    height: 36px !important;
    padding-top: 0.375rem !important;
    padding-bottom: 0.375rem !important;
    font-size: 0.875rem !important;
  }

  /* Homepage search bar styles */
  .homepage-search input {
    height: 42px !important;
    font-size: 1rem !important;
  }

  .homepage-search .search-button-mobile {
    min-width: 28px;
    min-height: 28px;
  }

  .homepage-search .search-icon,
  .search-page-search .search-icon {
    width: 18px !important;
    height: 18px !important;
  }

  /* Search page specific styles */
  .search-page-search input {
    height: 42px !important;
    font-size: 1rem !important;
  }

  .search-page-search .search-button-mobile {
    min-width: 28px;
    min-height: 28px;
  }

  /* Position dropdown correctly for mobile */
  .mobile-search-dropdown {
    width: 100%;
    left: 0 !important;
    right: 0 !important;
  }

  /* Mobile video card styles */
  .group {
    margin-bottom: 0.5rem;
  }

  /* Mobile video grid styles */
  main {
    padding-bottom: 4rem !important; /* Make room for the bottom nav */
  }

  /* Mobile section titles */
  section h2 {
    font-size: 1rem !important;
  }

  /* Mobile layout styles - keeping original colors */
}

/* Ensure video is always visible in fullscreen mode */
:fullscreen video,
:-webkit-full-screen video,
:-moz-full-screen video,
:-ms-fullscreen video {
  display: block !important;
  visibility: visible !important;
}

/* Fix for sidebar and video player overlap */
@media (max-width: 1023px) {
  .sidebar-open .video-player-container {
    width: calc(100% - 16rem);
    margin-left: 16rem;
    transition: width 0.3s ease, margin-left 0.3s ease;
  }

  .sidebar-open .video-container {
    width: 100% !important;
  }

  .sidebar-open main {
    margin-left: 16rem;
    transition: margin-left 0.3s ease;
  }

  /* Ensure the sidebar is above the video player */
  aside.fixed {
    z-index: 40 !important;
  }

  /* Ensure the video player is below the sidebar */
  .video-container {
    z-index: 10 !important;
  }

  /* Ensure video controls are visible */
  .video-container .absolute {
    z-index: 15 !important;
  }
}