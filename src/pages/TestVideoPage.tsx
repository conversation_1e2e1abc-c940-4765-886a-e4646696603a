import React, { useEffect } from 'react';
import { useParams, useNavigate, <PERSON> } from 'react-router-dom';
import { useVideo } from '../hooks/useVideos';
import NativeVideoPlayer from '../components/video/NativeVideoPlayer';

// List of valid video IDs from the database
const VALID_VIDEO_IDS = [
  '5ac4509c-ed57-49fc-a160-2ed92f72d95e',
  'ef3f0da2-836d-4370-a3f1-14a4f81950d7',
  '30ac95c9-2e7e-4254-a858-f919fa319799',
  'cbf8d650-bac5-4b50-821e-d6770493fe85',
  '1fbb411b-211a-4344-98a1-c5f43739f643'
];

const TestVideoPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Redirect to a valid ID if the current ID is invalid
  useEffect(() => {
    // Check if the ID is a simple number or otherwise invalid
    if (id && !VALID_VIDEO_IDS.includes(id) && /^\d+$/.test(id)) {
      // Redirect to the first valid ID
      navigate(`/test-video/${VALID_VIDEO_IDS[0]}`, { replace: true });
    }
  }, [id, navigate]);

  // Use the hook to fetch video data
  const { video, isLoading, error } = useVideo(id || '');

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-700"></div>
        </div>
      </div>
    );
  }

  if (error || !video) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto mb-8">
          <h2 className="text-xl font-bold text-white mb-2">Error Loading Video</h2>
          <p className="text-red-200 mb-4">{error || 'Video not found'}</p>
          <p className="text-white mb-4">Please try one of these valid video IDs:</p>
          <div className="grid grid-cols-1 gap-2">
            {VALID_VIDEO_IDS.map(videoId => (
              <Link
                key={videoId}
                to={`/test-video/${videoId}`}
                className="bg-blue-700 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
              >
                {videoId}
              </Link>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">{video.title}</h1>

      {/* Use the enhanced NativeVideoPlayer component */}
      <NativeVideoPlayer
        video={video}
        onBack={() => navigate(-1)}
      />

      <div className="mt-8 bg-gray-800 rounded-lg p-4">
        <h2 className="text-xl font-bold mb-4">Test Other Videos</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {VALID_VIDEO_IDS.filter(videoId => videoId !== id).map(videoId => (
            <Link
              key={videoId}
              to={`/test-video/${videoId}`}
              className="bg-blue-700 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
            >
              Try Video ID: {videoId.substring(0, 8)}...
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TestVideoPage;
