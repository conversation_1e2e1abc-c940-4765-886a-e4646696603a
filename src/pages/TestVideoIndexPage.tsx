import React from 'react';
import { Link } from 'react-router-dom';

// List of valid video IDs from the database
const VALID_VIDEO_IDS = [
  '5ac4509c-ed57-49fc-a160-2ed92f72d95e',
  'ef3f0da2-836d-4370-a3f1-14a4f81950d7',
  '30ac95c9-2e7e-4254-a858-f919fa319799',
  'cbf8d650-bac5-4b50-821e-d6770493fe85',
  '1fbb411b-211a-4344-98a1-c5f43739f643'
];

const TestVideoIndexPage = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-white">Test Video Player</h1>
        
        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-bold mb-4 text-white">About This Page</h2>
          <p className="text-gray-300 mb-4">
            This page allows you to test the native video player with different videos from the database.
            The player has been configured to be purely native and support fullscreen functionality.
          </p>
          <p className="text-gray-300 mb-4">
            Click on any of the video IDs below to test the player with that video.
          </p>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-bold mb-4 text-white">Available Test Videos</h2>
          <div className="grid grid-cols-1 gap-4">
            {VALID_VIDEO_IDS.map(videoId => (
              <Link 
                key={videoId} 
                to={`/test-video/${videoId}`}
                className="bg-blue-700 hover:bg-blue-600 text-white px-6 py-4 rounded-lg transition-colors flex items-center justify-between group"
              >
                <span className="font-mono">{videoId}</span>
                <span className="bg-blue-800 group-hover:bg-blue-700 px-3 py-1 rounded-md transition-colors">
                  Test
                </span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestVideoIndexPage;
