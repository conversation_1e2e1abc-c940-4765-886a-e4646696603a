import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import CategoryTabs from '../components/sections/CategoryTabs';
import VideoGrid from '../components/sections/VideoGrid';
import Pagination from '../components/ui/Pagination';
import SearchBar from '../components/search/SearchBar';
import { Video } from '../types';
import { useVideos } from '../hooks/useVideos';

const HomePage = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const navigate = useNavigate();

  // Use SWR hooks for data fetching with caching - increased page size for better grid display
  const {
    videos,
    pagination,
    isLoading: videosLoading,
    error: videosError
  } = useVideos(selectedCategory, currentPage, 20); // Increased from 12 to 20 for better grid layout

  // Handle category change
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setCurrentPage(1); // Reset to first page when changing category
  };

  // Handle video click
  const handleVideoClick = (video: Video) => {
    navigate(`/video/${video.id}`);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top when changing pages
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Error state
  if (videosError && !videos.length) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto">
          <h2 className="text-xl font-bold text-white mb-2">Error Loading Videos</h2>
          <p className="text-red-200">{videosError}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pb-10">
      <div className="container mx-auto px-4">
        {/* Mobile Search Bar - Only visible on mobile */}
        <div className="md:hidden my-4">
          <div className="relative search-bar-container px-1">
            <SearchBar className="w-full mobile-search-bar homepage-search" />
          </div>
        </div>

        {/* Error Display */}
        {videosError && (
          <div className="bg-red-500/20 border border-red-500 text-white p-4 rounded-lg mb-6">
            <h3 className="font-bold mb-2">Error Loading Videos</h3>
            <p>{videosError}</p>
          </div>
        )}

        {/* Category Tabs */}
        <CategoryTabs
          selectedCategory={selectedCategory}
          onCategoryChange={handleCategoryChange}
        />

        {/* Main Video Grid */}
        <div className="mt-6">
          <VideoGrid
            title={selectedCategory ? `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} Videos` : 'All Videos'}
            videos={videos}
            onVideoClick={handleVideoClick}
            isLoading={videosLoading}
            className="mb-8"
          />
        </div>

        {/* Pagination */}
        {!videosLoading && videos.length > 0 && (
          <div className="mt-8">
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
              className="mb-8"
            />
          </div>
        )}

        {/* No videos message */}
        {!videosLoading && videos.length === 0 && !videosError && (
          <div className="text-center py-16">
            <div className="text-gray-400 text-lg mb-4">
              {selectedCategory ? `No videos found in ${selectedCategory} category` : 'No videos found'}
            </div>
            {selectedCategory && (
              <button
                onClick={() => handleCategoryChange('')}
                className="text-blue-400 hover:text-blue-300 underline"
              >
                View all videos
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default HomePage;
