import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useCategoryStore } from '../stores/categoryStore';
import { useVideos } from '../hooks/useVideos';
import VideoGrid from '../components/sections/VideoGrid';
import Pagination from '../components/ui/Pagination';
import SearchBar from '../components/search/SearchBar';
import { ArrowLeft } from 'lucide-react';
import { Video } from '../types';

const CategoryPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  // Get page from URL params, default to 1
  const pageFromUrl = parseInt(searchParams.get('page') || '1');
  const [currentPage, setCurrentPage] = useState<number>(pageFromUrl);

  // Update currentPage when URL page param changes
  useEffect(() => {
    const urlPage = parseInt(searchParams.get('page') || '1');
    if (urlPage !== currentPage) {
      setCurrentPage(urlPage);
    }
  }, [searchParams, currentPage]);

  // Use SWR hooks for data fetching with caching - increased page size like HomePage
  const {
    videos,
    pagination,
    isLoading: videosLoading,
    error: videosError
  } = useVideos(slug || '', currentPage, 24); // Increased from 12 to 24 like HomePage

  const { getCategoryBySlug, fetchCategories } = useCategoryStore();

  // Fetch categories when component mounts
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const category = slug ? getCategoryBySlug(slug) : undefined;

  const handleVideoClick = (video: Video) => {
    navigate(`/video/${video.id}`);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Update URL to reflect the new page
    setSearchParams({ page: page.toString() });
    // Scroll to top when changing pages
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleBackClick = () => {
    navigate('/', { replace: true });
  };

  // Error state
  if (videosError && !videos.length) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto">
          <h2 className="text-xl font-bold text-white mb-2">Error Loading Videos</h2>
          <p className="text-red-200">{videosError}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pb-10">
      <div className="container mx-auto px-4">
        {/* Header with back button and title */}
        <div className="flex items-center mb-6 pt-4">
          <button
            onClick={handleBackClick}
            className="mr-4 p-2 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors"
            aria-label="Go back"
          >
            <ArrowLeft size={20} className="text-white" />
          </button>
          <h1 className="text-2xl font-bold text-white">
            {category ? category.name : 'Category Not Found'}
          </h1>
        </div>

        {/* Mobile Search Bar - Only visible on mobile */}
        <div className="md:hidden my-4">
          <div className="relative search-bar-container px-1">
            <SearchBar className="w-full mobile-search-bar homepage-search" />
          </div>
        </div>

        {/* Error Display */}
        {videosError && (
          <div className="bg-red-500/20 border border-red-500 text-white p-4 rounded-lg mb-6">
            <h3 className="font-bold mb-2">Error Loading Videos</h3>
            <p>{videosError}</p>
          </div>
        )}

        {/* Video Count and Page Info */}
        {!videosLoading && videos.length > 0 && (
          <div className="flex justify-between items-center mb-4 text-sm text-gray-400">
            <div>
              Showing {((currentPage - 1) * 24) + 1}-{Math.min(currentPage * 24, pagination.totalCount)} of {pagination.totalCount} videos
            </div>
            <div>
              Page {currentPage} of {pagination.totalPages}
            </div>
          </div>
        )}

        {/* Main Video Grid */}
        <div className="mt-6">
          <VideoGrid
            title={`${category?.name || 'Category'} Videos`}
            videos={videos}
            onVideoClick={handleVideoClick}
            isLoading={videosLoading}
            className="mb-8"
          />
        </div>

        {/* Pagination */}
        {!videosLoading && videos.length > 0 && pagination.totalPages > 1 && (
          <div className="mt-12 mb-8">
            <div className="flex flex-col items-center space-y-4">
              <div className="text-center text-gray-400 text-sm">
                Page {currentPage} of {pagination.totalPages} • {pagination.totalCount} total videos
              </div>
              <Pagination
                currentPage={pagination.currentPage}
                totalPages={pagination.totalPages}
                onPageChange={handlePageChange}
                className="justify-center"
              />
            </div>
          </div>
        )}

        {/* No videos message */}
        {!videosLoading && videos.length === 0 && !videosError && (
          <div className="text-center py-16">
            <div className="text-gray-400 text-lg mb-4">
              No videos found in this category
            </div>
            <p className="text-gray-500 text-sm mb-4">
              Try browsing other categories or check back later!
            </p>
            <button
              onClick={handleBackClick}
              className="px-6 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Back to Home
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryPage;
