import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Heart } from 'lucide-react';
import VideoGrid from '../components/sections/VideoGrid';
import { useFavoriteStore } from '../stores/favoriteStore';
import { useAuthStore } from '../stores/authStore';
import Button from '../components/ui/Button';

const FavoritesPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { favorites, isLoading, error, fetchFavorites } = useFavoriteStore();

  useEffect(() => {
    fetchFavorites();
  }, [fetchFavorites]);

  const handleVideoClick = (videoId: string) => {
    navigate(`/video/${videoId}`);
  };

  // If user is not logged in, show login prompt
  if (!user) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="bg-gray-800 rounded-lg p-8 max-w-2xl mx-auto text-center">
          <Heart size={48} className="mx-auto text-gray-600 mb-4" />
          <h1 className="text-2xl font-bold text-white mb-4">Favorites</h1>
          <p className="text-gray-400 mb-6">
            You need to be logged in to view and manage your favorite videos.
          </p>
          <Button
            variant="primary"
            onClick={() => navigate('/')}
          >
            Go to Home
          </Button>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto">
          <h2 className="text-xl font-bold text-white mb-2">Error Loading Favorites</h2>
          <p className="text-red-200">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-8">
        <Heart size={24} className="text-orange-500 mr-3" />
        <h1 className="text-2xl md:text-3xl font-bold text-white">Your Favorites</h1>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
        </div>
      ) : favorites.length === 0 ? (
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <Heart size={48} className="mx-auto text-gray-600 mb-4" />
          <h2 className="text-xl font-bold text-white mb-2">No Favorites Yet</h2>
          <p className="text-gray-400 mb-6">
            You haven't added any videos to your favorites yet.
          </p>
          <Button
            variant="primary"
            onClick={() => navigate('/')}
          >
            Explore Videos
          </Button>
        </div>
      ) : (
        <VideoGrid
          videos={favorites}
          onVideoClick={(video) => handleVideoClick(video.id)}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default FavoritesPage;
