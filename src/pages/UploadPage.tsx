import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import UploadForm from '../components/upload/UploadForm';
import { AlertCircle } from 'lucide-react';

const UploadPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isLoading, isApproved, profile } = useAuthStore();
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  // Redirect to login if not authenticated - but wait for auth to be properly checked
  useEffect(() => {
    // Only redirect after we've given auth enough time to load
    const timer = setTimeout(() => {
      setHasCheckedAuth(true);
    }, 2000); // Give 2 seconds for auth to load

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Only redirect if we've checked auth and user is definitely not authenticated
    if (hasCheckedAuth && !isLoading && !user) {
      console.log('Redirecting to home - no user found after auth check');
      navigate('/');
    }
  }, [user, isLoading, navigate, hasCheckedAuth]);

  if (isLoading || !hasCheckedAuth) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-400 text-center">
            {isLoading ? 'Loading your account...' : 'Checking authentication...'}
          </p>
          <p className="text-gray-500 text-sm text-center mt-2">
            Please wait while we verify your login status
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  // If user is not approved, show a message
  if (!isApproved) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl md:text-3xl font-bold mb-6">Upload Video</h1>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle size={48} className="text-yellow-500 mb-4" />
            <h2 className="text-xl font-semibold mb-2">Approval Required</h2>
            <p className="text-gray-400 max-w-md mb-4">
              Your account is pending approval. New users need to be approved before they can upload videos.
            </p>
            <p className="text-gray-500 text-sm">
              Please check back later or contact an administrator for assistance.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl md:text-3xl font-bold mb-6">Upload Video</h1>

      <div className="bg-gray-800 rounded-lg p-6">
        <UploadForm />
      </div>
    </div>
  );
};

export default UploadPage;
