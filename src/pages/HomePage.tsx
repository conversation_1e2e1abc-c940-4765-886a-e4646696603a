import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import CategoryTabs from '../components/sections/CategoryTabs';
import VideoGrid from '../components/sections/VideoGrid';
import Pagination from '../components/ui/Pagination';
import SearchBar from '../components/search/SearchBar';
import { Video } from '../types';
import { useVideos } from '../hooks/useVideos';
import { getVideoThumbnailPlaceholder } from '../utils/imageUtils';

const HomePage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();

  // Get page and category from URL params
  const pageFromUrl = parseInt(searchParams.get('page') || '1');
  const categoryFromUrl = searchParams.get('category') || '';
  const [currentPage, setCurrentPage] = useState<number>(pageFromUrl);
  const [selectedCategory, setSelectedCategory] = useState<string>(categoryFromUrl);

  // Reset to page 1 when navigating to home route without page param
  useEffect(() => {
    if (location.pathname === '/' && !searchParams.has('page')) {
      setCurrentPage(1);
      // Update URL to reflect page 1
      const newParams: any = { page: '1' };
      if (selectedCategory) {
        newParams.category = selectedCategory;
      }
      setSearchParams(newParams);
    }
  }, [location.pathname, searchParams, setSearchParams, selectedCategory]);

  // Update currentPage and category when URL params change
  useEffect(() => {
    const urlPage = parseInt(searchParams.get('page') || '1');
    const urlCategory = searchParams.get('category') || '';
    if (urlPage !== currentPage) {
      setCurrentPage(urlPage);
    }
    if (urlCategory !== selectedCategory) {
      setSelectedCategory(urlCategory);
    }
  }, [searchParams, currentPage, selectedCategory]);

  // Fetch videos with category filter using new MySQL API
  const {
    videos: apiVideos,
    pagination,
    isLoading: videosLoading,
    error: videosError
  } = useVideos(selectedCategory, currentPage, 24); // 24 videos per page for better grid layout

  // Convert API videos to the format expected by VideoGrid
  const videos: Video[] = apiVideos.map(video => ({
    id: video.id,
    title: video.title,
    description: video.description || '',
    thumbnail: video.thumbnail_url || getVideoThumbnailPlaceholder(),
    thumbnailUrl: video.thumbnail_url || getVideoThumbnailPlaceholder(),
    videoUrl: video.video_url,
    duration: video.duration || 0,
    views: video.views || 0,
    likes: video.likes || 0,
    isHD: video.is_hd || false,
    category: video.category || 'uncategorized',
    uploadDate: video.created_at,
    creator: {
      id: video.user_id,
      name: video.username || 'Unknown User',
      avatar: video.user_avatar || 'https://placehold.co/150/gray/white?text=User',
      isVerified: false,
      isCreator: true,
      subscriberCount: 0,
    },
  }));

  // Handle video click
  const handleVideoClick = (video: Video) => {
    navigate(`/video/${video.id}`);
  };

  // Handle category change
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setCurrentPage(1); // Reset to page 1 when changing category
    // Update URL to reflect the new category and reset page
    const newParams: any = { page: '1' };
    if (category) {
      newParams.category = category;
    }
    setSearchParams(newParams);
    // Scroll to top when changing category
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Update URL to reflect the new page, keeping category
    const newParams: any = { page: page.toString() };
    if (selectedCategory) {
      newParams.category = selectedCategory;
    }
    setSearchParams(newParams);
    // Scroll to top when changing pages
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Error state
  if (videosError && !videos.length) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto">
          <h2 className="text-xl font-bold text-white mb-2">Error Loading Videos</h2>
          <p className="text-red-200">{videosError}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pb-10">
      <div className="container mx-auto px-4">
        {/* Mobile Search Bar - Only visible on mobile */}
        <div className="md:hidden my-4">
          <div className="relative search-bar-container px-1">
            <SearchBar className="w-full mobile-search-bar homepage-search" />
          </div>
        </div>

        {/* Category Tabs - Just under search bar */}
        <div className="mb-6">
          <CategoryTabs
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
          />
        </div>

        {/* Error Display */}
        {videosError && (
          <div className="bg-red-500/20 border border-red-500 text-white p-4 rounded-lg mb-6">
            <h3 className="font-bold mb-2">Error Loading Videos</h3>
            <p>{videosError}</p>
          </div>
        )}

        {/* Main Video Grid */}
        <div className="mt-6">
          <VideoGrid
            videos={videos}
            onVideoClick={handleVideoClick}
            isLoading={videosLoading}
            className="mb-8"
          />
        </div>

        {/* Pagination */}
        {!videosLoading && videos.length > 0 && pagination.totalPages > 1 && (
          <div className="mt-12 mb-8">
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
              className="justify-center"
            />
          </div>
        )}

        {/* No videos message */}
        {!videosLoading && videos.length === 0 && !videosError && (
          <div className="text-center py-16">
            <div className="text-gray-400 text-lg mb-4">
              No videos found
            </div>
            <p className="text-gray-500 text-sm">
              Check back later for new content!
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default HomePage;
