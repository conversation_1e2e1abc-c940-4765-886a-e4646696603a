import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Edit, Trash2, Play, AlertCircle, Plus, Filter, SortDesc,
  CheckSquare, Square, Eye, EyeOff, Calendar, BarChart2,
  RefreshCw, Search, X, ChevronDown
} from 'lucide-react';
import Button from '../components/ui/Button';
import { useVideoStore } from '../stores/videoStore';
import { useAuthStore } from '../stores/authStore';
import { Video, VideoStatus, SortOptions, FilterOptions } from '../types';
import EditVideoModal from '../components/video/EditVideoModal';
import DeleteConfirmationModal from '../components/ui/DeleteConfirmationModal';
import VideoStatusModal from '../components/video/VideoStatusModal';
import BatchOperationsModal from '../components/video/BatchOperationsModal';
import VideoAnalyticsModal from '../components/video/VideoAnalyticsModal';
import Pagination from '../components/ui/Pagination';
import { formatCount, formatDuration } from '../utils/formatters';
import { categories } from '../data/mockData';

const ManageVideosPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isLoading: authLoading } = useAuthStore();
  const {
    userVideos,
    selectedVideoIds,
    sortOptions,
    filterOptions,
    pagination,
    isLoading,
    error,
    fetchUserVideos,
    deleteVideo,
    selectVideo,
    selectAllVideos,
    batchDeleteVideos,
    batchUpdateVideosStatus,
    batchUpdateVideosCategory,
    setSortOptions,
    setFilterOptions,
    setPage
  } = useVideoStore();

  // Selected video for individual operations
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);

  // Modal states
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [isAnalyticsModalOpen, setIsAnalyticsModalOpen] = useState(false);
  const [isBatchModalOpen, setIsBatchModalOpen] = useState(false);

  // Filter and sort UI states
  const [isFilterMenuOpen, setIsFilterMenuOpen] = useState(false);
  const [isSortMenuOpen, setIsSortMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Error states
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Batch operation states
  const [batchOperation, setBatchOperation] = useState<'delete' | 'status' | 'category'>('delete');

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/');
    }
  }, [user, authLoading, navigate]);

  // Fetch user's videos when component mounts or pagination changes
  useEffect(() => {
    if (user) {
      fetchUserVideos(sortOptions, filterOptions, pagination.currentPage);
    }
  }, [user, fetchUserVideos, sortOptions, filterOptions, pagination.currentPage]);

  // Individual video operations
  const handleEditClick = (video: Video) => {
    setSelectedVideo(video);
    setIsEditModalOpen(true);
  };

  const handleDeleteClick = (video: Video) => {
    setSelectedVideo(video);
    setIsDeleteModalOpen(true);
  };

  const handleStatusClick = (video: Video) => {
    setSelectedVideo(video);
    setIsStatusModalOpen(true);
  };

  const handleAnalyticsClick = (video: Video) => {
    setSelectedVideo(video);
    setIsAnalyticsModalOpen(true);
  };

  const handlePlayClick = (video: Video) => {
    navigate(`/video/${video.id}`);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedVideo) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const success = await deleteVideo(selectedVideo.id);

      if (success) {
        setIsDeleteModalOpen(false);
        setSelectedVideo(null);
      } else {
        setDeleteError('Failed to delete video. Please try again.');
      }
    } catch (error) {
      setDeleteError('An unexpected error occurred. Please try again.');
      console.error('Error deleting video:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Batch operations
  const handleBatchOperation = (operation: 'delete' | 'status' | 'category') => {
    setBatchOperation(operation);
    setIsBatchModalOpen(true);
  };

  // Selection handlers
  const handleSelectVideo = (video: Video, isSelected: boolean) => {
    selectVideo(video.id, isSelected);
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    selectAllVideos(event.target.checked);
  };

  // Search, sort and filter handlers
  const handleSearch = (event: React.FormEvent) => {
    event.preventDefault();
    setFilterOptions({
      ...filterOptions,
      search: searchQuery
    });
  };

  const clearSearch = () => {
    setSearchQuery('');
    setFilterOptions({
      ...filterOptions,
      search: undefined
    });
  };

  const handleSortChange = (field: SortOptions['field']) => {
    const direction = sortOptions.field === field && sortOptions.direction === 'asc' ? 'desc' : 'asc';
    setSortOptions({ field, direction });
    setIsSortMenuOpen(false);
  };

  const handleFilterChange = (filter: Partial<FilterOptions>) => {
    setFilterOptions({ ...filterOptions, ...filter });
    setIsFilterMenuOpen(false);
  };

  const clearFilters = () => {
    setFilterOptions({});
    setSearchQuery('');
  };

  // Loading state
  if (authLoading || (isLoading && userVideos.length === 0)) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error && !userVideos.length) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto">
          <h2 className="text-xl font-bold text-white mb-2">Error Loading Videos</h2>
          <p className="text-red-200">{error}</p>
        </div>
      </div>
    );
  }

  // Helper function to get status badge
  const getStatusBadge = (status: VideoStatus) => {
    switch (status) {
      case 'public':
        return <span className="px-2 py-0.5 bg-green-500/20 text-green-400 rounded text-xs">Public</span>;
      case 'private':
        return <span className="px-2 py-0.5 bg-gray-500/20 text-gray-400 rounded text-xs">Private</span>;
      case 'scheduled':
        return <span className="px-2 py-0.5 bg-blue-500/20 text-blue-400 rounded text-xs">Scheduled</span>;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
        <h1 className="text-2xl md:text-3xl font-bold">Manage Your Videos</h1>
        {user && (
          <div className="flex items-center space-x-2">
            <Button
              variant="primary"
              leftIcon={<Plus size={18} />}
              onClick={() => navigate('/upload')}
            >
              Upload New Video
            </Button>
          </div>
        )}
      </div>

      {userVideos.length === 0 ? (
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <h2 className="text-xl font-semibold text-white mb-4">You haven't uploaded any videos yet</h2>
          <p className="text-gray-400 mb-6">Get started by uploading your first video</p>
          {user && (
            <Button
              variant="primary"
              leftIcon={<Plus size={18} />}
              onClick={() => navigate('/upload')}
            >
              Upload Video
            </Button>
          )}
        </div>
      ) : (
        <>
          {/* Search, Filter, and Batch Operations Bar */}
          <div className="bg-gray-800 rounded-lg p-4 mb-4">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <form onSubmit={handleSearch} className="relative">
                  <input
                    type="text"
                    placeholder="Search videos..."
                    className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 pl-10 pr-4 text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
                  {searchQuery && (
                    <button
                      type="button"
                      className="absolute right-3 top-2.5 text-gray-400 hover:text-white"
                      onClick={clearSearch}
                    >
                      <X size={18} />
                    </button>
                  )}
                </form>
              </div>

              {/* Filter and Sort */}
              <div className="flex gap-2">
                {/* Sort Dropdown */}
                <div className="relative">
                  <Button
                    variant="secondary"
                    leftIcon={<SortDesc size={18} />}
                    onClick={() => setIsSortMenuOpen(!isSortMenuOpen)}
                    className="whitespace-nowrap"
                  >
                    Sort
                    <ChevronDown size={16} className="ml-1" />
                  </Button>

                  {isSortMenuOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-10">
                      <div className="py-1">
                        <button
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-700 ${
                            sortOptions.field === 'title' ? 'text-orange-500' : 'text-white'
                          }`}
                          onClick={() => handleSortChange('title')}
                        >
                          Title {sortOptions.field === 'title' && (sortOptions.direction === 'asc' ? '↑' : '↓')}
                        </button>
                        <button
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-700 ${
                            sortOptions.field === 'views' ? 'text-orange-500' : 'text-white'
                          }`}
                          onClick={() => handleSortChange('views')}
                        >
                          Views {sortOptions.field === 'views' && (sortOptions.direction === 'asc' ? '↑' : '↓')}
                        </button>
                        <button
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-700 ${
                            sortOptions.field === 'createdAt' ? 'text-orange-500' : 'text-white'
                          }`}
                          onClick={() => handleSortChange('createdAt')}
                        >
                          Date {sortOptions.field === 'createdAt' && (sortOptions.direction === 'asc' ? '↑' : '↓')}
                        </button>
                        <button
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-700 ${
                            sortOptions.field === 'status' ? 'text-orange-500' : 'text-white'
                          }`}
                          onClick={() => handleSortChange('status')}
                        >
                          Status {sortOptions.field === 'status' && (sortOptions.direction === 'asc' ? '↑' : '↓')}
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Filter Dropdown */}
                <div className="relative">
                  <Button
                    variant="secondary"
                    leftIcon={<Filter size={18} />}
                    onClick={() => setIsFilterMenuOpen(!isFilterMenuOpen)}
                    className={`whitespace-nowrap ${Object.keys(filterOptions).length > 0 ? 'border-orange-500' : ''}`}
                  >
                    Filter
                    <ChevronDown size={16} className="ml-1" />
                  </Button>

                  {isFilterMenuOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-10">
                      <div className="py-1">
                        <div className="px-4 py-2 text-sm text-gray-300 font-medium border-b border-gray-700">Status</div>
                        <button
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-700 ${
                            filterOptions.status === 'public' ? 'text-orange-500' : 'text-white'
                          }`}
                          onClick={() => handleFilterChange({ status: 'public' })}
                        >
                          Public
                        </button>
                        <button
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-700 ${
                            filterOptions.status === 'private' ? 'text-orange-500' : 'text-white'
                          }`}
                          onClick={() => handleFilterChange({ status: 'private' })}
                        >
                          Private
                        </button>
                        <button
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-700 ${
                            filterOptions.status === 'scheduled' ? 'text-orange-500' : 'text-white'
                          }`}
                          onClick={() => handleFilterChange({ status: 'scheduled' })}
                        >
                          Scheduled
                        </button>

                        <div className="px-4 py-2 text-sm text-gray-300 font-medium border-b border-t border-gray-700">Category</div>
                        {categories.map(category => (
                          <button
                            key={category.id}
                            className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-700 ${
                              filterOptions.category === category.slug ? 'text-orange-500' : 'text-white'
                            }`}
                            onClick={() => handleFilterChange({ category: category.slug })}
                          >
                            {category.name}
                          </button>
                        ))}

                        <div className="border-t border-gray-700 mt-1 pt-1">
                          <button
                            className="w-full text-left px-4 py-2 text-sm text-orange-500 hover:bg-gray-700"
                            onClick={clearFilters}
                          >
                            Clear Filters
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Refresh Button */}
                <Button
                  variant="secondary"
                  leftIcon={<RefreshCw size={18} />}
                  onClick={() => fetchUserVideos()}
                  isLoading={isLoading}
                  disabled={isLoading}
                  className="whitespace-nowrap"
                >
                  Refresh
                </Button>
              </div>
            </div>

            {/* Batch Operations */}
            {selectedVideoIds.length > 0 && (
              <div className="mt-4 p-3 bg-gray-700 rounded-md flex flex-wrap items-center gap-3">
                <div className="text-white font-medium">
                  {selectedVideoIds.length} video{selectedVideoIds.length !== 1 ? 's' : ''} selected
                </div>
                <div className="flex-1"></div>
                <Button
                  variant="secondary"
                  size="sm"
                  leftIcon={<Eye size={16} />}
                  onClick={() => handleBatchOperation('status')}
                >
                  Change Status
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  leftIcon={<Filter size={16} />}
                  onClick={() => handleBatchOperation('category')}
                >
                  Change Category
                </Button>
                <Button
                  variant="danger"
                  size="sm"
                  leftIcon={<Trash2 size={16} />}
                  onClick={() => handleBatchOperation('delete')}
                >
                  Delete Selected
                </Button>
              </div>
            )}
          </div>

          {/* Videos Table */}
          <div className="bg-gray-800 rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-4 py-3 text-left">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded bg-gray-700 border-gray-600 text-orange-500 focus:ring-orange-500"
                          onChange={handleSelectAll}
                          checked={selectedVideoIds.length > 0 && selectedVideoIds.length === userVideos.length}
                        />
                      </div>
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-200">Video</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 hidden md:table-cell">Status</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 hidden md:table-cell">Date</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 hidden md:table-cell">Views</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-200">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {userVideos.map((video) => (
                    <tr key={video.id} className={`hover:bg-gray-700/50 transition-colors ${
                      selectedVideoIds.includes(video.id) ? 'bg-gray-700/70' : ''
                    }`}>
                      <td className="px-4 py-4">
                        <input
                          type="checkbox"
                          className="rounded bg-gray-700 border-gray-600 text-orange-500 focus:ring-orange-500"
                          checked={selectedVideoIds.includes(video.id)}
                          onChange={(e) => handleSelectVideo(video, e.target.checked)}
                        />
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex items-center">
                          <div className="relative w-24 h-14 flex-shrink-0 mr-3">
                            <img
                              src={video.thumbnailUrl || 'https://placehold.co/640x360/gray/white?text=No+Thumbnail'}
                              alt={video.title}
                              className="w-full h-full object-cover rounded"
                              onError={(e) => {
                                e.currentTarget.src = 'https://placehold.co/640x360/gray/white?text=No+Thumbnail';
                              }}
                            />
                            {video.duration > 0 && (
                              <div className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 rounded">
                                {formatDuration(video.duration)}
                              </div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="text-white font-medium truncate">{video.title}</h3>
                            <p className="text-gray-400 text-sm truncate">{video.description}</p>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm hidden md:table-cell">
                        {getStatusBadge(video.status)}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-400 hidden md:table-cell">
                        {new Date(video.createdAt).toLocaleDateString()}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-400 hidden md:table-cell">
                        {formatCount(video.views || 0)}
                      </td>
                      <td className="px-4 py-4 text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            className="p-1.5 rounded-full bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 transition-colors"
                            onClick={() => handlePlayClick(video)}
                            title="Play"
                          >
                            <Play size={16} />
                          </button>
                          <button
                            className="p-1.5 rounded-full bg-green-500/20 text-green-400 hover:bg-green-500/30 transition-colors"
                            onClick={() => handleAnalyticsClick(video)}
                            title="Analytics"
                          >
                            <BarChart2 size={16} />
                          </button>
                          <button
                            className="p-1.5 rounded-full bg-purple-500/20 text-purple-400 hover:bg-purple-500/30 transition-colors"
                            onClick={() => handleStatusClick(video)}
                            title="Change Status"
                          >
                            {video.status === 'public' ? (
                              <Eye size={16} />
                            ) : video.status === 'private' ? (
                              <EyeOff size={16} />
                            ) : (
                              <Calendar size={16} />
                            )}
                          </button>
                          <button
                            className="p-1.5 rounded-full bg-orange-500/20 text-orange-400 hover:bg-orange-500/30 transition-colors"
                            onClick={() => handleEditClick(video)}
                            title="Edit"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            className="p-1.5 rounded-full bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors"
                            onClick={() => handleDeleteClick(video)}
                            title="Delete"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {!isLoading && userVideos.length > 0 && (
              <div className="mt-6 flex justify-center">
                <Pagination
                  currentPage={pagination.currentPage}
                  totalPages={pagination.totalPages}
                  onPageChange={(page) => setPage(page)}
                  className="mb-4"
                />
              </div>
            )}
          </div>
        </>
      )}

      {/* Edit Modal */}
      {selectedVideo && (
        <EditVideoModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          video={selectedVideo}
        />
      )}

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Video"
        message={`Are you sure you want to delete "${selectedVideo?.title}"? This action cannot be undone.`}
        isLoading={isDeleting}
        error={deleteError}
      />

      {/* Status Modal */}
      {selectedVideo && (
        <VideoStatusModal
          isOpen={isStatusModalOpen}
          onClose={() => setIsStatusModalOpen(false)}
          video={selectedVideo}
        />
      )}

      {/* Analytics Modal */}
      {selectedVideo && (
        <VideoAnalyticsModal
          isOpen={isAnalyticsModalOpen}
          onClose={() => setIsAnalyticsModalOpen(false)}
          video={selectedVideo}
        />
      )}

      {/* Batch Operations Modal */}
      <BatchOperationsModal
        isOpen={isBatchModalOpen}
        onClose={() => setIsBatchModalOpen(false)}
        operation={batchOperation}
        selectedCount={selectedVideoIds.length}
      />
    </div>
  );
};

export default ManageVideosPage;
