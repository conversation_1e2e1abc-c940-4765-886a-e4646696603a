import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Search } from 'lucide-react';
import VideoGrid from '../components/sections/VideoGrid';
import SearchBar from '../components/search/SearchBar';
import { useSearchStore } from '../stores/searchStore';

const SearchPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    searchQuery,
    searchResults,
    isLoading,
    error,
    setSearchQuery,
    search
  } = useSearchStore();

  // Get search query from URL
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const queryParam = params.get('q');

    if (queryParam) {
      setSearchQuery(queryParam);
      search(queryParam);
    }
  }, [location.search, setSearchQuery, search]);

  const handleVideoClick = (videoId: string) => {
    navigate(`/video/${videoId}`);
  };

  return (
    <div className="container mx-auto px-4 py-4 md:py-8">
      {/* Prominent search bar for mobile */}
      <div className="mb-6">
        <div className="relative search-bar-container px-1 mb-4">
          <SearchBar className="w-full mobile-search-bar search-page-search" />
        </div>

        <h1 className="text-xl md:text-3xl font-bold text-white mb-2 flex items-center">
          <Search size={20} className="mr-2 text-blue-700" />
          Search Results
        </h1>
        {searchQuery && (
          <p className="text-gray-400 text-sm md:text-base">
            Showing results for <span className="text-white font-medium">"{searchQuery}"</span>
          </p>
        )}
      </div>

      {error && (
        <div className="bg-red-500/20 border border-red-500 rounded-md p-4 mb-8">
          <h3 className="text-white font-bold mb-2">Error</h3>
          <p className="text-red-200">{error}</p>
          {error.includes("column") && (
            <div className="mt-3 text-sm">
              <p className="text-white">This appears to be a database schema issue. The search is trying to access columns that don't exist.</p>
              <p className="text-gray-300 mt-2">We've updated the search to only look in title and description fields.</p>
              <button
                onClick={() => search(searchQuery)}
                className="mt-3 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md"
              >
                Try Again
              </button>
            </div>
          )}
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
        </div>
      ) : searchResults.length > 0 ? (
        <VideoGrid
          videos={searchResults}
          onVideoClick={(video) => handleVideoClick(video.id)}
        />
      ) : searchQuery ? (
        <div className="bg-gray-800 rounded-lg p-4 md:p-8 text-center">
          <Search size={36} className="mx-auto text-gray-600 mb-3" />
          <h2 className="text-lg md:text-xl font-bold text-white mb-2">No results found</h2>
          <p className="text-gray-400 mb-3 text-sm md:text-base">
            We couldn't find any videos matching "{searchQuery}"
          </p>
          <p className="text-gray-500 text-xs md:text-sm">
            Try different keywords or check for spelling mistakes
          </p>
        </div>
      ) : (
        <div className="bg-gray-800 rounded-lg p-4 md:p-8 text-center">
          <Search size={36} className="mx-auto text-gray-600 mb-3" />
          <h2 className="text-lg md:text-xl font-bold text-white mb-2">Search for videos</h2>
          <p className="text-gray-400 text-sm md:text-base">
            Use the search bar above to find videos
          </p>
        </div>
      )}
    </div>
  );
};

export default SearchPage;
