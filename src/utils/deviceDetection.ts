/**
 * Utility functions for device detection
 */

/**
 * Checks if the current device is a mobile device
 * @returns boolean indicating if the device is mobile
 */
export const isMobile = (): boolean => {
  // Check if window is defined (for SSR)
  if (typeof window === 'undefined') return false;

  // Check for mobile user agent
  const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;

  // Mobile device regex patterns
  const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;

  // Check if touch is supported
  const hasTouchScreen = 'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    (navigator as any).msMaxTouchPoints > 0;

  // Check screen size (typical mobile width)
  const isSmallScreen = window.innerWidth <= 768;

  // Store the result in sessionStorage for consistency
  const isMobileDevice = mobileRegex.test(userAgent) || (hasTouchScreen && isSmallScreen);

  try {
    // Store the result in sessionStorage to maintain consistency across component renders
    if (typeof sessionStorage !== 'undefined') {
      sessionStorage.setItem('isMobileDevice', String(isMobileDevice));
    }
  } catch (e) {
    console.error('Error accessing sessionStorage:', e);
  }

  return isMobileDevice;
};

/**
 * Gets the cached mobile device status or recalculates it
 * @returns boolean indicating if the device is mobile
 */
export const getIsMobile = (): boolean => {
  try {
    // Try to get the cached value first
    if (typeof sessionStorage !== 'undefined') {
      const cached = sessionStorage.getItem('isMobileDevice');
      if (cached !== null) {
        return cached === 'true';
      }
    }
  } catch (e) {
    console.error('Error accessing sessionStorage:', e);
  }

  // Fall back to recalculating
  return isMobile();
};

/**
 * Checks if the device supports the Fullscreen API
 * @returns boolean indicating if fullscreen is supported
 */
export const isFullscreenSupported = (): boolean => {
  // Check if document is defined (for SSR)
  if (typeof document === 'undefined') return false;

  return !!(
    document.fullscreenEnabled ||
    (document as any).webkitFullscreenEnabled ||
    (document as any).mozFullScreenEnabled ||
    (document as any).msFullscreenEnabled
  );
};

/**
 * Requests fullscreen for an element
 * @param element The HTML element to make fullscreen
 */
export const requestFullscreen = (element: HTMLElement): void => {
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if ((element as any).webkitRequestFullscreen) {
    (element as any).webkitRequestFullscreen();
  } else if ((element as any).mozRequestFullScreen) {
    (element as any).mozRequestFullScreen();
  } else if ((element as any).msRequestFullscreen) {
    (element as any).msRequestFullscreen();
  }
};

/**
 * Exits fullscreen mode
 */
export const exitFullscreen = (): void => {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if ((document as any).webkitExitFullscreen) {
    (document as any).webkitExitFullscreen();
  } else if ((document as any).mozCancelFullScreen) {
    (document as any).mozCancelFullScreen();
  } else if ((document as any).msExitFullscreen) {
    (document as any).msExitFullscreen();
  }
};

/**
 * Checks if the document is currently in fullscreen mode
 * @returns boolean indicating if in fullscreen mode
 */
export const isInFullscreen = (): boolean => {
  return !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  );
};
