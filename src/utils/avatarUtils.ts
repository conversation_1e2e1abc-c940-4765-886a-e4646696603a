/**
 * Utility functions for avatar generation and management
 */

/**
 * Returns a URL for an animated character avatar
 * Uses DiceBear's Avataaars collection for cartoon-style avatars
 *
 * @param seed A string seed to generate a consistent avatar (e.g. user ID or email)
 * @returns URL to an animated character avatar
 */
export const getAnimatedAvatar = (seed: string = 'default'): string => {
  // Use DiceBear Avataaars for cartoon-style avatars
  // Options: https://avatars.dicebear.com/styles/avataaars

  // Define possible options for variety
  const mouthOptions = ['smile', 'tongue', 'twinkle', 'vomit'];
  const eyesOptions = ['happy', 'hearts', 'stars', 'wink', 'winkWacky'];
  const topOptions = ['longHair', 'shortHair', 'eyepatch', 'hat', 'hijab', 'turban', 'bigHair', 'bob', 'bun'];
  const accessoriesOptions = ['kurt', 'prescription01', 'prescription02', 'round', 'sunglasses', 'wayfarers'];
  const hairColorOptions = ['auburn', 'black', 'blonde', 'brown', 'pastel', 'platinum', 'red', 'blue', 'pink'];
  const facialHairOptions = ['medium', 'light', 'majestic', 'fancy', 'magnum'];
  const clothesOptions = ['blazer', 'sweater', 'hoodie', 'overall', 'shirtCrewNeck'];
  const fabricOptions = ['denim', 'graphicShirt', 'stripes', 'dots'];
  const backgroundColors = ['b6e3f4', 'c0aede', 'ffd5dc', 'ffdfbf', 'd1d4f9', 'c0e8d5'];

  // Use the seed to deterministically select options
  const seedNum = seed.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);

  const getMod = (arr: string[], num: number) => arr[num % arr.length];

  const options = [
    `mouth=${getMod(mouthOptions, seedNum)}`,
    `eyes=${getMod(eyesOptions, seedNum + 1)}`,
    `top=${getMod(topOptions, seedNum + 2)}`,
    `accessories=${getMod(accessoriesOptions, seedNum + 3)}`,
    `hairColor=${getMod(hairColorOptions, seedNum + 4)}`,
    `facialHair=${getMod(facialHairOptions, seedNum + 5)}`,
    `clothes=${getMod(clothesOptions, seedNum + 6)}`,
    `fabric=${getMod(fabricOptions, seedNum + 7)}`,
    `backgroundColor=${getMod(backgroundColors, seedNum + 8)}`
  ].join('&');

  return `https://avatars.dicebear.com/api/avataaars/${encodeURIComponent(seed)}.svg?${options}`;
};

/**
 * Returns a URL for a fallback avatar if the main one fails to load
 */
export const getFallbackAvatar = (): string => {
  // Use a different style for fallback (bottts = robot style)
  const options = [
    'backgroundColor=b6e3f4',
    'colors=blue',
    'mouthChance=100',
    'sidesChance=100',
    'topChance=100'
  ].join('&');

  return `https://avatars.dicebear.com/api/bottts/fallback.svg?${options}`;
};
