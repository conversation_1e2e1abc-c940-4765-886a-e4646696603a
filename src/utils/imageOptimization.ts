/**
 * Advanced image optimization utilities for better performance
 */

// Cache for storing optimized image URLs
const imageUrlCache = new Map<string, string>();

// Cache for storing image format support detection
let formatSupportCache: { webp: boolean; avif: boolean } | null = null;

/**
 * Detect browser support for modern image formats
 */
export const detectImageFormatSupport = async (): Promise<{ webp: boolean; avif: boolean }> => {
  if (formatSupportCache) {
    return formatSupportCache;
  }

  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    formatSupportCache = { webp: false, avif: false };
    return formatSupportCache;
  }

  // Create a simple test image
  ctx.fillStyle = '#000';
  ctx.fillRect(0, 0, 1, 1);

  const webpSupport = await new Promise<boolean>((resolve) => {
    canvas.toBlob((blob) => {
      resolve(blob !== null);
    }, 'image/webp');
  });

  const avifSupport = await new Promise<boolean>((resolve) => {
    canvas.toBlob((blob) => {
      resolve(blob !== null);
    }, 'image/avif');
  });

  formatSupportCache = { webp: webpSupport, avif: avifSupport };
  return formatSupportCache;
};

/**
 * Generate optimized image URL with caching
 */
export const getOptimizedImageUrlCached = async (
  url: string,
  width: number,
  height?: number,
  quality = 80
): Promise<string> => {
  const cacheKey = `${url}-${width}-${height || 'auto'}-${quality}`;
  
  if (imageUrlCache.has(cacheKey)) {
    return imageUrlCache.get(cacheKey)!;
  }

  const formatSupport = await detectImageFormatSupport();
  let optimizedUrl = url;

  // Apply Supabase storage optimizations if applicable
  if (url.includes('supabase.co/storage/v1/object/public/')) {
    try {
      const urlObj = new URL(url);
      urlObj.searchParams.set('width', width.toString());
      if (height) {
        urlObj.searchParams.set('height', height.toString());
      }
      urlObj.searchParams.set('quality', quality.toString());

      // Use the best supported format
      if (formatSupport.avif) {
        urlObj.searchParams.set('format', 'avif');
      } else if (formatSupport.webp) {
        urlObj.searchParams.set('format', 'webp');
      }

      optimizedUrl = urlObj.toString();
    } catch (error) {
      console.warn('Failed to optimize image URL:', error);
    }
  }

  // For Namecheap hosting, we don't apply transformations since they're not supported
  // Just return the original URL for Namecheap hosted images
  if (url.includes('bluefilmx.com') || url.includes('premium34.web-hosting.com')) {
    optimizedUrl = url;
  }

  imageUrlCache.set(cacheKey, optimizedUrl);
  return optimizedUrl;
};

/**
 * Preload images with priority and format optimization
 */
export const preloadOptimizedImages = async (
  imageUrls: string[],
  options: {
    width?: number;
    height?: number;
    quality?: number;
    priority?: 'high' | 'low';
    maxConcurrent?: number;
  } = {}
) => {
  const {
    width = 640,
    height,
    quality = 80,
    priority = 'low',
    maxConcurrent = 3
  } = options;

  // Process images in batches to avoid overwhelming the browser
  const batches = [];
  for (let i = 0; i < imageUrls.length; i += maxConcurrent) {
    batches.push(imageUrls.slice(i, i + maxConcurrent));
  }

  for (const batch of batches) {
    const preloadPromises = batch.map(async (url) => {
      try {
        const optimizedUrl = await getOptimizedImageUrlCached(url, width, height, quality);
        
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = optimizedUrl;
        
        if (priority === 'high') {
          link.setAttribute('fetchpriority', 'high');
        }
        
        document.head.appendChild(link);
        
        // Clean up after a delay to prevent memory leaks
        setTimeout(() => {
          if (link.parentNode) {
            link.parentNode.removeChild(link);
          }
        }, 30000); // Remove after 30 seconds
        
      } catch (error) {
        console.warn('Failed to preload image:', url, error);
      }
    });

    await Promise.allSettled(preloadPromises);
    
    // Small delay between batches to prevent blocking
    if (batches.indexOf(batch) < batches.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
};

/**
 * Generate responsive image srcSet with optimized formats
 */
export const generateResponsiveSrcSet = async (
  url: string,
  baseSizes: number[] = [320, 640, 1024, 1280]
): Promise<string> => {
  const formatSupport = await detectImageFormatSupport();
  
  const srcSetEntries = await Promise.all(
    baseSizes.map(async (size) => {
      const optimizedUrl = await getOptimizedImageUrlCached(url, size);
      return `${optimizedUrl} ${size}w`;
    })
  );

  return srcSetEntries.join(', ');
};

/**
 * Clear image optimization cache
 */
export const clearImageCache = (): void => {
  imageUrlCache.clear();
  formatSupportCache = null;
};

/**
 * Get cache statistics
 */
export const getImageCacheStats = (): { size: number; keys: string[] } => {
  return {
    size: imageUrlCache.size,
    keys: Array.from(imageUrlCache.keys())
  };
};

/**
 * Compress image client-side (for user uploads)
 */
export const compressImage = (
  file: File,
  maxWidth = 1920,
  maxHeight = 1080,
  quality = 0.8
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        'image/jpeg',
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Lazy load images with intersection observer
 */
export const createLazyImageObserver = (
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver => {
  const defaultOptions: IntersectionObserverInit = {
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  };

  return new IntersectionObserver(callback, defaultOptions);
};

/**
 * Estimate image loading time based on connection
 */
export const estimateImageLoadTime = (imageSizeKB: number): number => {
  // Get connection info if available
  const connection = (navigator as any).connection;
  
  if (connection) {
    const effectiveType = connection.effectiveType;
    const speedMap: { [key: string]: number } = {
      'slow-2g': 50, // KB/s
      '2g': 250,
      '3g': 750,
      '4g': 2000
    };
    
    const speed = speedMap[effectiveType] || 1000;
    return (imageSizeKB / speed) * 1000; // Return in milliseconds
  }
  
  // Fallback estimate
  return (imageSizeKB / 500) * 1000; // Assume 500 KB/s
};
