/**
 * Cache Busting Utilities - Prevents browser cache conflicts
 */

export interface CacheConfig {
  version: string;
  timestamp: number;
  buildId: string;
}

/**
 * Get current cache configuration
 */
export const getCacheConfig = (): CacheConfig => {
  return {
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    timestamp: Date.now(),
    buildId: import.meta.env.VITE_BUILD_ID || 'dev'
  };
};

/**
 * Generate cache-busting query parameters
 */
export const getCacheBustingParams = (): string => {
  const config = getCacheConfig();
  return `v=${config.version}&t=${config.timestamp}&b=${config.buildId}`;
};

/**
 * Add cache-busting parameters to a URL
 */
export const addCacheBusting = (url: string): string => {
  if (!url) return url;
  
  try {
    const urlObj = new URL(url, window.location.origin);
    const params = getCacheBustingParams();
    
    // Add cache busting params
    urlObj.searchParams.set('v', getCacheConfig().version);
    urlObj.searchParams.set('cb', Date.now().toString());
    
    return urlObj.toString();
  } catch (error) {
    // If URL parsing fails, append params manually
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}${getCacheBustingParams()}`;
  }
};

/**
 * Create cache-aware fetch wrapper
 */
export const cacheBustingFetch = async (
  url: string, 
  options: RequestInit = {}
): Promise<Response> => {
  const cacheBustedUrl = addCacheBusting(url);
  
  const defaultHeaders = {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  };

  const mergedOptions: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers
    }
  };

  return fetch(cacheBustedUrl, mergedOptions);
};

/**
 * Check if current version matches stored version
 */
export const isVersionCurrent = (): boolean => {
  try {
    const storedVersion = localStorage.getItem('app-version');
    const currentVersion = getCacheConfig().version;
    return storedVersion === currentVersion;
  } catch (error) {
    return false;
  }
};

/**
 * Update stored version
 */
export const updateStoredVersion = (): void => {
  try {
    const config = getCacheConfig();
    localStorage.setItem('app-version', config.version);
    localStorage.setItem('app-build-id', config.buildId);
    localStorage.setItem('version-updated', config.timestamp.toString());
  } catch (error) {
    console.warn('Failed to update stored version:', error);
  }
};

/**
 * Clear version-specific cache
 */
export const clearVersionCache = async (): Promise<void> => {
  try {
    // Clear browser caches
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
    }

    // Clear version-specific localStorage items
    const keysToRemove = [
      'user-preferences-storage',
      'disclaimerAccepted',
      'recentSearches'
    ];
    
    keysToRemove.forEach(key => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.warn(`Failed to remove ${key}:`, error);
      }
    });

    // Update version
    updateStoredVersion();
  } catch (error) {
    console.error('Failed to clear version cache:', error);
  }
};

/**
 * Initialize cache busting on app start
 */
export const initializeCacheBusting = async (): Promise<boolean> => {
  try {
    const isCurrentVersion = isVersionCurrent();
    
    if (!isCurrentVersion) {
      console.log('Version mismatch detected, clearing cache...');
      await clearVersionCache();
      return true; // Indicates cache was cleared
    }
    
    return false; // No cache clearing needed
  } catch (error) {
    console.error('Failed to initialize cache busting:', error);
    return false;
  }
};

/**
 * Create cache-aware image loader
 */
export const loadImageWithCacheBusting = (src: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = addCacheBusting(src);
  });
};

/**
 * Preload critical resources with cache busting
 */
export const preloadCriticalResources = async (resources: string[]): Promise<void> => {
  const preloadPromises = resources.map(async (resource) => {
    try {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = addCacheBusting(resource);
      
      // Determine resource type
      if (resource.match(/\.(css)$/i)) {
        link.as = 'style';
      } else if (resource.match(/\.(js)$/i)) {
        link.as = 'script';
      } else if (resource.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
        link.as = 'image';
      }
      
      document.head.appendChild(link);
    } catch (error) {
      console.warn('Failed to preload resource:', resource, error);
    }
  });

  await Promise.allSettled(preloadPromises);
};

/**
 * Monitor for version updates
 */
export const monitorVersionUpdates = (onUpdateDetected: () => void): (() => void) => {
  let intervalId: number;

  const checkForUpdates = async () => {
    try {
      // Check if there's a new version available
      const response = await fetch('/version.json?' + Date.now(), {
        cache: 'no-cache'
      });
      
      if (response.ok) {
        const serverVersion = await response.json();
        const currentVersion = getCacheConfig().version;
        
        if (serverVersion.version !== currentVersion) {
          onUpdateDetected();
        }
      }
    } catch (error) {
      // Silently fail - version checking is not critical
    }
  };

  // Check every 5 minutes
  intervalId = window.setInterval(checkForUpdates, 5 * 60 * 1000);

  // Return cleanup function
  return () => {
    if (intervalId) {
      clearInterval(intervalId);
    }
  };
};
