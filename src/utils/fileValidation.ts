/**
 * File validation utilities with Android compatibility
 */

/**
 * Check if the current device is Android
 */
export const isAndroidDevice = (): boolean => {
  return /Android/i.test(navigator.userAgent);
};

/**
 * Validate video file with Android-specific considerations
 */
export const validateVideoFile = (file: File): { isValid: boolean; error?: string } => {
  const isAndroid = isAndroidDevice();
  
  // Check file size - be more lenient on Android due to compression differences
  const maxSize = isAndroid ? 60 * 1024 * 1024 : 50 * 1024 * 1024; // 60MB for Android, 50MB for others
  
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File is too large. Maximum size is ${maxSize / (1024 * 1024)}MB. Your file is ${(file.size / (1024 * 1024)).toFixed(2)}MB.`
    };
  }

  // Check file type - Android can report unusual MIME types
  const validTypes = [
    'video/mp4',
    'video/webm',
    'video/quicktime',
    'video/x-msvideo',
    'video/3gpp',
    'video/x-ms-wmv',
    'video/avi',
    'video/mov',
    'video/x-flv',
    'video/x-matroska',
    // Android-specific types
    'video/3gp',
    'video/mp2t',
    'video/x-m4v'
  ];

  const hasValidType = validTypes.includes(file.type) || file.type.startsWith('video/');
  
  // On Android, also check file extension if MIME type is weird
  if (!hasValidType && isAndroid) {
    const validExtensions = ['.mp4', '.webm', '.mov', '.avi', '.3gp', '.mkv', '.flv', '.wmv'];
    const fileName = file.name.toLowerCase();
    const hasValidExtension = validExtensions.some(ext => fileName.endsWith(ext));
    
    if (!hasValidExtension) {
      return {
        isValid: false,
        error: 'Unsupported video format. Please use MP4, WebM, MOV, AVI, or 3GP.'
      };
    }
  } else if (!hasValidType) {
    return {
      isValid: false,
      error: 'Unsupported video format. Please use MP4, WebM, MOV, or AVI.'
    };
  }

  return { isValid: true };
};

/**
 * Validate image file for thumbnails
 */
export const validateImageFile = (file: File): { isValid: boolean; error?: string } => {
  const isAndroid = isAndroidDevice();
  
  // Check file size
  const maxSize = 5 * 1024 * 1024; // 5MB
  
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `Thumbnail is too large. Maximum size is 5MB. Your file is ${(file.size / (1024 * 1024)).toFixed(2)}MB.`
    };
  }

  // Check file type
  const validTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/gif'
  ];

  const hasValidType = validTypes.includes(file.type) || file.type.startsWith('image/');
  
  if (!hasValidType && isAndroid) {
    // On Android, also check file extension
    const validExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
    const fileName = file.name.toLowerCase();
    const hasValidExtension = validExtensions.some(ext => fileName.endsWith(ext));
    
    if (!hasValidExtension) {
      return {
        isValid: false,
        error: 'Unsupported image format. Please use JPG, PNG, WebP, or GIF.'
      };
    }
  } else if (!hasValidType) {
    return {
      isValid: false,
      error: 'Unsupported image format. Please use JPG, PNG, WebP, or GIF.'
    };
  }

  return { isValid: true };
};

/**
 * Get optimized upload settings for the current device
 */
export const getUploadSettings = () => {
  const isAndroid = isAndroidDevice();
  
  return {
    chunkSize: isAndroid ? 1024 * 1024 : 2 * 1024 * 1024, // 1MB chunks for Android, 2MB for others
    timeout: isAndroid ? 30000 : 20000, // Longer timeout for Android
    retries: isAndroid ? 3 : 2, // More retries for Android
    quality: isAndroid ? 0.7 : 0.8, // Lower quality for Android to save bandwidth
    maxDimension: isAndroid ? 800 : 1200 // Smaller max dimension for Android
  };
};

/**
 * Check if the browser supports required features for video upload
 */
export const checkBrowserSupport = (): { isSupported: boolean; missingFeatures: string[] } => {
  const missingFeatures: string[] = [];
  
  // Check for File API support
  if (!window.File || !window.FileReader || !window.FileList || !window.Blob) {
    missingFeatures.push('File API');
  }
  
  // Check for Canvas support
  const canvas = document.createElement('canvas');
  if (!canvas.getContext || !canvas.getContext('2d')) {
    missingFeatures.push('Canvas 2D');
  }
  
  // Check for Video element support
  const video = document.createElement('video');
  if (!video.canPlayType) {
    missingFeatures.push('Video element');
  }
  
  // Check for URL.createObjectURL support
  if (!window.URL || !window.URL.createObjectURL) {
    missingFeatures.push('Object URLs');
  }
  
  return {
    isSupported: missingFeatures.length === 0,
    missingFeatures
  };
};

/**
 * Prepare file for upload with Android-specific optimizations
 */
export const prepareFileForUpload = async (file: File): Promise<File> => {
  const isAndroid = isAndroidDevice();
  
  // For Android, we might want to compress large files
  if (isAndroid && file.size > 30 * 1024 * 1024) { // 30MB threshold
    console.log('Large file detected on Android, considering compression...');
    // For now, just return the original file
    // In the future, we could implement client-side compression here
  }
  
  return file;
};
