/**
 * Browser State Manager - Handles browser state conflicts and cache issues
 */

export interface BrowserStateInfo {
  userAgent: string;
  timestamp: number;
  version: string;
  sessionId: string;
}

export interface StateConflictReport {
  hasConflicts: boolean;
  conflicts: string[];
  recommendations: string[];
}

/**
 * Generate a unique session ID for this browser session
 */
export const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Get current browser state information
 */
export const getBrowserStateInfo = (): BrowserStateInfo => {
  return {
    userAgent: navigator.userAgent,
    timestamp: Date.now(),
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    sessionId: generateSessionId()
  };
};

/**
 * Check for potential state conflicts in browser storage
 */
export const checkForStateConflicts = (): StateConflictReport => {
  const conflicts: string[] = [];
  const recommendations: string[] = [];

  try {
    // Check for stale auth tokens
    const supabaseAuth = localStorage.getItem('sb-lfnxllcoixgfkasdjtnj-auth-token');
    if (supabaseAuth) {
      try {
        const authData = JSON.parse(supabaseAuth);
        if (authData.expires_at && new Date(authData.expires_at * 1000) < new Date()) {
          conflicts.push('Expired authentication token found');
          recommendations.push('Clear authentication data and re-login');
        }
      } catch (e) {
        conflicts.push('Corrupted authentication data');
        recommendations.push('Clear authentication storage');
      }
    }

    // Check for version mismatches
    const storedVersion = localStorage.getItem('app-version');
    const currentVersion = import.meta.env.VITE_APP_VERSION || '1.0.0';
    if (storedVersion && storedVersion !== currentVersion) {
      conflicts.push(`Version mismatch: stored ${storedVersion}, current ${currentVersion}`);
      recommendations.push('Clear cached data for new version');
    }

    // Check for large localStorage usage
    let totalSize = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        totalSize += localStorage[key].length;
      }
    }

    if (totalSize > 5 * 1024 * 1024) { // 5MB
      conflicts.push('Large localStorage usage detected');
      recommendations.push('Consider clearing non-essential cached data');
    }

    // Check for conflicting user preferences
    const userPrefs = localStorage.getItem('user-preferences-storage');
    if (userPrefs) {
      try {
        const prefs = JSON.parse(userPrefs);
        if (prefs.state && Object.keys(prefs.state.watchHistory || {}).length > 1000) {
          conflicts.push('Large watch history detected');
          recommendations.push('Consider clearing old watch history');
        }
      } catch (e) {
        conflicts.push('Corrupted user preferences data');
        recommendations.push('Reset user preferences');
      }
    }

  } catch (error) {
    conflicts.push('Error checking browser state');
    recommendations.push('Clear all browser data');
  }

  return {
    hasConflicts: conflicts.length > 0,
    conflicts,
    recommendations
  };
};

/**
 * Clear specific types of browser state
 */
export const clearBrowserState = (options: {
  auth?: boolean;
  preferences?: boolean;
  cache?: boolean;
  all?: boolean;
} = {}) => {
  const { auth = false, preferences = false, cache = false, all = false } = options;

  if (all) {
    localStorage.clear();
    sessionStorage.clear();
    return;
  }

  // Clear authentication data
  if (auth) {
    const authKeys = Object.keys(localStorage).filter(key =>
      key.startsWith('sb-') || key.includes('auth') || key.includes('supabase')
    );
    authKeys.forEach(key => localStorage.removeItem(key));
  }

  // Clear user preferences
  if (preferences) {
    localStorage.removeItem('user-preferences-storage');
    localStorage.removeItem('disclaimerAccepted');
    localStorage.removeItem('recentSearches');
    localStorage.removeItem('anonymousLikedComments');
  }

  // Clear cache-related data
  if (cache) {
    sessionStorage.clear();
    // Clear any cache-related localStorage items
    const cacheKeys = Object.keys(localStorage).filter(key =>
      key.includes('cache') || key.includes('swr') || key.includes('query')
    );
    cacheKeys.forEach(key => localStorage.removeItem(key));
  }
};

/**
 * Force refresh browser state
 */
export const forceBrowserRefresh = async () => {
  // Clear service workers
  if ('serviceWorker' in navigator) {
    const registrations = await navigator.serviceWorker.getRegistrations();
    await Promise.all(registrations.map(reg => reg.unregister()));
  }

  // Clear caches
  if ('caches' in window) {
    const cacheNames = await caches.keys();
    await Promise.all(cacheNames.map(name => caches.delete(name)));
  }

  // Set version marker
  localStorage.setItem('app-version', import.meta.env.VITE_APP_VERSION || '1.0.0');
  localStorage.setItem('last-refresh', Date.now().toString());

  // Force reload
  window.location.reload();
};

/**
 * Safe storage operations with error handling
 */
export const safeStorage = {
  setItem: (key: string, value: string): boolean => {
    try {
      localStorage.setItem(key, value);
      return true;
    } catch (error) {
      // console.warn('Failed to set localStorage item:', key, error);
      return false;
    }
  },

  getItem: (key: string): string | null => {
    try {
      return localStorage.getItem(key);
    } catch (error) {
      // console.warn('Failed to get localStorage item:', key, error);
      return null;
    }
  },

  removeItem: (key: string): boolean => {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      // console.warn('Failed to remove localStorage item:', key, error);
      return false;
    }
  }
};
