/**
 * Test utilities for mobile upload functionality
 */

import { checkNetworkConditions, validateFileForUpload, isMobileDevice } from './uploadUtils';

/**
 * Test mobile upload conditions and provide recommendations
 */
export const testMobileUploadConditions = () => {
  const results = {
    isMobile: isMobileDevice(),
    networkConditions: checkNetworkConditions(),
    recommendations: [] as string[],
    warnings: [] as string[],
    canUpload: true
  };

  // Check device type
  if (results.isMobile) {
    results.recommendations.push('Mobile device detected - using optimized upload settings');
    
    // Check memory if available
    const memory = (navigator as any).deviceMemory;
    if (memory) {
      if (memory < 2) {
        results.warnings.push(`Low device memory (${memory}GB) - thumbnail generation may fail`);
      } else if (memory < 4) {
        results.warnings.push(`Limited device memory (${memory}GB) - consider smaller files`);
      }
    }
  }

  // Check network conditions
  if (!results.networkConditions.isOnline) {
    results.canUpload = false;
    results.warnings.push('No internet connection');
  } else if (results.networkConditions.isSlowConnection) {
    results.warnings.push('Slow connection detected - uploads may take longer');
    results.recommendations.push('Consider uploading smaller files or waiting for better connection');
  }

  // Check connection type
  if (results.networkConditions.connectionType) {
    results.recommendations.push(`Connection type: ${results.networkConditions.connectionType}`);
  }

  return results;
};

/**
 * Test file upload readiness
 */
export const testFileUploadReadiness = (file: File) => {
  const validation = validateFileForUpload(file);
  const conditions = testMobileUploadConditions();
  
  return {
    fileValidation: validation,
    deviceConditions: conditions,
    overallReadiness: validation.isValid && conditions.canUpload,
    recommendations: [
      ...conditions.recommendations,
      ...(validation.isValid ? [] : [validation.error || 'File validation failed'])
    ],
    warnings: conditions.warnings
  };
};

/**
 * Log mobile upload diagnostics
 */
export const logMobileUploadDiagnostics = () => {
  const conditions = testMobileUploadConditions();
  
  console.group('📱 Mobile Upload Diagnostics');
  console.log('Device Type:', conditions.isMobile ? 'Mobile' : 'Desktop');
  console.log('Network Status:', conditions.networkConditions);
  console.log('Can Upload:', conditions.canUpload);
  
  if (conditions.recommendations.length > 0) {
    console.log('Recommendations:', conditions.recommendations);
  }
  
  if (conditions.warnings.length > 0) {
    console.warn('Warnings:', conditions.warnings);
  }
  
  // Additional device info
  console.log('User Agent:', navigator.userAgent);
  console.log('Device Memory:', (navigator as any).deviceMemory || 'Unknown');
  console.log('Hardware Concurrency:', navigator.hardwareConcurrency || 'Unknown');
  
  console.groupEnd();
  
  return conditions;
};

/**
 * Monitor upload progress and detect issues
 */
export const createUploadMonitor = () => {
  let startTime = Date.now();
  let lastProgressTime = Date.now();
  let lastProgress = 0;
  let stuckCount = 0;
  
  return {
    updateProgress: (progress: number) => {
      const now = Date.now();
      const timeSinceStart = now - startTime;
      const timeSinceLastProgress = now - lastProgressTime;
      
      // Check if upload is stuck
      if (progress === lastProgress && timeSinceLastProgress > 30000) { // 30 seconds
        stuckCount++;
        console.warn(`Upload appears stuck at ${progress}% for ${timeSinceLastProgress}ms`);
        
        if (stuckCount > 2) {
          console.error('Upload has been stuck multiple times, may need to retry');
        }
      } else if (progress > lastProgress) {
        stuckCount = 0;
        lastProgressTime = now;
      }
      
      lastProgress = progress;
      
      // Log progress milestones
      if (progress % 25 === 0 && progress > 0) {
        const speed = (progress / (timeSinceStart / 1000)).toFixed(2);
        console.log(`Upload ${progress}% complete (${speed}%/sec)`);
      }
    },
    
    getStats: () => ({
      duration: Date.now() - startTime,
      stuckCount,
      lastProgress
    })
  };
};
