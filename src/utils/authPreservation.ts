/**
 * Utilities to help preserve authentication state during intensive operations
 * Particularly useful for mobile devices that may experience memory pressure
 */

import { useAuthStore } from '../stores/authStore';

/**
 * Check if the current device is likely to have memory constraints
 */
export const isLowMemoryDevice = (): boolean => {
  // Check if device memory API is available
  const memory = (navigator as any).deviceMemory;
  if (memory) {
    return memory < 2; // Less than 2GB RAM
  }

  // Fallback: check for mobile device
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  return isMobile;
};

/**
 * Create a function that periodically checks authentication state
 * and calls a callback if authentication is lost
 */
export const createAuthWatcher = (
  onAuthLost: () => void,
  intervalMs: number = 1000
): (() => void) => {
  let intervalId: NodeJS.Timeout;
  let isWatching = false;

  const startWatching = () => {
    if (isWatching) return;
    
    isWatching = true;
    intervalId = setInterval(() => {
      const { user } = useAuthStore.getState();
      if (!user) {
        stopWatching();
        onAuthLost();
      }
    }, intervalMs);
  };

  const stopWatching = () => {
    if (intervalId) {
      clearInterval(intervalId);
    }
    isWatching = false;
  };

  // Start watching immediately
  startWatching();

  // Return stop function
  return stopWatching;
};

/**
 * Execute a function with authentication monitoring
 * If authentication is lost during execution, the operation is cancelled
 */
export const withAuthMonitoring = async <T>(
  operation: () => Promise<T>,
  onAuthLost?: () => void
): Promise<T> => {
  return new Promise((resolve, reject) => {
    // Check initial auth state
    const { user: initialUser } = useAuthStore.getState();
    if (!initialUser) {
      reject(new Error('User not authenticated'));
      return;
    }

    let isCompleted = false;

    // Set up auth monitoring
    const stopWatching = createAuthWatcher(() => {
      if (!isCompleted) {
        isCompleted = true;
        onAuthLost?.();
        reject(new Error('Authentication lost during operation'));
      }
    });

    // Execute the operation
    operation()
      .then((result) => {
        if (!isCompleted) {
          isCompleted = true;
          stopWatching();
          resolve(result);
        }
      })
      .catch((error) => {
        if (!isCompleted) {
          isCompleted = true;
          stopWatching();
          reject(error);
        }
      });
  });
};

/**
 * Delay execution with authentication checking
 * Useful for preventing race conditions on mobile devices
 */
export const authSafeDelay = async (ms: number): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check auth state before delay
    const { user: initialUser } = useAuthStore.getState();
    if (!initialUser) {
      reject(new Error('User not authenticated'));
      return;
    }

    setTimeout(() => {
      // Check auth state after delay
      const { user: delayedUser } = useAuthStore.getState();
      if (!delayedUser) {
        reject(new Error('Authentication lost during delay'));
        return;
      }
      resolve();
    }, ms);
  });
};

/**
 * Preserve authentication state in localStorage as a backup
 * This can help recover from temporary auth state loss on mobile
 */
export const preserveAuthState = (): void => {
  try {
    const { user, profile } = useAuthStore.getState();
    if (user && profile) {
      const authBackup = {
        userId: user.id,
        userEmail: user.email,
        profileId: profile.id,
        timestamp: Date.now()
      };
      localStorage.setItem('auth_backup', JSON.stringify(authBackup));
    }
  } catch (error) {
    console.warn('Failed to preserve auth state:', error);
  }
};

/**
 * Attempt to restore authentication state from backup
 * Should only be used as a last resort
 */
export const attemptAuthRestore = (): boolean => {
  try {
    const backup = localStorage.getItem('auth_backup');
    if (!backup) return false;

    const authBackup = JSON.parse(backup);
    const age = Date.now() - authBackup.timestamp;
    
    // Only use backup if it's less than 5 minutes old
    if (age > 5 * 60 * 1000) {
      localStorage.removeItem('auth_backup');
      return false;
    }

    // Check if current auth state is actually lost
    const { user } = useAuthStore.getState();
    if (user) return true; // Auth is fine

    console.log('Attempting to restore auth state from backup');
    // Note: This would require additional implementation to actually restore the session
    // For now, we just indicate that a backup exists
    return true;
  } catch (error) {
    console.warn('Failed to restore auth state:', error);
    return false;
  }
};

/**
 * Clear authentication backup
 */
export const clearAuthBackup = (): void => {
  try {
    localStorage.removeItem('auth_backup');
  } catch (error) {
    console.warn('Failed to clear auth backup:', error);
  }
};
