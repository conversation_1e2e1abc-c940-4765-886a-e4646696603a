/**
 * Test MySQL API connection and data fetching
 */

import { apiClient } from '../lib/api';

export const testSupabaseConnection = async () => {
  console.log('🔍 Testing MySQL API connection...');

  try {
    // Test 1: Basic connection - fetch videos
    console.log('📡 Testing basic connection...');
    const response = await apiClient.getVideos({ limit: 1 });

    console.log('📡 Basic connection result:', { success: response.success });

    if (!response.success) {
      console.error('❌ Basic connection failed:', response.error);
      return false;
    }

    console.log('✅ Basic connection successful');

    // Test 2: Count videos
    console.log('📊 Counting videos...');
    const countResponse = await apiClient.getVideos({ limit: 1 });

    if (!countResponse.success) {
      console.error('❌ Count query failed:', countResponse.error);
      return false;
    }

    const totalCount = countResponse.data.pagination?.total || 0;
    console.log(`✅ Found ${totalCount} videos in database`);

    // Test 3: Fetch sample videos
    console.log('📹 Fetching sample videos...');
    const videosResponse = await apiClient.getVideos({
      limit: 5,
      sort: 'created_at',
      order: 'DESC'
    });

    if (!videosResponse.success) {
      console.error('❌ Video fetch failed:', videosResponse.error);
      return false;
    }

    const videos = videosResponse.data.videos;
    console.log(`✅ Successfully fetched ${videos?.length || 0} sample videos`);

    if (videos && videos.length > 0) {
      console.log('📋 Sample video data:');
      videos.forEach((video, index) => {
        console.log(`  ${index + 1}. ${video.title} (ID: ${video.id})`);
        console.log(`     Thumbnail: ${video.thumbnail_url || 'None'}`);
        console.log(`     Video URL: ${video.video_url || 'None'}`);
        console.log(`     Views: ${video.views || 0}`);
        console.log('');
      });
    }

    // Test 4: Test search functionality
    console.log('🔍 Testing search functionality...');
    const searchResponse = await apiClient.getVideos({
      search: 'video',
      limit: 3
    });

    if (!searchResponse.success) {
      console.error('❌ Search test failed:', searchResponse.error);
    } else {
      console.log(`✅ Search found ${searchResponse.data.videos?.length || 0} results`);
    }

    return true;

  } catch (error) {
    console.error('❌ MySQL API test failed with exception:', error);
    return false;
  }
};

// Auto-run test in development
if (import.meta.env.DEV) {
  // Run test after a short delay to ensure everything is loaded
  setTimeout(() => {
    testSupabaseConnection();
  }, 2000);
}
