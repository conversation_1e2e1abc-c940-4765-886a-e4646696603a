/**
 * Test Supabase connection and data fetching
 */

// import { supabase, getVideoQuery } from '../lib/supabase'; // Removed - using MySQL API

export const testSupabaseConnection = async () => {
  console.log('🔍 Testing Supabase connection...');

  try {
    // Test 1: Basic connection
    console.log('📡 Testing basic connection...');
    const { data: healthCheck, error: healthError } = await supabase
      .from('videos')
      .select('id')
      .limit(1);

    console.log('📡 Basic connection result:', { data: healthCheck, error: healthError });

    if (healthError) {
      console.error('❌ Basic connection failed:', healthError);
      return false;
    }

    console.log('✅ Basic connection successful');

    // Test 2: Count videos
    console.log('📊 Counting videos...');
    const { count, error: countError } = await supabase
      .from('videos')
      .select('id', { count: 'exact' });

    if (countError) {
      console.error('❌ Count query failed:', countError);
      return false;
    }

    console.log(`✅ Found ${count} videos in database`);

    // Test 3: Fetch sample videos
    console.log('📹 Fetching sample videos...');
    const { data: videos, error: videosError } = await getVideoQuery(
      supabase.from('videos')
    )
      .order('created_at', { ascending: false })
      .limit(5);

    if (videosError) {
      console.error('❌ Video fetch failed:', videosError);
      return false;
    }

    console.log(`✅ Successfully fetched ${videos?.length || 0} sample videos`);

    if (videos && videos.length > 0) {
      console.log('📋 Sample video data:');
      videos.forEach((video, index) => {
        console.log(`  ${index + 1}. ${video.title} (ID: ${video.id})`);
        console.log(`     Thumbnail: ${video.thumbnail_url || 'None'}`);
        console.log(`     Video URL: ${video.video_url || 'None'}`);
        console.log(`     Views: ${video.views || 0}`);
        console.log('');
      });
    }

    // Test 4: Check profiles table
    console.log('👤 Testing profiles table...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username')
      .limit(3);

    if (profilesError) {
      console.error('❌ Profiles fetch failed:', profilesError);
    } else {
      console.log(`✅ Found ${profiles?.length || 0} profiles`);
    }

    return true;

  } catch (error) {
    console.error('❌ Supabase test failed with exception:', error);
    return false;
  }
};

// Auto-run test in development
if (import.meta.env.DEV) {
  // Run test after a short delay to ensure everything is loaded
  setTimeout(() => {
    testSupabaseConnection();
  }, 2000);
}
