/**
 * SWR Utilities - Helper functions for managing SWR cache and state
 */

import { mutate } from 'swr';

/**
 * Clear all SWR cache
 */
export const clearSWRCache = async (): Promise<void> => {
  try {
    // Clear all SWR cache entries
    await mutate(() => true, undefined, { revalidate: false });
    // console.log('✅ SWR cache cleared');
  } catch (error) {
    // console.error('❌ Failed to clear SWR cache:', error);
  }
};

/**
 * Clear specific SWR cache keys
 */
export const clearSWRKey = async (key: string): Promise<void> => {
  try {
    await mutate(key, undefined, { revalidate: false });
    // console.log('✅ SWR key cleared:', key);
  } catch (error) {
    // console.error('❌ Failed to clear SWR key:', key, error);
  }
};

/**
 * Force revalidate SWR cache
 */
export const revalidateSWR = async (key?: string): Promise<void> => {
  try {
    if (key) {
      await mutate(key);
      // console.log('✅ SWR key revalidated:', key);
    } else {
      await mutate(() => true);
      // console.log('✅ All SWR cache revalidated');
    }
  } catch (error) {
    // console.error('❌ Failed to revalidate SWR:', error);
  }
};

/**
 * Clear video-related SWR cache
 */
export const clearVideoCache = async (): Promise<void> => {
  try {
    // Clear all video-related cache keys
    const videoKeys = [
      /^videos\//,
      /^trending\//,
      /^video\//,
    ];

    for (const keyPattern of videoKeys) {
      await mutate(keyPattern, undefined, { revalidate: false });
    }

    // console.log('✅ Video cache cleared');
  } catch (error) {
    // console.error('❌ Failed to clear video cache:', error);
  }
};

/**
 * Reset SWR and browser state
 */
export const resetSWRState = async (): Promise<void> => {
  try {
    // Clear SWR cache
    await clearSWRCache();

    // Clear browser storage
    localStorage.removeItem('swr-cache');
    sessionStorage.removeItem('swr-cache');

    // Clear any SWR-related items in localStorage
    const keysToRemove = Object.keys(localStorage).filter(key =>
      key.includes('swr') || key.includes('cache')
    );

    keysToRemove.forEach(key => localStorage.removeItem(key));

    // console.log('✅ SWR state reset complete');
  } catch (error) {
    // console.error('❌ Failed to reset SWR state:', error);
  }
};

/**
 * Debug SWR cache state
 */
export const debugSWRCache = (): void => {
  // console.log('🔍 SWR Cache Debug Info:');
  // console.log('- Current URL:', window.location.href);
  // console.log('- User Agent:', navigator.userAgent);
  // console.log('- Local Storage keys:', Object.keys(localStorage));
  // console.log('- Session Storage keys:', Object.keys(sessionStorage));

  // Check for SWR-related items
  const swrItems = Object.keys(localStorage).filter(key =>
    key.includes('swr') || key.includes('cache')
  );

  if (swrItems.length > 0) {
    // console.log('- SWR-related localStorage items:', swrItems);
  } else {
    // console.log('- No SWR-related localStorage items found');
  }
};
