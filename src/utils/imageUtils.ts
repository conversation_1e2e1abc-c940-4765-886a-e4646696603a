/**
 * Utility functions for handling images
 */

/**
 * Returns a data URL for a placeholder image with the specified dimensions and text
 * This avoids external dependencies on placeholder image services
 * @param width Width of the placeholder image
 * @param height Height of the placeholder image
 * @param text Text to display on the placeholder
 * @returns A data URL for the placeholder image
 */
export const getPlaceholderImage = (
  width: number = 640,
  height: number = 360,
  text: string = 'No Image'
): string => {
  // Return a simple data URL for a gray placeholder
  return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${width}' height='${height}' viewBox='0 0 ${width} ${height}'%3E%3Crect width='${width}' height='${height}' fill='%23333'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial, sans-serif' font-size='24' fill='%23fff' text-anchor='middle' dominant-baseline='middle'%3E${text}%3C/text%3E%3C/svg%3E`;
};

/**
 * Returns a data URL for a placeholder avatar
 * @param size Size of the avatar
 * @param text Text to display on the avatar
 * @returns A data URL for the placeholder avatar
 */
export const getPlaceholderAvatar = (
  size: number = 150,
  text: string = 'User'
): string => {
  // Return a simple data URL for a blue circular avatar
  return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${size}' height='${size}' viewBox='0 0 ${size} ${size}'%3E%3Ccircle cx='${size/2}' cy='${size/2}' r='${size/2}' fill='%232563eb'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial, sans-serif' font-size='${size/4}' fill='%23fff' text-anchor='middle' dominant-baseline='middle'%3E${text.charAt(0).toUpperCase()}%3C/text%3E%3C/svg%3E`;
};

/**
 * Returns a placeholder thumbnail for videos
 * @returns A data URL for a video thumbnail placeholder
 */
export const getVideoThumbnailPlaceholder = (): string => {
  return getPlaceholderImage(640, 360, 'No Thumbnail');
};
