import { create } from 'zustand';

interface DisclaimerState {
  hasAcceptedDisclaimer: boolean;
  acceptDisclaimer: () => void;
  checkDisclaimerStatus: () => void;
}

export const useDisclaimerStore = create<DisclaimerState>((set) => ({
  // Initialize as false to ensure the disclaimer shows by default
  hasAcceptedDisclaimer: false,

  acceptDisclaimer: () => {
    // Store with timestamp to potentially expire the acceptance after some time
    const acceptanceData = {
      accepted: true,
      timestamp: new Date().getTime()
    };
    localStorage.setItem('disclaimerAccepted', JSON.stringify(acceptanceData));
    set({ hasAcceptedDisclaimer: true });
  },

  checkDisclaimerStatus: () => {
    try {
      // Check if the user has accepted the disclaimer
      const storedValue = localStorage.getItem('disclaimerAccepted');

      if (!storedValue) {
        // No stored value means first-time visitor
        set({ hasAcceptedDisclaimer: false });
        return;
      }

      // Parse the stored JSON data
      const acceptanceData = JSON.parse(storedValue);

      // If the data is in the old format (just a string 'true'), convert it
      if (typeof acceptanceData === 'boolean' || acceptanceData === 'true') {
        set({ hasAcceptedDisclaimer: true });
        return;
      }

      // Check if the acceptance is valid
      if (acceptanceData && acceptanceData.accepted) {
        set({ hasAcceptedDisclaimer: true });
      } else {
        set({ hasAcceptedDisclaimer: false });
      }
    } catch (error) {
      // If there's any error parsing, default to showing the disclaimer
      console.error('Error checking disclaimer status:', error);
      set({ hasAcceptedDisclaimer: false });
    }
  },
}));
