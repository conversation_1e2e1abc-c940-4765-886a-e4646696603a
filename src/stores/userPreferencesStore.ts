import { create } from 'zustand';
import { persist } from 'zustand/middleware';
// import { supabase } from '../lib/supabase'; // Removed - using MySQL API
import { VideoProgress } from '../types';

interface UserPreferencesState {
  watchHistory: Record<string, VideoProgress>;
  likedVideos: string[];
  watchLater: string[];
  viewedCategories: Record<string, number>;
  isLoading: boolean;
  error: string | null;

  // Watch history functions
  updateWatchProgress: (videoId: string, currentTime: number, duration: number) => void;
  getWatchProgress: (videoId: string) => VideoProgress | null;
  getContinueWatchingVideos: () => Promise<string[]>;

  // Video interactions
  likeVideo: (videoId: string) => Promise<boolean>;
  unlikeVideo: (videoId: string) => Promise<boolean>;
  isVideoLiked: (videoId: string) => boolean;
  addToWatchLater: (videoId: string) => void;
  removeFromWatchLater: (videoId: string) => void;
  isInWatchLater: (videoId: string) => boolean;

  // Category tracking
  trackCategoryView: (category: string) => void;
  getMostViewedCategories: (limit?: number) => string[];
}

export const useUserPreferencesStore = create<UserPreferencesState>()(
  persist(
    (set, get) => ({
      watchHistory: {},
      likedVideos: [],
      watchLater: [],
      viewedCategories: {},
      isLoading: false,
      error: null,

      // Watch history functions
      updateWatchProgress: (videoId, currentTime, duration) => {
        const percent = Math.min(Math.round((currentTime / duration) * 100), 100);
        const completed = percent >= 90; // Consider video completed if watched 90% or more

        set(state => ({
          watchHistory: {
            ...state.watchHistory,
            [videoId]: {
              currentTime,
              duration,
              percent,
              lastWatched: new Date().toISOString(),
              completed
            }
          }
        }));

        // If user is authenticated, sync to database
        const syncToDatabase = async () => {
          const { data: { user } } = await supabase.auth.getUser();

          if (user) {
            const { error } = await supabase
              .from('watch_history')
              .upsert({
                user_id: user.id,
                video_id: videoId,
                playback_position: currentTime,
                duration,
                percent,
                last_watched: new Date().toISOString(),
                completed
              }, {
                onConflict: 'user_id,video_id'
              });

            if (error) {
              console.error('Error syncing watch history:', error);
            }
          }
        };

        syncToDatabase();
      },

      getWatchProgress: (videoId) => {
        return get().watchHistory[videoId] || null;
      },

      getContinueWatchingVideos: async () => {
        set({ isLoading: true, error: null });

        try {
          const { data: { user } } = await supabase.auth.getUser();

          if (!user) {
            // If not authenticated, use local storage data
            const watchHistory = get().watchHistory;

            // Filter for videos that are not completed and were watched in the last 30 days
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const continueWatchingIds = Object.entries(watchHistory)
              .filter(([_, progress]) => {
                const lastWatched = new Date(progress.lastWatched);
                return !progress.completed && lastWatched > thirtyDaysAgo;
              })
              .sort(([_, a], [__, b]) => {
                return new Date(b.lastWatched).getTime() - new Date(a.lastWatched).getTime();
              })
              .map(([id, _]) => id);

            set({ isLoading: false });
            return continueWatchingIds;
          }

          // If authenticated, get from database
          const { data, error } = await supabase
            .from('watch_history')
            .select('video_id, last_watched, completed, percent')
            .eq('user_id', user.id)
            .eq('completed', false)
            .gt('percent', 5) // At least watched 5%
            .lt('percent', 90) // But not more than 90%
            .order('last_watched', { ascending: false })
            .limit(10);

          if (error) {
            throw new Error(error.message);
          }

          // Update local state with database data
          const watchHistory = { ...get().watchHistory };

          data.forEach(item => {
            watchHistory[item.video_id] = {
              currentTime: 0, // We'll get the actual time when loading the video
              duration: 0,
              percent: item.percent,
              lastWatched: item.last_watched,
              completed: item.completed
            };
          });

          set({ watchHistory, isLoading: false });
          return data.map(item => item.video_id);
        } catch (error) {
          console.error('Error fetching continue watching videos:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false
          });
          return [];
        }
      },



      // Video interactions
      likeVideo: async (videoId) => {
        set({ isLoading: true, error: null });

        try {
          const { data: { user } } = await supabase.auth.getUser();

          if (!user) {
            // If not authenticated, just update local state
            set(state => ({
              likedVideos: [...state.likedVideos, videoId],
              isLoading: false
            }));
            return true;
          }

          // If authenticated, update database
          const { error } = await supabase
            .from('video_likes')
            .insert({
              user_id: user.id,
              video_id: videoId,
              liked_at: new Date().toISOString()
            });

          if (error) {
            throw new Error(error.message);
          }

          // Update video likes count
          await supabase.rpc('increment_video_likes', { video_id: videoId });

          // Update local state
          set(state => ({
            likedVideos: [...state.likedVideos, videoId],
            isLoading: false
          }));

          return true;
        } catch (error) {
          console.error('Error liking video:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false
          });
          return false;
        }
      },

      unlikeVideo: async (videoId) => {
        set({ isLoading: true, error: null });

        try {
          const { data: { user } } = await supabase.auth.getUser();

          if (!user) {
            // If not authenticated, just update local state
            set(state => ({
              likedVideos: state.likedVideos.filter(id => id !== videoId),
              isLoading: false
            }));
            return true;
          }

          // If authenticated, update database
          const { error } = await supabase
            .from('video_likes')
            .delete()
            .eq('user_id', user.id)
            .eq('video_id', videoId);

          if (error) {
            throw new Error(error.message);
          }

          // Update video likes count
          await supabase.rpc('decrement_video_likes', { video_id: videoId });

          // Update local state
          set(state => ({
            likedVideos: state.likedVideos.filter(id => id !== videoId),
            isLoading: false
          }));

          return true;
        } catch (error) {
          console.error('Error unliking video:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false
          });
          return false;
        }
      },

      isVideoLiked: (videoId) => {
        return get().likedVideos.includes(videoId);
      },

      addToWatchLater: (videoId) => {
        set(state => ({
          watchLater: [...state.watchLater, videoId]
        }));

        // If user is authenticated, sync to database
        const syncToDatabase = async () => {
          const { data: { user } } = await supabase.auth.getUser();

          if (user) {
            const { error } = await supabase
              .from('watch_later')
              .insert({
                user_id: user.id,
                video_id: videoId,
                added_at: new Date().toISOString()
              });

            if (error) {
              console.error('Error adding to watch later:', error);
            }
          }
        };

        syncToDatabase();
      },

      removeFromWatchLater: (videoId) => {
        set(state => ({
          watchLater: state.watchLater.filter(id => id !== videoId)
        }));

        // If user is authenticated, sync to database
        const syncToDatabase = async () => {
          const { data: { user } } = await supabase.auth.getUser();

          if (user) {
            const { error } = await supabase
              .from('watch_later')
              .delete()
              .eq('user_id', user.id)
              .eq('video_id', videoId);

            if (error) {
              console.error('Error removing from watch later:', error);
            }
          }
        };

        syncToDatabase();
      },

      isInWatchLater: (videoId) => {
        return get().watchLater.includes(videoId);
      },

      // Category tracking
      trackCategoryView: (category) => {
        if (!category) return;

        set(state => {
          const viewedCategories = { ...state.viewedCategories };
          viewedCategories[category] = (viewedCategories[category] || 0) + 1;

          return { viewedCategories };
        });
      },

      getMostViewedCategories: (limit = 5) => {
        const viewedCategories = get().viewedCategories;

        return Object.entries(viewedCategories)
          .sort(([_, a], [__, b]) => b - a)
          .slice(0, limit)
          .map(([category, _]) => category);
      }
    }),
    {
      name: 'user-preferences-storage',
      partialize: (state) => ({
        watchHistory: state.watchHistory,
        likedVideos: state.likedVideos,
        watchLater: state.watchLater,
        viewedCategories: state.viewedCategories
      })
    }
  )
);
