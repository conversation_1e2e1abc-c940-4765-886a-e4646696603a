import { create } from 'zustand';
// import { supabase } from '../lib/supabase'; // Removed - using MySQL API
import { useAuthStore } from './authStore';
import { uploadFile, getPublicUrl } from '../lib/namecheapStorage';
import { validateFileForUpload, checkNetworkConditions, isMobileDevice } from '../utils/uploadUtils';

interface UploadState {
  isUploading: boolean;
  uploadProgress: number;
  uploadError: string | null;
  uploadedVideoUrl: string | null;
  uploadedThumbnailUrl: string | null;
}

interface UploadActions {
  uploadVideo: (data: {
    title: string;
    description: string;
    category: string;
    tags?: string[];
    videoFile: File;
    thumbnailFile?: File | null;
  }) => Promise<string | null>;
  resetUpload: () => void;
  setUploadProgress: (progress: number) => void;
}

type UploadStore = UploadState & UploadActions;

export const useUploadStore = create<UploadStore>((set, get) => ({
  // State
  isUploading: false,
  uploadProgress: 0,
  uploadError: null,
  uploadedVideoUrl: null,
  uploadedThumbnailUrl: null,

  // Actions
  setUploadProgress: (progress: number) => {
    set({ uploadProgress: progress });
  },

  resetUpload: () => {
    set({
      isUploading: false,
      uploadProgress: 0,
      uploadError: null,
      uploadedVideoUrl: null,
      uploadedThumbnailUrl: null,
    });
  },

  uploadVideo: async (data: {
    title: string;
    description: string;
    category: string;
    tags?: string[];
    videoFile: File;
    thumbnailFile?: File | null;
  }) => {
    const { title, description, category, tags, videoFile, thumbnailFile } = data;
    const { user } = useAuthStore.getState();
    if (!user) {
      throw new Error('User must be logged in to upload videos');
    }

    set({
      isUploading: true,
      uploadProgress: 0,
      uploadError: null,
      uploadedVideoUrl: null,
      uploadedThumbnailUrl: null,
    });

    try {
      // Validate files before upload
      const videoValidation = validateFileForUpload(videoFile, 'video');
      if (!videoValidation.isValid) {
        throw new Error(videoValidation.error || 'Invalid video file');
      }

      if (thumbnailFile) {
        const thumbnailValidation = validateFileForUpload(thumbnailFile, 'image');
        if (!thumbnailValidation.isValid) {
          throw new Error(thumbnailValidation.error || 'Invalid thumbnail file');
        }
      }

      // Check network conditions
      const networkConditions = await checkNetworkConditions();
      const isMobile = isMobileDevice();
      const isLargeFile = videoFile.size > 50 * 1024 * 1024; // 50MB

      console.log('Upload conditions:', {
        isMobile,
        isLargeFile,
        networkConditions,
        fileSize: videoFile.size
      });

      set({ uploadProgress: 5 });

      // 1. Upload video file
      let videoUrl: string;
      try {
        console.log('Starting video upload...');
        
        const uploadResult = await uploadFile(
          videoFile,
          'video',
          (progress) => {
            // Video upload takes 75% of total progress
            const percentage = Math.round((progress.percentage * 0.75));
            set({ uploadProgress: 5 + percentage });
          }
        );

        videoUrl = uploadResult.url;
        console.log('Video uploaded successfully:', videoUrl);
      } catch (error) {
        console.error('Video upload failed:', error);
        throw new Error(`Video upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      set({ uploadProgress: 85, uploadedVideoUrl: videoUrl });

      // 2. Upload thumbnail if provided
      let thumbnailUrl: string | null = null;
      if (thumbnailFile) {
        try {
          console.log('Starting thumbnail upload...');
          
          const thumbnailResult = await uploadFile(
            thumbnailFile,
            'image',
            (progress) => {
              // Thumbnail upload takes remaining 10% of progress
              const percentage = Math.round((progress.percentage * 0.1));
              set({ uploadProgress: 85 + percentage });
            }
          );

          thumbnailUrl = thumbnailResult.url;
          console.log('Thumbnail uploaded successfully:', thumbnailUrl);
        } catch (error) {
          console.error('Thumbnail upload failed:', error);
          // Continue without thumbnail instead of failing the whole upload
          console.warn('Continuing upload without thumbnail');
        }
      }

      set({ uploadProgress: 95, uploadedThumbnailUrl: thumbnailUrl });

      // 3. Insert video record in the database
      try {
        console.log('Saving video metadata to database...');
        
        const videoData: any = {
          title,
          description,
          video_url: videoUrl,
          thumbnail_url: thumbnailUrl,
          user_id: user.id,
          category,
          is_hd: true, // Assuming all uploads are HD for now
        };

        const { data: videoRecord, error: insertError } = await supabase
          .from('videos')
          .insert(videoData)
          .select()
          .single();

        if (insertError) {
          console.error('Database insert error:', insertError);
          throw new Error(`Failed to save video metadata: ${insertError.message}`);
        }

        console.log('Video metadata saved successfully:', videoRecord);
        set({ uploadProgress: 100 });

        // Success! Reset state after a brief delay
        setTimeout(() => {
          get().resetUpload();
        }, 2000);

        return videoRecord.id;

      } catch (error) {
        console.error('Database operation failed:', error);
        throw new Error(`Failed to save video metadata: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

    } catch (error) {
      console.error('Upload failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      
      set({
        isUploading: false,
        uploadError: errorMessage,
        uploadProgress: 0,
      });

      throw error;
    }
  },
}));

// Helper function to check if storage buckets exist (for compatibility)
const checkBucketExists = async (bucketName: string): Promise<boolean> => {
  // For Namecheap storage, we assume directories exist or will be created
  // This is mainly for compatibility with existing code
  console.log(`Checking bucket: ${bucketName} (Namecheap storage)`);
  return true;
};

// Export for backward compatibility
export { checkBucketExists };
