export interface User {
  id: string;
  email: string;
  avatar: string;
  isVerified: boolean;
  isCreator: boolean;
  subscriberCount?: number;
  isSubscribed?: boolean;
}

export interface VideoAnalytics {
  viewsLast7Days: number;
  viewsLast30Days: number;
  likesLast7Days: number;
  likesLast30Days: number;
  viewsByDay: { date: string; count: number }[];
  viewsByCountry?: { country: string; count: number }[];
  viewsByDevice?: { device: string; count: number }[];
}

export type VideoStatus = 'public' | 'private' | 'scheduled';

export interface VideoProgress {
  currentTime: number;
  duration: number;
  percent: number;
  lastWatched: string;
  completed: boolean;
}

export interface Video {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl: string;
  duration: number;
  views: number;
  likes: number;
  createdAt: string;
  updatedAt?: string;
  publishedAt?: string;
  scheduledFor?: string;
  status: VideoStatus;
  isHD: boolean;
  isPremium: boolean;
  price?: number;
  tags: string[];
  category: string;
  originalCategory?: string;
  creator: User;
  analytics?: VideoAnalytics;
  progress?: VideoProgress;
  similarity?: number; // For recommendation algorithm
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  count?: number;
  isSpecial?: boolean;
}

export interface Comment {
  id: string;
  content: string;
  createdAt: string;
  user: User;
  likes: number;
}

