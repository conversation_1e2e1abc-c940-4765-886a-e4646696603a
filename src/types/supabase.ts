export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          username: string;
          avatar_url: string | null;
          created_at: string;
          updated_at: string;
          is_approved: boolean;
        };
        Insert: {
          id: string;
          username: string;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
          is_approved?: boolean;
        };
        Update: {
          id?: string;
          username?: string;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
          is_approved?: boolean;
        };
      };
      videos: {
        Row: {
          id: string;
          title: string;
          description: string | null;
          thumbnail_url: string | null;
          video_url: string;
          duration: number;
          views: number;
          likes: number;
          is_hd: boolean;
          user_id: string;
          created_at: string;
          updated_at: string;
          category: string;
          is_part_of_multi_upload: boolean | null;
          is_first_in_multi_upload: boolean | null;
          total_parts_in_multi_upload: number | null;
          tags: any | null;
        };
        Insert: {
          id?: string;
          title: string;
          description?: string | null;
          thumbnail_url?: string | null;
          video_url: string;
          duration?: number;
          views?: number;
          likes?: number;
          is_hd?: boolean;
          user_id: string;
          created_at?: string;
          updated_at?: string;
          category: string;
          is_part_of_multi_upload?: boolean | null;
          is_first_in_multi_upload?: boolean | null;
          total_parts_in_multi_upload?: number | null;
          tags?: any | null;
        };
        Update: {
          id?: string;
          title?: string;
          description?: string | null;
          thumbnail_url?: string | null;
          video_url?: string;
          duration?: number;
          views?: number;
          likes?: number;
          is_hd?: boolean;
          user_id?: string;
          created_at?: string;
          updated_at?: string;
          category?: string;
          is_part_of_multi_upload?: boolean | null;
          is_first_in_multi_upload?: boolean | null;
          total_parts_in_multi_upload?: number | null;
          tags?: any | null;
        };
      };
      video_tags: {
        Row: {
          id: string;
          video_id: string;
          tag: string;
        };
        Insert: {
          id?: string;
          video_id: string;
          tag: string;
        };
        Update: {
          id?: string;
          video_id?: string;
          tag?: string;
        };
      };
      categories: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      collections: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          thumbnail_url: string | null;
          user_id: string;
          is_public: boolean;
          video_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          thumbnail_url?: string | null;
          user_id: string;
          is_public?: boolean;
          video_count?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          thumbnail_url?: string | null;
          user_id?: string;
          is_public?: boolean;
          video_count?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      collection_videos: {
        Row: {
          id: string;
          collection_id: string;
          video_id: string;
          position: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          collection_id: string;
          video_id: string;
          position?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          collection_id?: string;
          video_id?: string;
          position?: number;
          created_at?: string;
        };
      };
      comments: {
        Row: {
          id: string;
          video_id: string;
          user_id: string;
          content: string;
          likes: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          video_id: string;
          user_id: string;
          content: string;
          likes?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          video_id?: string;
          user_id?: string;
          content?: string;
          likes?: number;
          created_at?: string;
        };
      };
      comment_replies: {
        Row: {
          id: string;
          comment_id: string;
          user_id: string;
          content: string;
          likes: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          comment_id: string;
          user_id: string;
          content: string;
          likes?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          comment_id?: string;
          user_id?: string;
          content?: string;
          likes?: number;
          created_at?: string;
        };
      };
      favorites: {
        Row: {
          id: string;
          user_id: string;
          video_id: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          video_id: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          video_id?: string;
          created_at?: string;
        };
      };
      watch_history: {
        Row: {
          id: string;
          user_id: string;
          video_id: string;
          playback_position: number;
          duration: number;
          percent: number;
          completed: boolean;
          last_watched: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          video_id: string;
          playback_position?: number;
          duration?: number;
          percent?: number;
          completed?: boolean;
          last_watched?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          video_id?: string;
          playback_position?: number;
          duration?: number;
          percent?: number;
          completed?: boolean;
          last_watched?: string;
          created_at?: string;
        };
      };

      user_roles: {
        Row: {
          id: string;
          user_id: string;
          role: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          role: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          role?: string;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      is_user_admin: {
        Args: {
          user_id: string;
        };
        Returns: boolean;
      };
      is_user_approved: {
        Args: {
          user_id: string;
        };
        Returns: boolean;
      };
      approve_user: {
        Args: {
          user_id: string;
        };
        Returns: void;
      };
      create_missing_profiles: {
        Args: Record<string, never>;
        Returns: void;
      };
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
