/**
 * Custom hook for image optimization and performance
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  detectImageFormatSupport, 
  getOptimizedImageUrlCached, 
  preloadOptimizedImages,
  createLazyImageObserver 
} from '../utils/imageOptimization';

interface UseImageOptimizationOptions {
  width?: number;
  height?: number;
  quality?: number;
  priority?: boolean;
  progressive?: boolean;
  enablePreload?: boolean;
}

interface UseImageOptimizationReturn {
  optimizedSrc: string;
  isLoading: boolean;
  isLoaded: boolean;
  error: string | null;
  formatSupport: { webp: boolean; avif: boolean } | null;
  preloadImages: (urls: string[]) => Promise<void>;
}

export const useImageOptimization = (
  src: string,
  options: UseImageOptimizationOptions = {}
): UseImageOptimizationReturn => {
  const {
    width = 640,
    height,
    quality = 80,
    priority = false,
    progressive = true,
    enablePreload = true
  } = options;

  const [optimizedSrc, setOptimizedSrc] = useState<string>(src);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [formatSupport, setFormatSupport] = useState<{ webp: boolean; avif: boolean } | null>(null);
  
  const observerRef = useRef<IntersectionObserver | null>(null);
  const imgRef = useRef<HTMLImageElement | null>(null);

  // Detect format support on mount
  useEffect(() => {
    detectImageFormatSupport().then(setFormatSupport);
  }, []);

  // Optimize image URL when src or options change
  useEffect(() => {
    if (!src) return;

    const optimizeImage = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const optimized = await getOptimizedImageUrlCached(src, width, height, quality);
        setOptimizedSrc(optimized);
        
        // If priority, preload immediately
        if (priority && enablePreload) {
          const link = document.createElement('link');
          link.rel = 'preload';
          link.as = 'image';
          link.href = optimized;
          link.setAttribute('fetchpriority', 'high');
          document.head.appendChild(link);
        }
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to optimize image');
        setOptimizedSrc(src); // Fallback to original
      } finally {
        setIsLoading(false);
      }
    };

    optimizeImage();
  }, [src, width, height, quality, priority, enablePreload]);

  // Handle image load events
  const handleImageLoad = useCallback(() => {
    setIsLoaded(true);
    setIsLoading(false);
  }, []);

  const handleImageError = useCallback(() => {
    setError('Failed to load image');
    setIsLoading(false);
  }, []);

  // Preload multiple images
  const preloadImages = useCallback(async (urls: string[]) => {
    if (!enablePreload) return;
    
    try {
      await preloadOptimizedImages(urls, {
        width,
        height,
        quality,
        priority: priority ? 'high' : 'low',
        maxConcurrent: 3
      });
    } catch (err) {
      console.warn('Failed to preload images:', err);
    }
  }, [width, height, quality, priority, enablePreload]);

  return {
    optimizedSrc,
    isLoading,
    isLoaded,
    error,
    formatSupport,
    preloadImages
  };
};

/**
 * Hook for lazy loading images with intersection observer
 */
export const useLazyImage = (
  src: string,
  options: UseImageOptimizationOptions & {
    rootMargin?: string;
    threshold?: number;
  } = {}
) => {
  const {
    rootMargin = '50px',
    threshold = 0.1,
    ...imageOptions
  } = options;

  const [isInView, setIsInView] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(imageOptions.priority || false);
  const imgRef = useRef<HTMLElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const imageOptimization = useImageOptimization(
    shouldLoad ? src : '',
    imageOptions
  );

  // Set up intersection observer
  useEffect(() => {
    if (imageOptions.priority || shouldLoad) return;

    const element = imgRef.current;
    if (!element) return;

    observerRef.current = createLazyImageObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsInView(true);
          setShouldLoad(true);
          observerRef.current?.disconnect();
        }
      },
      { rootMargin, threshold }
    );

    observerRef.current.observe(element);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [imageOptions.priority, shouldLoad, rootMargin, threshold]);

  return {
    ...imageOptimization,
    isInView,
    shouldLoad,
    imgRef
  };
};

/**
 * Hook for progressive image loading
 */
export const useProgressiveImage = (
  src: string,
  options: UseImageOptimizationOptions = {}
) => {
  const [lowQualityLoaded, setLowQualityLoaded] = useState(false);
  const [highQualityLoaded, setHighQualityLoaded] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string>('');

  const lowQualityOptions = { ...options, width: 40, quality: 30 };
  const highQualityOptions = options;

  const lowQuality = useImageOptimization(src, lowQualityOptions);
  const highQuality = useImageOptimization(src, highQualityOptions);

  // Load low quality first
  useEffect(() => {
    if (lowQuality.optimizedSrc && !lowQualityLoaded) {
      const img = new Image();
      img.onload = () => {
        setCurrentSrc(lowQuality.optimizedSrc);
        setLowQualityLoaded(true);
        
        // Start loading high quality
        const highQualityImg = new Image();
        highQualityImg.onload = () => {
          setCurrentSrc(highQuality.optimizedSrc);
          setHighQualityLoaded(true);
        };
        highQualityImg.src = highQuality.optimizedSrc;
      };
      img.src = lowQuality.optimizedSrc;
    }
  }, [lowQuality.optimizedSrc, highQuality.optimizedSrc, lowQualityLoaded]);

  return {
    src: currentSrc,
    isLowQualityLoaded: lowQualityLoaded,
    isHighQualityLoaded: highQualityLoaded,
    isLoading: !lowQualityLoaded,
    shouldBlur: lowQualityLoaded && !highQualityLoaded
  };
};

/**
 * Hook for batch image preloading
 */
export const useImagePreloader = () => {
  const [preloadedUrls, setPreloadedUrls] = useState<Set<string>>(new Set());
  const [isPreloading, setIsPreloading] = useState(false);

  const preloadBatch = useCallback(async (
    urls: string[],
    options: {
      width?: number;
      height?: number;
      quality?: number;
      priority?: 'high' | 'low';
      batchSize?: number;
    } = {}
  ) => {
    const { batchSize = 3, ...preloadOptions } = options;
    
    setIsPreloading(true);
    
    try {
      // Filter out already preloaded URLs
      const urlsToPreload = urls.filter(url => !preloadedUrls.has(url));
      
      if (urlsToPreload.length === 0) {
        setIsPreloading(false);
        return;
      }

      await preloadOptimizedImages(urlsToPreload, {
        ...preloadOptions,
        maxConcurrent: batchSize
      });

      // Mark URLs as preloaded
      setPreloadedUrls(prev => {
        const newSet = new Set(prev);
        urlsToPreload.forEach(url => newSet.add(url));
        return newSet;
      });
      
    } catch (error) {
      console.warn('Batch preload failed:', error);
    } finally {
      setIsPreloading(false);
    }
  }, [preloadedUrls]);

  const clearPreloadCache = useCallback(() => {
    setPreloadedUrls(new Set());
  }, []);

  return {
    preloadBatch,
    clearPreloadCache,
    isPreloading,
    preloadedCount: preloadedUrls.size
  };
};
