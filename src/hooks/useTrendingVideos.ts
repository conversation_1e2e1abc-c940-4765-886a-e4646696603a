import useSWR, { SWRConfiguration } from 'swr';
import { apiClient } from '../lib/api';
import { Video } from '../types';
import { getWorkingThumbnailUrl, getWorkingVideoUrl } from '../utils/mediaUtils';

// Define the fetcher function
const trendingVideosFetcher = async (key: string) => {
  console.log('🔥 trendingVideosFetcher called with key:', key);

  // Parse the key to extract parameters
  const [_, limit] = key.split('/');
  const limitNum = parseInt(limit) || 8;
  console.log('📊 Parsed limit:', limitNum);

  console.log('📊 Executing trending videos query...');
  const response = await apiClient.getVideos({
    limit: limitNum,
    sort: 'views',
    order: 'DESC'
  });

  console.log('📊 Trending videos query result:', { success: response.success, dataLength: response.data?.videos?.length });

  if (!response.success) {
    console.error('❌ Trending videos fetch error:', response.error);
    throw new Error(response.error || 'Failed to fetch trending videos');
  }

  const data = response.data.videos || [];

  // Transform the data to match our Video type and set category to 'trending'
  const trendingVideos: Video[] = data.map(item => ({
    id: item.id,
    title: item.title,
    description: item.description || '',
    thumbnailUrl: getWorkingThumbnailUrl(item.thumbnail_url || ''),
    videoUrl: getWorkingVideoUrl(item.video_url || ''),
    duration: item.duration || 0,
    views: item.views || 0,
    likes: item.likes || 0,
    createdAt: item.created_at,
    updatedAt: item.updated_at || item.created_at, // Fallback to created_at if updated_at is missing
    publishedAt: item.created_at, // Use created_at since published_at doesn't exist in schema
    scheduledFor: undefined, // Field doesn't exist in schema
    status: 'public', // Field doesn't exist in schema, default to public
    isHD: item.is_hd || false,
    isPremium: false, // Field doesn't exist in schema, default to false
    tags: item.tags ? item.tags.split(',').map(tag => tag.trim()) : [], // Convert comma-separated tags to array
    category: 'trending', // Set category to 'trending' for all trending videos
    originalCategory: item.category || 'uncategorized', // Store original category
    creator: {
      id: item.user_id || '',
      email: '',
      avatar: item.user_avatar || 'https://via.placeholder.com/150?text=User',
      isVerified: false,
      isCreator: true,
      subscriberCount: 0
    }
  }));

  console.log('🎯 trendingVideosFetcher returning:', { count: trendingVideos.length });
  return trendingVideos;
};

// Define the hook
export function useTrendingVideos(limit: number = 8, config?: SWRConfiguration) {
  const { data, error, isLoading, isValidating, mutate } = useSWR(
    `trending/${limit}`,
    trendingVideosFetcher,
    {
      revalidateOnFocus: false,
      revalidateIfStale: true,
      revalidateOnReconnect: true,
      dedupingInterval: 60000, // 1 minute
      focusThrottleInterval: 120000, // 2 minutes
      ...config
    }
  );

  return {
    trendingVideos: data || [],
    isLoading,
    isValidating,
    error: error?.message,
    mutate
  };
}
