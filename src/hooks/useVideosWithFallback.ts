import { useState, useEffect } from 'react';
import { Video } from '../types';
import { useVideos } from './useVideos';

/**
 * Enhanced video hook with automatic fallback to direct fetch
 * when SWR fails or times out
 */
export const useVideosWithFallback = (
  category: string = '',
  page: number = 1,
  pageSize: number = 12
) => {
  const [fallbackVideos, setFallbackVideos] = useState<Video[]>([]);
  const [fallbackLoading, setFallbackLoading] = useState(false);
  const [fallbackError, setFallbackError] = useState<string | null>(null);
  const [useFallback, setUseFallback] = useState(false);
  const [fallbackAttempted, setFallbackAttempted] = useState(false);

  // Try SWR first
  const {
    videos: swrVideos,
    isLoading: swrLoading,
    error: swrError,
    mutate: swrMutate
  } = useVideos(category, page, pageSize);

  // Direct fetch fallback function
  const fetchVideosDirect = async () => {
    setFallbackLoading(true);
    setFallbackError(null);

    try {
      console.log('🔄 Fallback: Attempting direct video fetch...');

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Missing environment variables');
      }

      // Calculate pagination
      const offset = (page - 1) * pageSize;

      // Build query parameters
      const params = new URLSearchParams({
        select: 'id,title,description,thumbnail_url,video_url,duration,views,likes,is_hd,user_id,created_at,updated_at,category,tags',
        order: 'created_at.desc',
        limit: pageSize.toString(),
        offset: offset.toString()
      });

      // Add category filter if specified
      if (category && category !== 'all' && category !== '') {
        params.append('category', `eq.${category}`);
      }

      const response = await fetch(`${supabaseUrl}/rest/v1/videos?${params}`, {
        method: 'GET',
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        signal: AbortSignal.timeout(6000) // 6 second timeout
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const videoData = await response.json();

      if (!Array.isArray(videoData)) {
        throw new Error('Invalid response format');
      }

      // Transform to Video type
      const transformedVideos: Video[] = videoData.map(item => ({
        id: item.id,
        title: item.title || 'Untitled Video',
        description: item.description || '',
        thumbnailUrl: item.thumbnail_url || 'https://placehold.co/400x225/gray/white?text=No+Thumbnail',
        videoUrl: item.video_url || '',
        duration: item.duration || 0,
        views: item.views || 0,
        likes: item.likes || 0,
        createdAt: item.created_at,
        updatedAt: item.updated_at || item.created_at,
        publishedAt: item.created_at,
        scheduledFor: undefined,
        status: 'public',
        isHD: item.is_hd || false,
        isPremium: false,
        tags: Array.isArray(item.tags) ? item.tags : [],
        category: item.category || 'uncategorized',
        creator: {
          id: item.user_id || '',
          email: '',
          avatar: 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      }));

      setFallbackVideos(transformedVideos);
      setUseFallback(true);
      console.log('✅ Fallback: Successfully loaded', transformedVideos.length, 'videos');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Fallback: Direct fetch failed:', error);
      setFallbackError(errorMessage);
    } finally {
      setFallbackLoading(false);
      setFallbackAttempted(true);
    }
  };

  // Auto-trigger fallback when SWR fails or times out
  useEffect(() => {
    const shouldUseFallback = (
      !fallbackAttempted &&
      !useFallback &&
      !fallbackLoading &&
      (
        (swrError && swrError.includes('timeout')) ||
        (swrLoading && swrVideos.length === 0) // SWR stuck loading
      )
    );

    if (shouldUseFallback) {
      // Wait a bit to see if SWR recovers, then try fallback
      const timer = setTimeout(() => {
        if (swrLoading && swrVideos.length === 0 && !swrError) {
          // console.log('⏰ SWR taking too long, triggering fallback...');
          fetchVideosDirect();
        }
      }, 5000); // Wait 5 seconds before fallback

      return () => clearTimeout(timer);
    }
  }, [swrLoading, swrError, swrVideos.length, fallbackAttempted, useFallback, fallbackLoading]);

  // Trigger fallback immediately if SWR has an error
  useEffect(() => {
    if (swrError && !fallbackAttempted && !useFallback) {
      // console.log('❌ SWR error detected, triggering immediate fallback:', swrError);
      fetchVideosDirect();
    }
  }, [swrError, fallbackAttempted, useFallback]);

  // Return the appropriate data
  const videos = useFallback ? fallbackVideos : swrVideos;
  const isLoading = useFallback ? fallbackLoading : swrLoading;
  const error = useFallback ? fallbackError : swrError;

  return {
    videos,
    isLoading,
    error,
    mutate: swrMutate,
    // Additional fallback info
    fallbackActive: useFallback,
    fallbackAttempted,
    retryFallback: fetchVideosDirect,
    // Force fallback function
    forceFallback: () => {
      if (!useFallback) {
        fetchVideosDirect();
      }
    }
  };
};
