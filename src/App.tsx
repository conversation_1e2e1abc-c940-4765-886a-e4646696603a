import React, { useState, useEffect, lazy, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/layout/Navbar';
import Sidebar from './components/layout/Sidebar';
import MobileNav from './components/layout/MobileNav';
import AuthModal from './components/auth/AuthModal';
import ProtectedRoute from './components/auth/ProtectedRoute';
import DisclaimerModal from './components/modals/DisclaimerModal';
import BrowserStateDebugger from './components/debug/BrowserStateDebugger';
import VideoLoadingDiagnostic from './components/debug/VideoLoadingDiagnostic';
import { useAuthStore } from './stores/authStore';
import { useDisclaimerStore } from './stores/disclaimerStore';
import PerformanceMonitor from './components/performance/PerformanceMonitor';
import MediaPerformanceMonitor from './components/performance/MediaPerformanceMonitor';
import EnvironmentCheck from './components/debug/EnvironmentCheck';
import { initializeCacheBusting, monitorVersionUpdates } from './utils/cacheBusting';
import { checkForStateConflicts } from './utils/browserStateManager';
import SupabaseDebug from './components/debug/SupabaseDebug';
import VideoDataDebug from './components/debug/VideoDataDebug';
import VideoDebugPanel from './components/debug/VideoDebugPanel';
import DirectVideoFetch from './components/debug/DirectVideoFetch';
import NetworkDiagnostic from './components/debug/NetworkDiagnostic';
import ManualVideoLoader from './components/debug/ManualVideoLoader';
import LoadingStatusIndicator from './components/debug/LoadingStatusIndicator';
import AuthDebugger from './components/debug/AuthDebugger';

// Lazy load page components
const HomePage = lazy(() => import('./pages/HomePage'));
const VideoPage = lazy(() =>
  import('./pages/VideoPage').catch(error => {
    console.error('Failed to load VideoPage:', error);
    // Return a fallback component
    return {
      default: () => (
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto">
            <h1 className="text-2xl font-bold text-white mb-2">Error Loading Video Page</h1>
            <p className="text-red-200 mb-4">There was an error loading the video player. Please refresh the page.</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-700 hover:bg-blue-600 text-white px-4 py-2 rounded"
            >
              Refresh Page
            </button>
          </div>
        </div>
      )
    };
  })
);
const CategoryPage = lazy(() => import('./pages/CategoryPage'));
const AllVideosPage = lazy(() => import('./pages/AllVideosPage'));
const UploadPage = lazy(() => import('./pages/UploadPage'));
const ManageVideosPage = lazy(() => import('./pages/ManageVideosPage'));
const SearchPage = lazy(() => import('./pages/SearchPage'));
const FavoritesPage = lazy(() => import('./pages/FavoritesPage'));
const TestVideoPage = lazy(() => import('./pages/TestVideoPage'));
const TestVideoIndexPage = lazy(() => import('./pages/TestVideoIndexPage'));
const AdminPage = lazy(() => import('./pages/AdminPage'));

function App() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'home' | 'search' | 'upload' | 'profile'>('home');
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'signup'>('login');
  const [showDisclaimer, setShowDisclaimer] = useState(true);
  const [appError, setAppError] = useState<string | null>(null);
  const [showBrowserDebugger, setShowBrowserDebugger] = useState(false);
  const [showVideoLoadingDiagnostic, setShowVideoLoadingDiagnostic] = useState(false);
  const [cacheCleared, setCacheCleared] = useState(false);
  const [manualVideos, setManualVideos] = useState<any[]>([]);

  const { user, loadUser } = useAuthStore();
  const { hasAcceptedDisclaimer, acceptDisclaimer, checkDisclaimerStatus } = useDisclaimerStore();

  // Initialize cache busting and browser state management
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize cache busting
        const wasCleared = await initializeCacheBusting();
        setCacheCleared(wasCleared);

        // Check for browser state conflicts
        const conflicts = checkForStateConflicts();
        if (conflicts.hasConflicts) {
          console.warn('Browser state conflicts detected:', conflicts);
          // Auto-show debugger if there are critical conflicts
          const hasCriticalConflicts = conflicts.conflicts.some(conflict =>
            conflict.includes('Expired') || conflict.includes('Corrupted')
          );
          if (hasCriticalConflicts) {
            setShowBrowserDebugger(true);
          }
        }

        // Set up version monitoring
        const cleanup = monitorVersionUpdates(() => {
          // console.log('New version detected, prompting user to refresh...');
          if (confirm('A new version is available. Refresh to update?')) {
            window.location.reload();
          }
        });

        return cleanup;
      } catch (error) {
        // console.error('Failed to initialize app:', error);
        setAppError('Failed to initialize application. Please refresh the page.');
      }
    };

    initializeApp();
  }, []);

  // Check if user has already accepted the disclaimer
  useEffect(() => {
    // Always check the disclaimer status when the app loads
    checkDisclaimerStatus();
    setShowDisclaimer(!hasAcceptedDisclaimer);
  }, [hasAcceptedDisclaimer, checkDisclaimerStatus]);

  // Force check on initial load to ensure first-time visitors see the disclaimer
  useEffect(() => {
    checkDisclaimerStatus();
  }, [checkDisclaimerStatus]);

  useEffect(() => {
    loadUser().catch((error) => {
      console.error('Failed to load user:', error);
      setAppError(`Failed to initialize authentication: ${error.message}`);
    });
  }, [loadUser]);

  // Add or remove sidebar-open class to body when sidebar state changes
  useEffect(() => {
    if (sidebarOpen) {
      document.body.classList.add('sidebar-open');
    } else {
      document.body.classList.remove('sidebar-open');
    }

    return () => {
      document.body.classList.remove('sidebar-open');
    };
  }, [sidebarOpen]);

  const handleTabChange = (tab: 'home' | 'search' | 'upload' | 'profile') => {
    setActiveTab(tab);

    // Show login modal if user tries to access profile or upload without being logged in
    if ((tab === 'profile' || tab === 'upload') && !user) {
      setAuthMode('login');
      setShowAuthModal(true);
      return;
    }
  };

  const handleLoginClick = () => {
    setAuthMode('login');
    setShowAuthModal(true);
  };

  const handleSignUpClick = () => {
    setAuthMode('signup');
    setShowAuthModal(true);
  };

  // Update active tab based on current route
  useEffect(() => {
    const path = window.location.pathname;
    if (path === '/') {
      setActiveTab('home');
    } else if (path.includes('/search')) {
      setActiveTab('search');
    } else if (path.includes('/upload')) {
      setActiveTab('upload');
    } else if (path.includes('/manage') || path.includes('/profile')) {
      setActiveTab('profile');
    }
  }, []);

  // Get development mode status from environment
  const isDevelopment = import.meta.env.DEV;

  // Show error screen if there's an app-level error
  if (appError) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center p-8">
          <h1 className="text-2xl font-bold text-red-400 mb-4">Application Error</h1>
          <p className="text-gray-300 mb-4">{appError}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <div className={`min-h-screen bg-gray-900 text-white ${sidebarOpen ? 'sidebar-open' : ''}`}>
        {/* Adult Content Disclaimer Modal */}
        <DisclaimerModal
          isOpen={showDisclaimer}
          onAccept={() => {
            acceptDisclaimer();
            setShowDisclaimer(false);
          }}
        />

        <Navbar
          onOpenSidebar={() => setSidebarOpen(true)}
          isAuthenticated={!!user}
          onLoginClick={handleLoginClick}
          onSignUpClick={handleSignUpClick}
        />
        <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

        <main className="pt-16 pb-20 lg:pl-64 transition-all duration-300">
          <Suspense fallback={
            <div className="flex justify-center items-center h-[70vh]">
              <div className="animate-pulse flex flex-col items-center">
                <div className="w-16 h-16 bg-blue-700 rounded-full mb-4"></div>
                <div className="h-4 w-32 bg-gray-700 rounded mb-2"></div>
                <div className="h-3 w-24 bg-gray-700 rounded"></div>
              </div>
            </div>
          }>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/video/:id" element={<VideoPage />} />
              <Route path="/test-video" element={<TestVideoIndexPage />} />
              <Route path="/test-video/:id" element={<TestVideoPage />} />
              <Route path="/category/:slug" element={<CategoryPage />} />
              <Route path="/all-videos" element={<AllVideosPage />} />
              <Route path="/upload" element={
                <ProtectedRoute requireAuth={true} requireApproval={true}>
                  <UploadPage />
                </ProtectedRoute>
              } />
              <Route path="/manage" element={
                <ProtectedRoute requireAuth={true}>
                  <ManageVideosPage />
                </ProtectedRoute>
              } />
              <Route path="/search" element={<SearchPage />} />
              <Route path="/favorites" element={<FavoritesPage />} />
              <Route path="/admin" element={
                <ProtectedRoute requireAuth={true}>
                  <AdminPage />
                </ProtectedRoute>
              } />
            </Routes>
          </Suspense>
        </main>

        <MobileNav
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />

        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          initialMode={authMode}
        />

        {/* Performance monitor - hidden in production */}
        <PerformanceMonitor
          visible={false}
          position="bottom-right"
        />

        {/* Media Performance monitor - hidden in production */}
        <MediaPerformanceMonitor
          visible={false}
        />

        {/* Auth debugger - hidden in production, set visible={true} to debug auth issues */}
        <AuthDebugger
          visible={false}
          position="bottom-left"
        />

        {/* Debug panels - hidden for cleaner UI */}
        <EnvironmentCheck
          visible={false}
        />

        <VideoDebugPanel
          visible={false}
        />

        {/* Debug components - hidden in production */}
        {false && (
          <>
            <SupabaseDebug />
            <VideoDataDebug />
            <DirectVideoFetch />
            <NetworkDiagnostic />
          </>
        )}
        {/* Manual Video Loader - hidden in production */}
        {false && <ManualVideoLoader onVideosLoaded={setManualVideos} />}
        {/* Loading Status Indicator - hidden in production */}
        {false && (
          <LoadingStatusIndicator
            videosLoading={false}
            videosCount={0}
            videosError={null}
            manualVideosCount={manualVideos.length}
          />
        )}

        {/* Browser State Debugger - hidden in production */}
        {false && (
          <BrowserStateDebugger
            isOpen={showBrowserDebugger}
            onClose={() => setShowBrowserDebugger(false)}
          />
        )}

        {/* Video Loading Diagnostic - hidden in production */}
        {false && (
          <VideoLoadingDiagnostic
            isOpen={showVideoLoadingDiagnostic}
            onClose={() => setShowVideoLoadingDiagnostic(false)}
          />
        )}

        {/* Debug triggers - hidden in production */}
        {false && (
          <div className="fixed bottom-4 left-4 space-y-2 z-40">
            <button
              onClick={() => setShowBrowserDebugger(true)}
              className="block w-full bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded text-sm"
              title="Open Browser State Debugger"
            >
              Debug Browser State
            </button>
            <button
              onClick={() => setShowVideoLoadingDiagnostic(true)}
              className="block w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm"
              title="Diagnose Video Loading Issues"
            >
              Debug Video Loading
            </button>
            <button
              onClick={async () => {
                const { resetSWRState } = await import('./utils/swrUtils');
                await resetSWRState();
                window.location.reload();
              }}
              className="block w-full bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm"
              title="Clear SWR Cache and Reload"
            >
              Reset SWR & Reload
            </button>
          </div>
        )}
      </div>
    </Router>
  );
}

export default App;