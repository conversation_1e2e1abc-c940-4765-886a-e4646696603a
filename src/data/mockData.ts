import { Video, Category, User } from '../types';

export const users: User[] = [
  {
    id: '1',
    username: '<PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=150',
    isVerified: true,
    isCreator: true,
    subscriberCount: 45300,
    isSubscribed: false
  },
  {
    id: '2',
    username: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150',
    isVerified: true,
    isCreator: true,
    subscriberCount: 98200,
    isSubscribed: true
  },
  {
    id: '3',
    username: 'SophiaWil<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150',
    isVerified: true,
    isCreator: true,
    subscriberCount: 124500,
    isSubscribed: false
  },
  {
    id: '4',
    username: 'MikePassion',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=150',
    isVerified: true,
    isCreator: true,
    subscriberCount: 67800,
    isSubscribed: false
  }
];

export const categories: Category[] = [
  { id: '1', name: 'Amateur', slug: 'amateur' },
  { id: '2', name: 'Professional', slug: 'professional' },
  { id: '3', name: 'Cosplay', slug: 'cosplay' },
  { id: '4', name: 'Couples', slug: 'couples' },
  { id: '5', name: 'Solo', slug: 'solo' },
  { id: '6', name: 'Verified', slug: 'verified' },
  { id: '7', name: 'HD', slug: 'hd' },
  { id: '8', name: 'Trending', slug: 'trending' }
];

export const videos: Video[] = [
  {
    id: '1',
    title: 'Hot Summer Night Passion',
    description: 'Watch this steamy video featuring an intimate encounter on a hot summer night. High definition quality guaranteed to provide the ultimate viewing experience.',
    thumbnailUrl: 'https://images.pexels.com/photos/3771824/pexels-photo-3771824.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    duration: 842,
    views: 1245789,
    likes: 24680,
    createdAt: '2025-05-12',
    isHD: true,
    isPremium: false,
    tags: ['amateur', 'couple', 'summer'],
    category: 'amateur',
    creator: users[0]
  },
  {
    id: '2',
    title: 'Intimate Confession Room',
    description: 'An exclusive behind-the-scenes look at our most intimate confession room. Watch as our guests reveal their deepest desires and fantasies.',
    thumbnailUrl: 'https://images.pexels.com/photos/704767/pexels-photo-704767.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    duration: 1256,
    views: 876543,
    likes: 18970,
    createdAt: '2025-05-10',
    isHD: true,
    isPremium: false,
    tags: ['interview', 'confession', 'fantasy'],
    category: 'solo',
    creator: users[1]
  },
  {
    id: '3',
    title: 'Cosplay Dreams Come True',
    description: 'Watch as this stunning cosplayer brings your favorite character to life in this exclusive video. High quality costumes and even higher quality action.',
    thumbnailUrl: 'https://images.pexels.com/photos/4056159/pexels-photo-4056159.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    duration: 934,
    views: 1567890,
    likes: 31456,
    createdAt: '2025-05-08',
    isHD: true,
    isPremium: false,
    tags: ['cosplay', 'fantasy', 'roleplay'],
    category: 'cosplay',
    creator: users[2]
  },
  {
    id: '4',
    title: 'Midnight Rendezvous',
    description: 'A secret meeting turns into an unforgettable night of passion. Watch as these two strangers connect in ways they never expected.',
    thumbnailUrl: 'https://images.pexels.com/photos/2335126/pexels-photo-2335126.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    duration: 1125,
    views: 975310,
    likes: 21345,
    createdAt: '2025-05-05',
    isHD: true,
    isPremium: false,
    tags: ['couple', 'passionate', 'night'],
    category: 'couples',
    creator: users[3]
  },
  {
    id: '5',
    title: 'Solo Exploration',
    description: 'Watch this stunning performer take you on a journey of self-discovery and pleasure. An intimate solo performance you won\'t want to miss.',
    thumbnailUrl: 'https://images.pexels.com/photos/7709020/pexels-photo-7709020.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    duration: 687,
    views: 753210,
    likes: 15678,
    createdAt: '2025-05-03',
    isHD: false,
    isPremium: false,
    tags: ['solo', 'intimate', 'exploration'],
    category: 'solo',
    creator: users[0]
  },
  {
    id: '6',
    title: 'Perfect Morning Wake Up',
    description: 'There\'s no better way to start the day than with the perfect morning routine. Watch as this couple shows you how they like to wake up.',
    thumbnailUrl: 'https://images.pexels.com/photos/4204805/pexels-photo-4204805.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    duration: 945,
    views: 892456,
    likes: 19876,
    createdAt: '2025-05-01',
    isHD: true,
    isPremium: false,
    tags: ['couple', 'morning', 'intimate'],
    category: 'couples',
    creator: users[1]
  },
  {
    id: '7',
    title: 'Beach Day Fun',
    description: 'A day at the beach turns into an unforgettable experience. Watch as these beachgoers find a private spot to enjoy the sun and each other.',
    thumbnailUrl: 'https://images.pexels.com/photos/4319752/pexels-photo-4319752.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    duration: 1032,
    views: 1123450,
    likes: 25690,
    createdAt: '2025-04-28',
    isHD: true,
    isPremium: false,
    tags: ['outdoor', 'beach', 'summer'],
    category: 'amateur',
    creator: users[2]
  },
  {
    id: '8',
    title: 'Professional Studio Session',
    description: 'Go behind the scenes of a professional adult film studio. Watch how the magic happens with top performers and expert production.',
    thumbnailUrl: 'https://images.pexels.com/photos/3760529/pexels-photo-3760529.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    duration: 1569,
    views: 1987654,
    likes: 42316,
    createdAt: '2025-04-25',
    isHD: true,
    isPremium: false,
    tags: ['professional', 'studio', 'hd'],
    category: 'professional',
    creator: users[3]
  }
];

export const getFeaturedVideo = (): {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  creator: {
    name: string;
    avatar: string;
  }
} => {
  const video = videos[2]; // Cosplay video as featured
  return {
    id: video.id,
    title: video.title,
    description: video.description,
    thumbnailUrl: video.thumbnailUrl,
    creator: {
      name: video.creator.username,
      avatar: video.creator.avatar
    }
  };
};

export const getVideosByCategory = (categorySlug: string): Video[] => {
  if (categorySlug === 'all') {
    return videos;
  }
  return videos.filter(video => video.category === categorySlug);
};

export const getTrendingVideos = (): Video[] => {
  // In a real app, this would be based on an algorithm
  return [...videos].sort((a, b) => b.views - a.views).slice(0, 4);
};

export const getRecommendedVideos = (): Video[] => {
  // In a real app, this would be personalized
  return [...videos].sort(() => Math.random() - 0.5).slice(0, 4);
};