import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface DisclaimerModalProps {
  isOpen: boolean;
  onAccept: () => void;
}

const DisclaimerModal: React.FC<DisclaimerModalProps> = ({ isOpen, onAccept }) => {
  const navigate = useNavigate();
  const [isVisible, setIsVisible] = useState(isOpen);

  useEffect(() => {
    setIsVisible(isOpen);

    // Prevent scrolling when modal is open
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  const handleAccept = () => {
    setIsVisible(false);
    onAccept();
    // We don't need to set localStorage here as it's handled in the store
  };

  const handleDecline = () => {
    // Redirect to a safe page or external site
    window.location.href = 'https://www.google.com';
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-md w-full border border-gray-700 shadow-2xl">
        <div className="p-6">
          <h2 className="text-2xl font-bold text-white mb-4 text-center">Adult Content Warning</h2>
          <div className="h-px bg-gray-700 mb-4"></div>

          <p className="text-gray-200 mb-6 text-center">
            This site contains explicit adult material. You may only proceed if you are 18 years of age or older
            (or the legal age of majority in your jurisdiction). If you do not meet these requirements,
            you are not permitted to access this site.
          </p>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={handleAccept}
              className="px-6 py-2 bg-blue-700 hover:bg-blue-600 text-white rounded-md font-medium transition-colors"
            >
              Enter
            </button>
            <button
              onClick={handleDecline}
              className="px-6 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md font-medium transition-colors"
            >
              Leave
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DisclaimerModal;
