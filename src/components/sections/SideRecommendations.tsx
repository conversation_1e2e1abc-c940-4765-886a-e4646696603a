import React from 'react';
import { Video } from '../../types';
import { formatDuration, formatCount } from '../../utils/formatters';
import { useNavigate } from 'react-router-dom';
import OptimizedImage from '../ui/OptimizedImage';

interface SideRecommendationsProps {
  videos: Video[];
  currentVideoId: string;
}

const SideRecommendations: React.FC<SideRecommendationsProps> = ({
  videos,
  currentVideoId
}) => {
  const navigate = useNavigate();

  // Filter out the current video
  const filteredVideos = videos.filter(video => video.id !== currentVideoId);

  // Only show up to 8 videos
  const displayVideos = filteredVideos.slice(0, 8);

  if (displayVideos.length === 0) {
    return null;
  }

  return (
    <div className="bg-gray-900 rounded-lg p-4 sticky top-20">
      <h3 className="text-lg font-medium text-white mb-4">Recommended Videos</h3>
      <div className="space-y-3 max-h-[calc(100vh-150px)] overflow-y-auto pr-1 custom-scrollbar">
        {displayVideos.map(video => (
          <div
            key={video.id}
            className="flex cursor-pointer hover:bg-gray-800 rounded-lg transition-colors p-2 group"
            onClick={() => navigate(`/video/${video.id}`)}
          >
            <div className="relative w-32 sm:w-40 flex-shrink-0">
              <OptimizedImage
                src={video.thumbnailUrl}
                alt={video.title}
                fallbackSrc="https://placehold.co/640x360/gray/white?text=No+Thumbnail"
                className="w-full aspect-video object-cover rounded-md"
                width={160}
                height={90}
                sizes="(max-width: 640px) 128px, 160px"
              />
              <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1 rounded">
                {formatDuration(video.duration)}
              </div>
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity flex items-center justify-center">
                <div className="w-8 h-8 rounded-full bg-blue-700/80 text-white flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                  </svg>
                </div>
              </div>
            </div>
            <div className="ml-3 flex-1 overflow-hidden">
              <h4 className="text-white text-sm font-medium line-clamp-2 group-hover:text-blue-400 transition-colors">{video.title}</h4>
              <div className="text-gray-400 text-xs mt-1">
                {formatCount(video.views)} views
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SideRecommendations;
