import React from 'react';
import { PlayCircle } from 'lucide-react';
import Button from '../ui/Button';

interface HeroSectionProps {
  featured: {
    id: string;
    title: string;
    description: string;
    thumbnailUrl: string;
    creator: {
      name: string;
      avatar: string;
    };
  };
  onPlay?: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ featured, onPlay }) => {
  return (
    <div className="relative h-[50vh] md:h-[60vh] overflow-hidden mb-8">
      {/* Background image */}
      <div className="absolute inset-0">
        <img 
          src={featured.thumbnailUrl} 
          alt={featured.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/80 to-transparent" />
      </div>
      
      {/* Content */}
      <div className="relative h-full container mx-auto px-4 flex flex-col justify-end pb-16">
        <div className="max-w-2xl">
          <div className="flex items-center mb-3">
            <img 
              src={featured.creator.avatar} 
              alt={featured.creator.name}
              className="w-8 h-8 rounded-full object-cover mr-2 border border-orange-500"
            />
            <span className="text-white text-sm">{featured.creator.name}</span>
          </div>
          
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white leading-tight mb-3">
            {featured.title}
          </h1>
          
          <p className="text-gray-300 mb-6 text-sm md:text-base line-clamp-2 md:line-clamp-3">
            {featured.description}
          </p>
          
          <div className="flex space-x-4">
            <Button 
              variant="primary" 
              size="lg"
              leftIcon={<PlayCircle size={20} />}
              onClick={onPlay}
            >
              Play Now
            </Button>
            <Button 
              variant="outline" 
              size="lg"
            >
              Add to Watchlist
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;