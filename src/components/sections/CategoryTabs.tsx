import React from 'react';

interface CategoryTabsProps {
  onCategoryChange: (category: string) => void;
  selectedCategory: string;
}

const CategoryTabs: React.FC<CategoryTabsProps> = ({
  onCategoryChange,
  selectedCategory
}) => {
  // Fixed list of categories as requested
  const categories = [
    { id: 'trending', name: 'Trending', slug: 'trending' },
    { id: 'recommended', name: 'Recommended', slug: 'recommended' },
    { id: 'hot', name: 'Hot', slug: 'hot' },
    { id: 'new', name: 'New', slug: 'new' }
  ];

  return (
    <div className="mb-6">
      {/* Mobile-optimized category tabs */}
      <div className="flex space-x-3 sm:space-x-6 md:space-x-8 py-2 overflow-x-auto scrollbar-hide">
        {/* All Videos tab */}
        <button
          className={`px-3 py-2 sm:px-4 rounded-full whitespace-nowrap transition-colors text-sm sm:text-base ${
            selectedCategory === ''
              ? 'bg-blue-700 text-white'
              : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
          }`}
          onClick={() => onCategoryChange('')}
        >
          All Videos
        </button>

        {/* Category tabs */}
        {categories.map(category => (
          <button
            key={category.id}
            className={`px-3 py-2 sm:px-4 rounded-full whitespace-nowrap transition-colors text-sm sm:text-base ${
              selectedCategory === category.slug
                ? 'bg-blue-700 text-white'
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
            }`}
            onClick={() => onCategoryChange(category.slug)}
          >
            {category.name}
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategoryTabs;
