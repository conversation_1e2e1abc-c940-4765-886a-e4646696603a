import React, { useMemo } from 'react';
import { Video } from '../../types';
import VideoCard from '../ui/VideoCard';
import VideoCardWithProgress from '../ui/VideoCardWithProgress';
import VideoSkeleton from '../ui/VideoSkeleton';
import VideoSeriesCard from '../ui/VideoSeriesCard';
// Temporarily disabled: import { groupVideosBySeries, VideoSeries } from '../../utils/videoGrouping';

// Temporary type definition
interface VideoSeries {
  baseTitle: string;
  videos: Video[];
  totalVideos: number;
}

interface VideoGridProps {
  title?: string;
  videos: Video[];
  onVideoClick?: (video: Video) => void;
  isLoading?: boolean;
  viewAll?: () => void;
  compact?: boolean;
  className?: string;
  showProgressBar?: boolean;
}

const VideoGrid: React.FC<VideoGridProps> = ({
  title,
  videos,
  onVideoClick,
  isLoading = false,
  viewAll,
  compact = false,
  className = "",
  showProgressBar = false
}) => {
  // Generate skeleton cards for loading state
  const skeletonCount = compact ? 6 : 8;
  const skeletonCards = Array(skeletonCount).fill(0).map((_, index) => (
    <VideoSkeleton key={`skeleton-${index}`} compact={compact} />
  ));

  // Temporarily disable series grouping to fix the "T is not a function" error
  const groupedContent = useMemo(() => {
    if (isLoading || videos.length === 0) return [];
    // Return videos as individual items instead of grouping them
    return videos;
  }, [videos, isLoading]);

  // Handle click on a video in a series
  const handleSeriesVideoClick = (videoId: string) => {
    if (!onVideoClick) return;

    const video = videos.find(v => v.id === videoId);
    if (video) {
      onVideoClick(video);
    }
  };

  return (
    <section className={`py-5 ${className}`}>
      {title && (
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl md:text-2xl font-bold text-white group">
            {title}
            {!compact && (
              <div className="h-1 w-0 group-hover:w-full bg-blue-700 transition-all duration-300 mt-1"></div>
            )}
          </h2>
          <div className="flex items-center">
            {isLoading && videos.length > 0 && (
              <div className="w-5 h-5 border-t-2 border-b-2 border-blue-700 rounded-full animate-spin mr-3"></div>
            )}
            {viewAll && (
              <button
                onClick={viewAll}
                className="text-gray-400 hover:text-blue-700 text-sm font-medium transition-colors"
              >
                View All
              </button>
            )}
          </div>
        </div>
      )}

      {compact ? (
        // Compact layout (horizontal scrolling)
        <div className="relative group">
          <div className="overflow-x-auto scrollbar-hide pb-4">
            <div className="flex space-x-4" style={{ minWidth: 'max-content' }}>
              {isLoading && videos.length === 0 ? (
                // Show skeleton cards when loading and no videos are available
                skeletonCards
              ) : (
                // Show actual videos (series grouping disabled)
                groupedContent.map((video) => (
                  <div key={video.id} className="w-[280px]">
                    {video.progress && showProgressBar ? (
                      <VideoCardWithProgress
                        video={video}
                        onClick={onVideoClick}
                      />
                    ) : (
                      <VideoCard
                        video={video}
                        onClick={onVideoClick}
                      />
                    )}
                  </div>
                ))
              )}

              {!isLoading && videos.length === 0 && (
                <div className="py-8 text-center text-gray-400 w-full">
                  <p>No videos found</p>
                </div>
              )}
            </div>
          </div>

          {/* Gradient fade on sides for horizontal scroll */}
          <div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-900 to-transparent pointer-events-none"></div>
          <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-900 to-transparent pointer-events-none"></div>
        </div>
      ) : (
        // Standard grid layout
        <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
          {isLoading && videos.length === 0 ? (
            // Show skeleton cards when loading and no videos are available
            skeletonCards
          ) : (
            // Show actual videos (series grouping disabled)
            groupedContent.map((video) => (
              video.progress && showProgressBar ? (
                <VideoCardWithProgress
                  key={video.id}
                  video={video}
                  onClick={onVideoClick}
                />
              ) : (
                <VideoCard
                  key={video.id}
                  video={video}
                  onClick={onVideoClick}
                />
              )
            ))
          )}

          {!isLoading && videos.length === 0 && (
            <div className="col-span-full py-8 text-center text-gray-400">
              <p>No videos found</p>
            </div>
          )}
        </div>
      )}
    </section>
  );
};

export default VideoGrid;