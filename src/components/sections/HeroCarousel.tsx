import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, ChevronRight, Play } from 'lucide-react';
import { Video } from '../../types';
import Button from '../ui/Button';
import HeroSkeleton from '../ui/HeroSkeleton';
import { formatCount, formatDuration } from '../../utils/formatters';

interface HeroCarouselProps {
  videos: Video[];
  isLoading?: boolean;
}

const HeroCarousel: React.FC<HeroCarouselProps> = ({ videos, isLoading = false }) => {
  const navigate = useNavigate();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Auto-advance the carousel every 6 seconds
  useEffect(() => {
    if (videos.length <= 1) return;

    const interval = setInterval(() => {
      handleNext();
    }, 6000);

    return () => clearInterval(interval);
  }, [currentIndex, videos.length]);

  const handleNext = () => {
    if (videos.length <= 1) return;

    setIsTransitioning(true);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % videos.length);

    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  };

  const handlePrev = () => {
    if (videos.length <= 1) return;

    setIsTransitioning(true);
    setCurrentIndex((prevIndex) => (prevIndex - 1 + videos.length) % videos.length);

    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  };

  const handleDotClick = (index: number) => {
    if (index === currentIndex) return;

    setIsTransitioning(true);
    setCurrentIndex(index);

    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  };

  const handleWatchNow = () => {
    if (videos.length > 0) {
      navigate(`/video/${videos[currentIndex].id}`);
    }
  };

  // Render loading skeleton
  if (isLoading) {
    return <HeroSkeleton />;
  }

  // If no videos, show placeholder
  if (videos.length === 0) {
    return (
      <div className="relative w-full h-[500px] bg-gray-800 rounded-xl overflow-hidden flex items-center justify-center">
        <div className="text-center p-6">
          <h2 className="text-2xl text-white font-bold mb-2">No Featured Videos</h2>
          <p className="text-gray-400">Check back later for featured content</p>
        </div>
      </div>
    );
  }

  const currentVideo = videos[currentIndex];

  return (
    <div className="relative w-full h-[500px] bg-gray-900 rounded-xl overflow-hidden group">
      {/* Background image */}
      <div
        className={`absolute inset-0 bg-cover bg-center transition-opacity duration-500 ${
          isTransitioning ? 'opacity-0' : 'opacity-100'
        }`}
        style={{ backgroundImage: `url(${currentVideo.thumbnailUrl})` }}
      ></div>

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black via-black/60 to-transparent"></div>

      {/* Content */}
      <div className="absolute bottom-0 left-0 right-0 p-6 md:p-10 z-10">
        <div className={`transition-all duration-500 ${isTransitioning ? 'opacity-0 translate-y-4' : 'opacity-100 translate-y-0'}`}>
          <div className="flex items-center mb-2">
            <span className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded mr-2">FEATURED</span>
            {currentVideo.isHD && (
              <span className="bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded mr-2">HD</span>
            )}
            <span className="text-gray-300 text-sm">{formatDuration(currentVideo.duration)}</span>
          </div>

          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-2">
            {currentVideo.title}
          </h2>

          <p className="text-gray-300 mb-4 max-w-2xl line-clamp-2 md:line-clamp-3">
            {currentVideo.description}
          </p>

          <div className="flex items-center text-gray-400 text-sm mb-4">
            <span className="font-medium text-white">{currentVideo.creator.username}</span>
            <span className="mx-2">•</span>
            <span>{formatCount(currentVideo.views)} views</span>
          </div>

          <Button
            variant="primary"
            size="lg"
            leftIcon={<Play size={18} />}
            onClick={handleWatchNow}
          >
            Watch Now
          </Button>
        </div>
      </div>

      {/* Navigation arrows */}
      {videos.length > 1 && (
        <>
          <button
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={handlePrev}
            aria-label="Previous slide"
          >
            <ChevronLeft size={24} />
          </button>

          <button
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={handleNext}
            aria-label="Next slide"
          >
            <ChevronRight size={24} />
          </button>

          {/* Dots navigation */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {videos.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-all ${
                  index === currentIndex ? 'bg-orange-500 w-6' : 'bg-white/50 hover:bg-white/80'
                }`}
                onClick={() => handleDotClick(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default HeroCarousel;
