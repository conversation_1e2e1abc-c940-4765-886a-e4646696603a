import React from 'react';
import { Category } from '../../types';

interface CategorySliderProps {
  categories: Category[];
  activeCategory?: string;
  onCategorySelect?: (category: Category) => void;
}

const CategorySlider: React.FC<CategorySliderProps> = ({
  categories,
  activeCategory = 'all',
  onCategorySelect
}) => {
  return (
    <div className="relative mb-6">
      <div className="flex overflow-x-auto pb-3 scrollbar-hide mask-fade-right">
        <div className="flex space-x-2 px-4">
          <button
            className={`
              px-4 py-2 rounded-full whitespace-nowrap transition-colors
              ${activeCategory === 'all'
                ? 'bg-orange-500 text-white'
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}
            `}
            onClick={() => onCategorySelect?.({ id: 'all', name: 'All', slug: 'all' })}
          >
            All Videos
          </button>

          {categories.map((category) => (
            <button
              key={category.id}
              className={`
                px-4 py-2 rounded-full whitespace-nowrap transition-colors
                ${activeCategory === category.slug
                  ? 'bg-blue-700 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}
              `}
              onClick={() => onCategorySelect?.(category)}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Fade effect on edges */}
      <div className="absolute top-0 right-0 h-full w-24 bg-gradient-to-l from-gray-900 to-transparent pointer-events-none" />
    </div>
  );
};

export default CategorySlider;