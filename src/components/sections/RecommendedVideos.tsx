import React from 'react';
import { Video } from '../../types';
import VideoCard from '../ui/VideoCard';
import { useNavigate } from 'react-router-dom';
import { X } from 'lucide-react';

interface RecommendedVideosProps {
  videos: Video[];
  currentVideoId: string;
  onClose: () => void;
  onVideoSelect: (videoId: string) => void;
}

const RecommendedVideos: React.FC<RecommendedVideosProps> = ({
  videos,
  currentVideoId,
  onClose,
  onVideoSelect
}) => {
  const navigate = useNavigate();

  console.log('RecommendedVideos component rendered with videos:', videos);
  console.log('Current video ID:', currentVideoId);

  // Filter out the current video
  const filteredVideos = videos.filter(video => video.id !== currentVideoId);
  console.log('Filtered videos (without current):', filteredVideos);

  // Only show up to 6 videos
  const displayVideos = filteredVideos.slice(0, 6);
  console.log('Display videos (up to 6):', displayVideos);

  if (displayVideos.length === 0) {
    console.log('No videos to display, returning null');
    return null;
  }

  return (
    <div className="absolute inset-0 bg-black bg-opacity-90 z-50 flex flex-col">
      <div className="p-4 flex justify-between items-center">
        <h2 className="text-xl font-bold text-white">Recommended Videos</h2>
        <button
          onClick={onClose}
          className="p-2 rounded-full hover:bg-gray-800 text-gray-400 hover:text-white transition-colors"
          aria-label="Close recommendations"
        >
          <X size={24} />
        </button>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {displayVideos.map(video => (
            <div
              key={video.id}
              className="cursor-pointer"
              onClick={() => onVideoSelect(video.id)}
            >
              <VideoCard video={video} />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RecommendedVideos;
