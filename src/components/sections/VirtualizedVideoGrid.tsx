import React, { useMemo, useRef, useState, useEffect } from 'react';
import { FixedSizeGrid, FixedSizeList, GridChildComponentProps, ListChildComponentProps } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import { Video } from '../../types';
import VideoCard from '../ui/VideoCard';
import VideoCardWithProgress from '../ui/VideoCardWithProgress';
import VideoSkeleton from '../ui/VideoSkeleton';
import VideoSeriesCard from '../ui/VideoSeriesCard';
import { groupVideosBySeries, VideoSeries } from '../../utils/videoGrouping';
import { useInView } from 'react-intersection-observer';

interface VirtualizedVideoGridProps {
  title?: string;
  videos: Video[];
  onVideoClick?: (video: Video) => void;
  isLoading?: boolean;
  viewAll?: () => void;
  className?: string;
  showProgressBar?: boolean;
  itemHeight?: number;
  overscanCount?: number;
  horizontal?: boolean;
  gridColumns?: number;
}

const VirtualizedVideoGrid: React.FC<VirtualizedVideoGridProps> = ({
  title,
  videos,
  onVideoClick,
  isLoading = false,
  viewAll,
  className = "",
  showProgressBar = false,
  itemHeight = 280,
  overscanCount = 5,
  horizontal = false,
  gridColumns = 4
}) => {
  // Use intersection observer to detect when the grid is in view
  const { ref: gridRef, inView } = useInView({
    threshold: 0.1,
    triggerOnce: false,
  });

  // Generate skeleton cards for loading state
  const skeletonCount = 8;
  const skeletonCards = Array(skeletonCount).fill(0).map((_, index) => (
    <VideoSkeleton key={`skeleton-${index}`} />
  ));

  // Group videos by series
  const groupedContent = useMemo(() => {
    if (isLoading || videos.length === 0) return [];
    return groupVideosBySeries(videos);
  }, [videos, isLoading]);

  // Handle click on a video in a series
  const handleSeriesVideoClick = (videoId: string) => {
    if (!onVideoClick) return;

    const video = videos.find(v => v.id === videoId);
    if (video) {
      onVideoClick(video);
    }
  };

  // Calculate the number of columns based on container width for grid layout
  const getColumnCount = (width: number) => {
    if (width < 640) return 1; // xs - always single column for mobile
    if (width < 768) return 1; // sm - keep single column for better mobile experience
    if (width < 1024) return 2; // md
    return gridColumns; // lg and above
  };

  // Render a grid item
  const GridItem = ({ columnIndex, rowIndex, style, data }: GridChildComponentProps) => {
    const { items, columnCount, onItemClick } = data;
    const index = rowIndex * columnCount + columnIndex;

    if (index >= items.length) {
      return null;
    }

    const item = items[index];

    // Apply padding to the style - less padding on mobile
    const itemStyle = {
      ...style,
      padding: window.innerWidth < 640 ? '4px 8px' : '8px',
    };

    // Check if this is a video series
    if ('baseTitle' in item && 'videos' in item) {
      const series = item as VideoSeries;
      return (
        <div style={itemStyle}>
          <VideoSeriesCard
            series={series}
            onClick={handleSeriesVideoClick}
          />
        </div>
      );
    } else {
      // Regular video
      const video = item as Video;
      return (
        <div style={itemStyle}>
          {video.progress && showProgressBar ? (
            <VideoCardWithProgress
              video={video}
              onClick={onItemClick}
            />
          ) : (
            <VideoCard
              video={video}
              onClick={onItemClick}
            />
          )}
        </div>
      );
    }
  };

  // Render a list item (for horizontal scrolling)
  const ListItem = ({ index, style, data }: ListChildComponentProps) => {
    const { items, onItemClick } = data;

    if (index >= items.length) {
      return null;
    }

    const item = items[index];

    // Apply padding to the style - adjust for mobile
    const itemStyle = {
      ...style,
      padding: window.innerWidth < 640 ? '0 4px' : '0 8px',
    };

    // Check if this is a video series
    if ('baseTitle' in item && 'videos' in item) {
      const series = item as VideoSeries;
      return (
        <div style={itemStyle}>
          <VideoSeriesCard
            series={series}
            onClick={handleSeriesVideoClick}
            compact={true}
          />
        </div>
      );
    } else {
      // Regular video
      const video = item as Video;
      return (
        <div style={itemStyle}>
          {video.progress && showProgressBar ? (
            <VideoCardWithProgress
              video={video}
              onClick={onItemClick}
            />
          ) : (
            <VideoCard
              video={video}
              onClick={onItemClick}
            />
          )}
        </div>
      );
    }
  };

  return (
    <section className={`py-3 md:py-5 ${className}`} ref={gridRef}>
      {title && (
        <div className="flex justify-between items-center mb-2 md:mb-4">
          <h2 className="text-lg md:text-2xl font-bold text-white">
            {title}
          </h2>
          <div className="flex items-center">
            {isLoading && videos.length > 0 && (
              <div className="w-4 h-4 md:w-5 md:h-5 border-t-2 border-b-2 border-blue-700 rounded-full animate-spin mr-2 md:mr-3"></div>
            )}
            {viewAll && (
              <button
                onClick={viewAll}
                className="text-gray-400 hover:text-blue-700 text-xs md:text-sm font-medium transition-colors"
              >
                View All
              </button>
            )}
          </div>
        </div>
      )}

      {isLoading && videos.length === 0 ? (
        // Show skeleton cards when loading and no videos are available
        <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
          {skeletonCards}
        </div>
      ) : !isLoading && videos.length === 0 ? (
        // Show empty state
        <div className="py-8 text-center text-gray-400 w-full">
          <p>No videos found</p>
        </div>
      ) : (
        // Show virtualized grid or list
        <div className="w-full" style={{
          height: horizontal
            ? itemHeight + 20
            : window.innerWidth < 640
              ? 'calc(100vh - 200px)'
              : 'calc(100vh - 300px)',
          minHeight: window.innerWidth < 640 ? '300px' : '400px',
          maxHeight: '1200px'
        }}>
          <AutoSizer>
            {({ height, width }) => {
              // Always render for now (intersection observer optimization disabled)
              // if (!inView) {
              //   return <div style={{ width, height }} />;
              // }

              if (horizontal) {
                // Horizontal list
                return (
                  <FixedSizeList
                    height={height}
                    width={width}
                    itemCount={groupedContent.length}
                    itemSize={280} // Width of each item
                    layout="horizontal"
                    overscanCount={overscanCount}
                    itemData={{
                      items: groupedContent,
                      onItemClick: onVideoClick
                    }}
                  >
                    {ListItem}
                  </FixedSizeList>
                );
              } else {
                // Grid layout
                const columnCount = getColumnCount(width);
                const rowCount = Math.ceil(groupedContent.length / columnCount);

                return (
                  <FixedSizeGrid
                    columnCount={columnCount}
                    columnWidth={width / columnCount}
                    height={height}
                    rowCount={rowCount}
                    rowHeight={itemHeight}
                    width={width}
                    overscanRowCount={overscanCount}
                    itemData={{
                      items: groupedContent,
                      columnCount,
                      onItemClick: onVideoClick
                    }}
                  >
                    {GridItem}
                  </FixedSizeGrid>
                );
              }
            }}
          </AutoSizer>
        </div>
      )}
    </section>
  );
};

export default VirtualizedVideoGrid;
