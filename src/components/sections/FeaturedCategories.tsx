import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCategoryStore } from '../../stores/categoryStore';

const FeaturedCategories: React.FC = () => {
  const navigate = useNavigate();
  const { featuredCategories, isLoading, fetchFeaturedCategories } = useCategoryStore();
  
  useEffect(() => {
    fetchFeaturedCategories();
  }, [fetchFeaturedCategories]);
  
  const handleCategoryClick = (slug: string) => {
    navigate(`/category/${slug}`);
  };
  
  if (isLoading) {
    return (
      <div className="mb-8">
        <h2 className="text-xl font-bold text-white mb-4">Popular Categories</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {Array(6).fill(0).map((_, index) => (
            <div 
              key={`skeleton-${index}`}
              className="h-24 bg-gray-800 rounded-lg animate-pulse"
            />
          ))}
        </div>
      </div>
    );
  }
  
  if (featuredCategories.length === 0) {
    return null;
  }
  
  return (
    <div className="mb-8">
      <h2 className="text-xl font-bold text-white mb-4">Popular Categories</h2>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {featuredCategories.map(category => (
          <div
            key={category.id}
            className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg p-4 cursor-pointer hover:from-orange-500 hover:to-orange-700 transition-colors"
            onClick={() => handleCategoryClick(category.slug)}
          >
            <h3 className="text-white font-medium text-center">{category.name}</h3>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FeaturedCategories;
