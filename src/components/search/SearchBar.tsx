import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, X, Clock, Trash2 } from 'lucide-react';
import { useSearchStore } from '../../stores/searchStore';

interface SearchBarProps {
  className?: string;
  onClose?: () => void;
}

const SearchBar: React.FC<SearchBarProps> = ({
  className = '',
  onClose
}) => {
  const navigate = useNavigate();
  const {
    searchQuery,
    recentSearches,
    setSearchQuery,
    search,
    clearSearch,
    addRecentSearch,
    clearRecentSearches
  } = useSearchStore();

  const [showRecentSearches, setShowRecentSearches] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchContainerRef = useRef<HTMLDivElement>(null);

  // Handle clicks outside the search container
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node)
      ) {
        setShowRecentSearches(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);

    // Show recent searches when the input is focused and empty
    setShowRecentSearches(e.target.value === '');
  };

  const handleInputFocus = () => {
    // Show recent searches when the input is focused and empty
    setShowRecentSearches(searchQuery === '');
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    if (!searchQuery.trim()) return;

    // Add to recent searches
    addRecentSearch(searchQuery);

    // Hide recent searches
    setShowRecentSearches(false);

    // Perform search
    search(searchQuery);

    // Navigate to search results page
    navigate(`/search?q=${encodeURIComponent(searchQuery)}`);

    // Close search bar if on mobile
    if (onClose) onClose();
  };

  const handleClearSearch = () => {
    clearSearch();
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
    setShowRecentSearches(true);
  };

  const handleRecentSearchClick = (query: string) => {
    setSearchQuery(query);
    setShowRecentSearches(false);
    search(query);
    navigate(`/search?q=${encodeURIComponent(query)}`);

    // Close search bar if on mobile
    if (onClose) onClose();
  };

  return (
    <div
      ref={searchContainerRef}
      className={`relative ${className}`}
    >
      <form onSubmit={handleSearch} className="relative">
        <div className="relative flex items-center">
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search videos"
            value={searchQuery}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            className="w-full bg-gray-800 text-white rounded-full pl-10 pr-10 py-2.5 focus:outline-none focus:ring-1 focus:ring-blue-700 border border-gray-700 hover:border-blue-700 transition-colors shadow-sm text-sm md:text-base mobile-search-input"
          />
          <Search className="absolute left-3 text-blue-500 search-icon" size={16} />

          {searchQuery && (
            <button
              type="button"
              onClick={handleClearSearch}
              className="absolute right-8 text-gray-400 hover:text-white p-1 search-button-mobile"
            >
              <X size={16} />
            </button>
          )}

          <button
            type="submit"
            className="absolute right-2 text-blue-700 hover:text-blue-600 p-1 search-button-mobile"
            disabled={!searchQuery.trim()}
          >
            <Search size={16} />
          </button>
        </div>

        {/* Recent searches dropdown */}
        {showRecentSearches && recentSearches.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-gray-800 rounded-md shadow-lg z-50 overflow-hidden max-h-60 overflow-y-auto mobile-search-dropdown">
            <div className="flex items-center justify-between p-2 border-b border-gray-700">
              <h3 className="text-white font-medium text-sm">Recent Searches</h3>
              <button
                onClick={clearRecentSearches}
                className="text-gray-400 hover:text-white flex items-center text-xs"
              >
                <Trash2 size={12} className="mr-1" />
                Clear
              </button>
            </div>
            <ul>
              {recentSearches.map((query, index) => (
                <li key={index}>
                  <button
                    onClick={() => handleRecentSearchClick(query)}
                    className="w-full text-left px-3 py-2 hover:bg-gray-700 flex items-center text-gray-300 hover:text-white text-sm truncate"
                  >
                    <Clock size={14} className="mr-2 text-gray-500 flex-shrink-0" />
                    <span className="truncate">{query}</span>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        )}
      </form>
    </div>
  );
};

export default SearchBar;
