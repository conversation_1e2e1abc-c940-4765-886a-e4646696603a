import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, X, Image, Film, AlertCircle, CheckCircle, Camera } from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { useUploadStore } from '../../stores/uploadStoreNamecheap';
import { useAuthStore } from '../../stores/authStore';
import CategorySelector from '../categories/CategorySelector';
import { generateThumbnail, generateMultipleThumbnails } from '../../utils/thumbnailGenerator';
import { validateVideoFile, validateImageFile, checkBrowserSupport, isAndroidDevice } from '../../utils/fileValidation';
import { preserveAuthState, withAuthMonitoring, authSafeDelay } from '../../utils/authPreservation';

const UploadForm: React.FC = () => {
  const navigate = useNavigate();
  const videoInputRef = useRef<HTMLInputElement>(null);
  const thumbnailInputRef = useRef<HTMLInputElement>(null);

  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('new'); // Default to 'new' category
  const [tags, setTags] = useState('');
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [videoPreview, setVideoPreview] = useState<string | null>(null);
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);
  const [generatedThumbnails, setGeneratedThumbnails] = useState<string[]>([]);
  const [selectedThumbnailIndex, setSelectedThumbnailIndex] = useState<number>(-1);
  const [isGeneratingThumbnails, setIsGeneratingThumbnails] = useState<boolean>(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [browserSupport, setBrowserSupport] = useState<{ isSupported: boolean; missingFeatures: string[] } | null>(null);

  const { uploadVideo, isUploading, uploadProgress, error } = useUploadStore();

  // Create a stable setError function
  const setError = useCallback((error: string | null) => {
    useUploadStore.setState({ error });
  }, []);

  // Check browser support on component mount
  useEffect(() => {
    const support = checkBrowserSupport();
    setBrowserSupport(support);

    if (!support.isSupported) {
      console.warn('Browser missing features:', support.missingFeatures);
      setError(`Your browser is missing required features: ${support.missingFeatures.join(', ')}`);
    }

    // Preserve authentication state for mobile devices
    if (isAndroidDevice()) {
      preserveAuthState();

      // Set up periodic auth state preservation
      const preserveInterval = setInterval(() => {
        preserveAuthState();
      }, 30000); // Every 30 seconds

      return () => clearInterval(preserveInterval);
    }
  }, [setError]);

  // Clear success message when component unmounts or when starting a new upload
  useEffect(() => {
    if (isUploading) {
      setSuccessMessage(null);
    }
  }, [isUploading]);

  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      console.log('📱 Mobile file selected:', {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
        isMobile: isAndroidDevice() || /iPad|iPhone|iPod/.test(navigator.userAgent)
      });

      // Use the new validation utility
      const validation = validateVideoFile(file);

      if (!validation.isValid) {
        setErrors({
          ...errors,
          video: validation.error || 'Invalid video file'
        });
        return;
      }

      setVideoFile(file);

      // Create a preview URL for the video with error handling
      try {
        const url = URL.createObjectURL(file);
        setVideoPreview(url);
      } catch (error) {
        console.error('Error creating video preview:', error);
        // Continue without preview on Android if URL.createObjectURL fails
      }

      // Reset generated thumbnails when a new video is selected
      setGeneratedThumbnails([]);
      setSelectedThumbnailIndex(-1);

      // Clear any previous error
      if (errors.video) {
        const newErrors = { ...errors };
        delete newErrors.video;
        setErrors(newErrors);
      }
    }
  };

  const handleThumbnailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check authentication state before processing
      const { user: currentUser } = useAuthStore.getState();
      if (!currentUser) {
        console.warn('User not authenticated during thumbnail selection');
        setErrors({
          ...errors,
          thumbnail: 'Please log in again to select a thumbnail'
        });
        return;
      }

      // Use the new validation utility
      const validation = validateImageFile(file);

      if (!validation.isValid) {
        setErrors({
          ...errors,
          thumbnail: validation.error || 'Invalid image file'
        });
        return;
      }

      setThumbnailFile(file);

      // Create a preview URL for the thumbnail with enhanced error handling for Android
      try {
        // For Android devices, use a more conservative approach
        if (isAndroidDevice()) {
          // Check if we have enough memory for the operation
          const memory = (navigator as any).deviceMemory;
          if (memory && memory < 2) {
            console.warn('Low memory device detected, skipping thumbnail preview');
            setThumbnailPreview(null);
          } else {
            const url = URL.createObjectURL(file);
            setThumbnailPreview(url);

            // Clean up the URL after a short delay to free memory on mobile
            setTimeout(() => {
              try {
                URL.revokeObjectURL(url);
              } catch (e) {
                // Ignore cleanup errors
              }
            }, 5000);
          }
        } else {
          // Standard approach for non-Android devices
          const url = URL.createObjectURL(file);
          setThumbnailPreview(url);
        }
      } catch (error) {
        console.error('Error creating thumbnail preview:', error);
        // Continue without preview - this is not critical for upload
        setThumbnailPreview(null);
      }

      // Clear any previous error
      if (errors.thumbnail) {
        const newErrors = { ...errors };
        delete newErrors.thumbnail;
        setErrors(newErrors);
      }

      // Verify authentication state is still valid after file processing
      setTimeout(() => {
        const { user: verifyUser } = useAuthStore.getState();
        if (!verifyUser) {
          console.error('Authentication lost during thumbnail processing');
          setErrors({
            ...errors,
            thumbnail: 'Authentication lost. Please refresh the page and try again.'
          });
        }
      }, 100);
    }
  };

  const handleVideoClick = (e?: React.MouseEvent) => {
    // Prevent any default behavior that might cause navigation
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Add a small delay to ensure the click event is processed
    setTimeout(() => {
      videoInputRef.current?.click();
    }, 100);
  };

  const handleCameraClick = (e?: React.MouseEvent) => {
    // Prevent any default behavior that might cause navigation
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Create a temporary input for camera capture
    const cameraInput = document.createElement('input');
    cameraInput.type = 'file';
    cameraInput.accept = 'video/*';
    cameraInput.capture = 'environment';
    cameraInput.style.position = 'absolute';
    cameraInput.style.left = '-9999px';
    cameraInput.style.opacity = '0';

    cameraInput.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        // Simulate the same validation and handling as regular file input
        const event = {
          target: { files: [file] }
        } as React.ChangeEvent<HTMLInputElement>;
        handleVideoChange(event);
      }
      // Clean up the temporary input
      document.body.removeChild(cameraInput);
    };

    // Add to DOM temporarily and click
    document.body.appendChild(cameraInput);
    setTimeout(() => {
      cameraInput.click();
    }, 100);
  };

  const handleThumbnailClick = () => {
    thumbnailInputRef.current?.click();
  };

  const removeVideo = () => {
    if (videoPreview) {
      URL.revokeObjectURL(videoPreview);
    }
    setVideoFile(null);
    setVideoPreview(null);
    if (videoInputRef.current) {
      videoInputRef.current.value = '';
    }
  };

  const removeThumbnail = () => {
    setThumbnailFile(null);
    setThumbnailPreview(null);
    setSelectedThumbnailIndex(-1);
    if (thumbnailInputRef.current) {
      thumbnailInputRef.current.value = '';
    }
  };

  const handleGenerateThumbnails = async () => {
    if (!videoFile) {
      setErrors({
        ...errors,
        thumbnail: 'Please upload a video first'
      });
      return;
    }

    try {
      setIsGeneratingThumbnails(true);

      // Check if user is still authenticated before starting
      const { user } = useAuthStore.getState();
      if (!user) {
        setErrors({
          ...errors,
          thumbnail: 'Please log in again to generate thumbnails'
        });
        return;
      }

      // For Android devices, use a more conservative approach
      if (isAndroidDevice()) {
        // Check device memory before proceeding
        const memory = (navigator as any).deviceMemory;
        if (memory && memory < 2) {
          setErrors({
            ...errors,
            thumbnail: 'Device memory too low for thumbnail generation. Please upload a custom thumbnail instead.'
          });
          return;
        }

        // Add a small delay to prevent overwhelming the device
        await authSafeDelay(500);
      }

      // Generate 3 thumbnails at different points in the video with auth monitoring
      const thumbnailBlobs = await withAuthMonitoring(
        () => generateMultipleThumbnails(videoFile, 3),
        () => {
          setErrors({
            ...errors,
            thumbnail: 'Authentication lost during thumbnail generation. Please refresh the page and try again.'
          });
        }
      );

      // Check authentication again after generation
      const { user: currentUser } = useAuthStore.getState();
      if (!currentUser) {
        console.error('Authentication lost during thumbnail generation');
        setErrors({
          ...errors,
          thumbnail: 'Authentication lost during thumbnail generation. Please refresh the page and try again.'
        });
        return;
      }

      // Convert blobs to URLs for preview with Android-specific handling
      let thumbnailUrls: string[] = [];
      try {
        thumbnailUrls = thumbnailBlobs.map(blob => URL.createObjectURL(blob));
        setGeneratedThumbnails(thumbnailUrls);

        // For Android, clean up URLs after a delay to free memory
        if (isAndroidDevice()) {
          setTimeout(() => {
            thumbnailUrls.forEach(url => {
              try {
                URL.revokeObjectURL(url);
              } catch (e) {
                // Ignore cleanup errors
              }
            });
          }, 30000); // Clean up after 30 seconds
        }
      } catch (urlError) {
        console.error('Error creating thumbnail URLs:', urlError);
        setErrors({
          ...errors,
          thumbnail: 'Failed to create thumbnail previews. Please try uploading a custom thumbnail.'
        });
        return;
      }

      // Clear any previous thumbnail
      setThumbnailFile(null);
      setThumbnailPreview(null);

      // Clear any previous error
      if (errors.thumbnail) {
        const newErrors = { ...errors };
        delete newErrors.thumbnail;
        setErrors(newErrors);
      }

      // For Android, add a final verification step after a short delay
      if (isAndroidDevice()) {
        setTimeout(() => {
          const { user: verifyUser } = useAuthStore.getState();
          if (!verifyUser) {
            console.error('Authentication lost after thumbnail generation');
            setErrors({
              ...errors,
              thumbnail: 'Authentication lost. Please refresh the page and try again.'
            });
          }
        }, 200);
      }
    } catch (error) {
      console.error('Error generating thumbnails:', error);

      // Check if the error is related to authentication
      const { user: errorCheckUser } = useAuthStore.getState();
      if (!errorCheckUser) {
        setErrors({
          ...errors,
          thumbnail: 'Authentication lost during thumbnail generation. Please refresh the page and try again.'
        });
        return;
      }

      let errorMessage = 'Failed to generate thumbnails';

      if (error instanceof Error) {
        if (error.message.includes('Authentication')) {
          errorMessage = 'Authentication lost. Please refresh the page and try again.';
        } else if (error.message.includes('memory')) {
          errorMessage = 'Insufficient device memory. Try uploading without custom thumbnails.';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'Thumbnail generation timed out. Your device may be too slow for this operation.';
        } else {
          errorMessage = error.message;
        }
      }

      setErrors({
        ...errors,
        thumbnail: errorMessage
      });
    } finally {
      setIsGeneratingThumbnails(false);
    }
  };

  const selectGeneratedThumbnail = async (index: number) => {
    if (index >= 0 && index < generatedThumbnails.length) {
      // Check authentication state before processing
      const { user: currentUser } = useAuthStore.getState();
      if (!currentUser) {
        console.warn('User not authenticated during thumbnail selection');
        setErrors({
          ...errors,
          thumbnail: 'Please log in again to select a thumbnail'
        });
        return;
      }

      setSelectedThumbnailIndex(index);

      try {
        // Convert the URL back to a blob
        const response = await fetch(generatedThumbnails[index]);
        const blob = await response.blob();

        // Create a file from the blob
        const file = new File([blob], `thumbnail-${Date.now()}.jpg`, { type: 'image/jpeg' });

        // Set as the thumbnail file
        setThumbnailFile(file);
        setThumbnailPreview(generatedThumbnails[index]);

        // Verify authentication state is still valid after processing
        setTimeout(() => {
          const { user: verifyUser } = useAuthStore.getState();
          if (!verifyUser) {
            console.error('Authentication lost during thumbnail selection');
            setErrors({
              ...errors,
              thumbnail: 'Authentication lost. Please refresh the page and try again.'
            });
          }
        }, 100);
      } catch (error) {
        console.error('Error selecting thumbnail:', error);

        // Check if the error is related to authentication
        const { user: errorCheckUser } = useAuthStore.getState();
        if (!errorCheckUser) {
          setErrors({
            ...errors,
            thumbnail: 'Authentication lost during thumbnail selection. Please refresh the page and try again.'
          });
        } else {
          setErrors({
            ...errors,
            thumbnail: 'Failed to select thumbnail'
          });
        }
      }
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!videoFile) {
      newErrors.video = 'Video file is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!videoFile) {
      return;
    }

    const tagArray = tags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    try {
      // Show a message to the user that we're preparing the upload
      setError(null);

      const videoId = await uploadVideo({
        title,
        description,
        category,
        tags: tagArray,
        videoFile: videoFile,
        thumbnailFile
      });

      if (videoId) {
        // Success! Show success message and then navigate after a short delay
        setSuccessMessage('Video uploaded successfully! Redirecting to video page...');
        setTimeout(() => {
          navigate(`/video/${videoId}`);
        }, 2000);
      } else if (!error) {
        // If there's no specific error but the upload failed
        setError('Upload failed. Please try again.');
      }
    } catch (err) {
      console.error('Upload failed:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
    }
  };

  return (
    <div className="space-y-6">
      {/* Mobile-specific notice */}
      {(isAndroidDevice() || /iPad|iPhone|iPod/.test(navigator.userAgent)) && (
        <div className="p-4 bg-blue-900/50 border border-blue-700 rounded-lg">
          <div className="flex items-start space-x-3">
            <AlertCircle size={20} className="text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-medium text-blue-200 mb-1">Mobile Upload Tips</h3>
              <div className="text-sm text-blue-300 space-y-1">
                <p>• Use MP4 format for best compatibility</p>
                <p>• Keep files under 60MB for faster uploads</p>
                <p>• Ensure stable internet connection</p>
                <p>• Large files will be uploaded in chunks for reliability</p>
                <p>• Stay on this page during upload to prevent logout</p>
                <p>• If you experience logout issues during thumbnail selection, try refreshing the page and uploading without auto-generated thumbnails</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Network status warning */}
      {typeof navigator !== 'undefined' && (navigator as any).connection && (
        (() => {
          const connection = (navigator as any).connection;
          const isSlowConnection = connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g';

          if (isSlowConnection) {
            return (
              <div className="p-4 bg-yellow-900/50 border border-yellow-700 rounded-lg">
                <div className="flex items-start space-x-3">
                  <AlertCircle size={20} className="text-yellow-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-yellow-200 mb-1">Slow Connection Detected</h3>
                    <p className="text-sm text-yellow-300">
                      Your connection appears to be slow. Consider uploading smaller files or waiting for a better connection.
                    </p>
                  </div>
                </div>
              </div>
            );
          }
          return null;
        })()
      )}

      {/* Browser support warning */}
      {browserSupport && !browserSupport.isSupported && (
        <div className="p-4 bg-red-900/50 border border-red-700 rounded-lg">
          <div className="flex items-start space-x-3">
            <AlertCircle size={20} className="text-red-400 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-medium text-red-200 mb-1">Browser Compatibility Issue</h3>
              <p className="text-sm text-red-300">
                Your browser is missing: {browserSupport.missingFeatures.join(', ')}. Please update your browser or try a different one.
              </p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-6">
          <Input
            label="Video Title"
            placeholder="Enter a descriptive title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            error={errors.title}
            fullWidth
          />

          <div>
            <label className="block text-sm font-medium text-gray-200 mb-1">
              Description
            </label>
            <textarea
              className={`w-full rounded-md bg-gray-800 border border-gray-700 text-white focus:border-orange-500 focus:ring-2 focus:ring-orange-500 focus:ring-opacity-20 p-4 transition-colors placeholder:text-gray-400 min-h-[120px] ${
                errors.description ? 'border-red-500' : ''
              }`}
              placeholder="Describe your video"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
            {errors.description && (
              <p className="text-sm text-red-500 mt-1">{errors.description}</p>
            )}
          </div>

          <CategorySelector
            selectedCategory={category}
            onSelect={setCategory}
          />



          <Input
            label="Tags"
            placeholder="Enter tags separated by commas"
            value={tags}
            onChange={(e) => setTags(e.target.value)}
            fullWidth
          />
        </div>

        <div className="space-y-6">
          {/* Video Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-200 mb-2">
              Video File
            </label>

            <input
              type="file"
              ref={videoInputRef}
              className="hidden"
              accept="video/*,video/mp4,video/webm,video/quicktime,video/x-msvideo,video/3gpp,video/x-ms-wmv"
              onChange={handleVideoChange}
            />

            {!videoFile ? (
              <div className="space-y-3">
                {/* Main upload area */}
                <div
                  className="border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-orange-500 transition-colors h-[200px] border-gray-700"
                  onClick={handleVideoClick}
                >
                  <Film size={48} className="text-gray-500 mb-3" />
                  <p className="text-gray-400 text-center">
                    Click to choose from gallery
                  </p>
                  <p className="text-gray-500 text-sm mt-1">
                    MP4, WebM or MOV (max. 50MB)
                  </p>
                </div>

                {/* Mobile camera option */}
                {(isAndroidDevice() || /iPad|iPhone|iPod/.test(navigator.userAgent)) && (
                  <div className="flex justify-center">
                    <Button
                      type="button"
                      variant="secondary"
                      size="sm"
                      leftIcon={<Camera size={16} />}
                      onClick={handleCameraClick}
                      className="w-full sm:w-auto"
                    >
                      Record with Camera
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="relative border rounded-lg overflow-hidden h-[200px]">
                {videoPreview && (
                  <video
                    src={videoPreview}
                    className="w-full h-full object-cover"
                    controls
                  />
                )}
                <button
                  type="button"
                  className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
                  onClick={removeVideo}
                >
                  <X size={16} />
                </button>
              </div>
            )}

            {errors.video && (
              <p className="text-sm text-red-500 mt-1">{errors.video}</p>
            )}
          </div>

          {/* Thumbnail Upload */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-200">
                Thumbnail Image (Optional)
              </label>

              {videoFile && (
                <Button
                  variant="secondary"
                  size="sm"
                  leftIcon={<Camera size={16} />}
                  onClick={handleGenerateThumbnails}
                  isLoading={isGeneratingThumbnails}
                  disabled={isGeneratingThumbnails}
                >
                  Auto-Generate
                </Button>
              )}
            </div>

            <input
              type="file"
              ref={thumbnailInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleThumbnailChange}
            />

            {/* Generated Thumbnails */}
            {generatedThumbnails.length > 0 && (
              <div className="mb-4">
                <p className="text-sm text-gray-300 mb-2">Select a generated thumbnail:</p>
                <div className="grid grid-cols-3 gap-2">
                  {generatedThumbnails.map((url, index) => (
                    <div
                      key={index}
                      className={`relative border-2 rounded overflow-hidden cursor-pointer transition-all ${
                        selectedThumbnailIndex === index ? 'border-orange-500 scale-105' : 'border-gray-700 hover:border-gray-500'
                      }`}
                      onClick={() => selectGeneratedThumbnail(index)}
                    >
                      <img
                        src={url}
                        alt={`Generated thumbnail ${index + 1}`}
                        className="w-full aspect-video object-cover"
                      />
                      {selectedThumbnailIndex === index && (
                        <div className="absolute top-1 right-1 bg-orange-500 text-white rounded-full p-0.5">
                          <CheckCircle size={14} />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Custom Thumbnail Upload */}
            {!thumbnailFile || selectedThumbnailIndex === -1 ? (
              <div
                className={`border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-orange-500 transition-colors h-[200px] ${
                  errors.thumbnail ? 'border-red-500' : 'border-gray-700'
                }`}
                onClick={handleThumbnailClick}
              >
                <Image size={48} className="text-gray-500 mb-3" />
                <p className="text-gray-400 text-center">
                  Click to upload custom thumbnail
                </p>
                <p className="text-gray-500 text-sm mt-1">
                  JPG, PNG or GIF (recommended: 1280×720, max 5MB)
                </p>
              </div>
            ) : (
              <div className="relative border rounded-lg overflow-hidden h-[200px]">
                {thumbnailPreview && (
                  <img
                    src={thumbnailPreview}
                    alt="Thumbnail preview"
                    className="w-full h-full object-cover"
                  />
                )}
                <button
                  type="button"
                  className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
                  onClick={removeThumbnail}
                >
                  <X size={16} />
                </button>
              </div>
            )}
            {errors.thumbnail && (
              <p className="text-sm text-red-500 mt-1">{errors.thumbnail}</p>
            )}
          </div>
        </div>
      </div>



      {error && (
        <div className="bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start">
          <AlertCircle size={20} className="text-red-500 mr-2 flex-shrink-0 mt-0.5" />
          <div className="text-red-200">
            <p>{error}</p>
            {error.includes("Storage buckets don't exist") && (
              <div className="mt-2 text-sm">
                <p>This is likely because:</p>
                <ul className="list-disc pl-5 mt-1 space-y-1">
                  <li>The storage buckets haven't been created in Supabase</li>
                  <li>Your user account doesn't have permission to access the buckets</li>
                </ul>
                <p className="mt-2">
                  <strong>Solution:</strong> An administrator needs to create the 'videos' and 'thumbnails' buckets in the Supabase dashboard.
                </p>
              </div>
            )}

            {error.includes("Row Level Security (RLS) policies") && (
              <div className="mt-2 text-sm">
                <p>This is likely because:</p>
                <ul className="list-disc pl-5 mt-1 space-y-1">
                  <li>The Row Level Security (RLS) policies for the storage buckets are not properly configured</li>
                  <li>Your user account doesn't have permission to upload files to these buckets</li>
                </ul>
                <p className="mt-2">
                  <strong>Solution:</strong> An administrator needs to check and update the RLS policies for the storage buckets with the following SQL:
                </p>
                <pre className="bg-gray-800 p-2 rounded mt-2 overflow-x-auto text-xs">
{`-- First, check existing policies
SELECT policyname, tablename, cmd, qual, with_check
FROM pg_policies
WHERE tablename = 'objects' AND schemaname = 'storage';

-- Then, update the policies if needed
ALTER POLICY "Users can upload videos to their own folder"
ON storage.objects
WITH CHECK (
  bucket_id = 'videos' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

ALTER POLICY "Users can upload thumbnails to their own folder"
ON storage.objects
WITH CHECK (
  bucket_id = 'thumbnails' AND
  auth.uid()::text = (storage.foldername(name))[1]
);`}
                </pre>
              </div>
            )}

            {(error.includes("violates foreign key constraint") || error.includes("user profile does not exist")) && (
              <div className="mt-2 text-sm">
                <p>This is likely because your user profile hasn't been properly created in the database.</p>
                <p className="mt-2">
                  <strong>Solution:</strong> Please try the following steps:
                </p>
                <ol className="list-decimal pl-5 mt-1 space-y-1">
                  <li>Log out and log back in to trigger automatic profile creation</li>
                  <li>Refresh the page and try uploading again</li>
                  <li>If the issue persists, contact support</li>
                </ol>
              </div>
            )}
          </div>
        </div>
      )}

      {successMessage && !error && (
        <div className="bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start">
          <CheckCircle size={20} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
          <p className="text-green-200">{successMessage}</p>
        </div>
      )}

      {isUploading && (
        <div className="bg-gray-700 rounded-full overflow-hidden">
          <div
            className="bg-orange-500 h-2 transition-all duration-300"
            style={{ width: `${uploadProgress}%` }}
          ></div>
          <p className="text-center text-sm text-gray-400 mt-2">
            Uploading: {uploadProgress}%
          </p>
        </div>
      )}

      <div className="flex justify-end space-x-3 pt-4">
        <Button
          variant="ghost"
          type="button"
          onClick={() => navigate('/')}
        >
          Cancel
        </Button>
        <Button
          variant="primary"
          type="submit"
          leftIcon={<Upload size={18} />}
          isLoading={isUploading}
          disabled={isUploading}
        >
          Upload Video
        </Button>
      </div>
    </form>
    </div>
  );
};

export default UploadForm;
