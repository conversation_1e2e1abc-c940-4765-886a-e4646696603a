import React, { useState, useEffect } from 'react';
import { Alert<PERSON>riangle, RefreshCw, CheckCircle } from 'lucide-react';
import { Video } from '../../types';

interface EmergencyVideoLoaderProps {
  onVideosLoaded: (videos: Video[]) => void;
  isVisible: boolean;
}

const EmergencyVideoLoader: React.FC<EmergencyVideoLoaderProps> = ({ onVideosLoaded, isVisible }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const loadVideosDirectly = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      console.log('🚨 Emergency Video Loader: Attempting direct fetch...');

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Missing environment variables');
      }

      // Use direct fetch with minimal configuration
      const response = await fetch(`${supabaseUrl}/rest/v1/videos?select=id,title,description,thumbnail_url,video_url,duration,views,likes,is_hd,user_id,created_at,updated_at,category,tags&order=created_at.desc&limit=12`, {
        method: 'GET',
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        signal: AbortSignal.timeout(8000) // 8 second timeout
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const videoData = await response.json();

      if (!Array.isArray(videoData)) {
        throw new Error('Invalid response format');
      }

      // Transform to Video type with minimal processing
      const transformedVideos: Video[] = videoData.map(item => ({
        id: item.id,
        title: item.title || 'Untitled Video',
        description: item.description || '',
        thumbnailUrl: item.thumbnail_url || 'https://placehold.co/400x225/gray/white?text=No+Thumbnail',
        videoUrl: item.video_url || '',
        duration: item.duration || 0,
        views: item.views || 0,
        likes: item.likes || 0,
        createdAt: item.created_at,
        updatedAt: item.updated_at || item.created_at,
        publishedAt: item.created_at,
        scheduledFor: undefined,
        status: 'public',
        isHD: item.is_hd || false,
        isPremium: false,
        tags: Array.isArray(item.tags) ? item.tags : [],
        category: item.category || 'uncategorized',
        creator: {
          id: item.user_id || '',
          email: '',
          avatar: 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      }));

      // console.log('✅ Emergency Video Loader: Successfully loaded', transformedVideos.length, 'videos');
      onVideosLoaded(transformedVideos);
      setSuccess(true);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      // console.error('❌ Emergency Video Loader failed:', err);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-load when visible
  useEffect(() => {
    if (isVisible && !success) {
      loadVideosDirectly();
    }
  }, [isVisible, success]);

  if (!isVisible) return null;

  return (
    <div className="bg-red-600 text-white p-4 rounded-lg mb-6 border-2 border-red-400">
      <div className="flex items-center gap-3 mb-3">
        <AlertTriangle className="w-6 h-6" />
        <h3 className="font-bold text-lg">🚨 Emergency Video Loader</h3>
      </div>

      <p className="mb-3">
        The normal video loading system is experiencing issues. Attempting emergency direct database access...
      </p>

      <div className="flex items-center gap-3 mb-3">
        {isLoading && (
          <div className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span className="text-sm">Loading videos directly from database...</span>
          </div>
        )}

        {success && (
          <div className="flex items-center gap-2 text-green-200">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm">Emergency loading successful! Videos should now appear below.</span>
          </div>
        )}

        {error && (
          <div className="text-red-200">
            <strong>Emergency loading failed:</strong> {error}
          </div>
        )}
      </div>

      <div className="flex gap-2">
        <button
          onClick={loadVideosDirectly}
          disabled={isLoading}
          className="bg-white text-red-600 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100 disabled:opacity-50"
        >
          {isLoading ? 'Loading...' : 'Retry Emergency Load'}
        </button>

        <button
          onClick={() => window.location.reload()}
          className="bg-red-800 text-white px-3 py-1 rounded text-sm font-medium hover:bg-red-900"
        >
          Reload Page
        </button>
      </div>

      {error && (
        <div className="mt-3 p-3 bg-red-800 rounded text-sm">
          <strong>Troubleshooting:</strong>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Check your internet connection</li>
            <li>Try refreshing the page</li>
            <li>Clear browser cache and cookies</li>
            <li>Try opening the site in an incognito/private window</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default EmergencyVideoLoader;
