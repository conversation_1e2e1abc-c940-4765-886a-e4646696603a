import React, { useState } from 'react';
import { X, Trash2, <PERSON>, <PERSON>Off, Calendar, AlertCircle, Filter, Save } from 'lucide-react';
import Button from '../ui/Button';
import { VideoStatus } from '../../types';
import { useVideoStore } from '../../stores/videoStore';
import { categories } from '../../data/mockData';

interface BatchOperationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  operation: 'delete' | 'status' | 'category';
  selectedCount: number;
}

const BatchOperationsModal: React.FC<BatchOperationsModalProps> = ({ 
  isOpen, 
  onClose, 
  operation,
  selectedCount
}) => {
  // Status operation state
  const [status, setStatus] = useState<VideoStatus>('public');
  const [scheduledDate, setScheduledDate] = useState<string>(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  });
  const [scheduledTime, setScheduledTime] = useState<string>('12:00');
  
  // Category operation state
  const [category, setCategory] = useState<string>('');
  
  // Common state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const { 
    batchDeleteVideos, 
    batchUpdateVideosStatus, 
    batchUpdateVideosCategory 
  } = useVideoStore();
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedCount === 0) {
      setError('No videos selected');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    setSuccess(false);
    
    try {
      let success = false;
      
      switch (operation) {
        case 'delete':
          success = await batchDeleteVideos();
          break;
          
        case 'status':
          success = await batchUpdateVideosStatus(status);
          break;
          
        case 'category':
          if (!category) {
            setError('Please select a category');
            setIsSubmitting(false);
            return;
          }
          success = await batchUpdateVideosCategory(category);
          break;
      }
      
      if (success) {
        setSuccess(true);
        // Close the modal after a short delay
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setError('Operation failed. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      console.error('Error performing batch operation:', err);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const getTitle = () => {
    switch (operation) {
      case 'delete':
        return 'Delete Selected Videos';
      case 'status':
        return 'Change Video Status';
      case 'category':
        return 'Change Video Category';
      default:
        return 'Batch Operation';
    }
  };
  
  const getIcon = () => {
    switch (operation) {
      case 'delete':
        return <Trash2 size={20} className="text-red-500 mr-2" />;
      case 'status':
        return <Eye size={20} className="text-blue-500 mr-2" />;
      case 'category':
        return <Filter size={20} className="text-purple-500 mr-2" />;
      default:
        return null;
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70">
      <div className="bg-gray-800 rounded-lg w-full max-w-md">
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-bold text-white flex items-center">
            {getIcon()}
            {getTitle()}
          </h2>
          <button
            className="p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors"
            onClick={onClose}
            disabled={isSubmitting}
          >
            <X size={20} />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            {/* Delete Operation */}
            {operation === 'delete' && (
              <div className="bg-red-500/10 border border-red-500/30 rounded-md p-4">
                <div className="flex items-start">
                  <AlertCircle size={24} className="text-red-500 mr-3 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="text-lg font-medium text-red-300 mb-1">Confirm Deletion</h3>
                    <p className="text-red-200">
                      Are you sure you want to delete {selectedCount} selected video{selectedCount !== 1 ? 's' : ''}? 
                      This action cannot be undone.
                    </p>
                  </div>
                </div>
              </div>
            )}
            
            {/* Status Operation */}
            {operation === 'status' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    New Status for {selectedCount} video{selectedCount !== 1 ? 's' : ''}
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    <button
                      type="button"
                      className={`flex flex-col items-center justify-center p-3 rounded-md border ${
                        status === 'public' 
                          ? 'border-green-500 bg-green-500/10 text-green-400' 
                          : 'border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600'
                      } transition-colors`}
                      onClick={() => setStatus('public')}
                    >
                      <Eye size={24} className="mb-2" />
                      <span className="text-sm font-medium">Public</span>
                    </button>
                    <button
                      type="button"
                      className={`flex flex-col items-center justify-center p-3 rounded-md border ${
                        status === 'private' 
                          ? 'border-gray-500 bg-gray-500/10 text-gray-300' 
                          : 'border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600'
                      } transition-colors`}
                      onClick={() => setStatus('private')}
                    >
                      <EyeOff size={24} className="mb-2" />
                      <span className="text-sm font-medium">Private</span>
                    </button>
                    <button
                      type="button"
                      className={`flex flex-col items-center justify-center p-3 rounded-md border ${
                        status === 'scheduled' 
                          ? 'border-blue-500 bg-blue-500/10 text-blue-400' 
                          : 'border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600'
                      } transition-colors`}
                      onClick={() => setStatus('scheduled')}
                    >
                      <Calendar size={24} className="mb-2" />
                      <span className="text-sm font-medium">Scheduled</span>
                    </button>
                  </div>
                </div>
                
                {status === 'scheduled' && (
                  <div className="bg-gray-700 p-4 rounded-md">
                    <label className="block text-sm font-medium text-gray-200 mb-2">
                      Publication Date and Time
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <input
                          type="date"
                          className="w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors"
                          value={scheduledDate}
                          onChange={(e) => setScheduledDate(e.target.value)}
                          min={new Date().toISOString().split('T')[0]}
                        />
                      </div>
                      <div>
                        <input
                          type="time"
                          className="w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors"
                          value={scheduledTime}
                          onChange={(e) => setScheduledTime(e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
            
            {/* Category Operation */}
            {operation === 'category' && (
              <div>
                <label className="block text-sm font-medium text-gray-200 mb-2">
                  New Category for {selectedCount} video{selectedCount !== 1 ? 's' : ''}
                </label>
                <select
                  className="w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-purple-500 focus:ring-2 focus:ring-purple-500 focus:ring-opacity-20 p-3 transition-colors"
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                >
                  <option value="">Select a category</option>
                  {categories.map((cat) => (
                    <option key={cat.id} value={cat.slug}>
                      {cat.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
            
            {error && (
              <div className="bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start">
                <AlertCircle size={20} className="text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-red-200">{error}</p>
              </div>
            )}
            
            {success && (
              <div className="bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start">
                <AlertCircle size={20} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-green-200">Operation completed successfully!</p>
              </div>
            )}
          </div>
          
          <div className="flex justify-end space-x-3 mt-6">
            <Button
              variant="ghost"
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant={operation === 'delete' ? 'danger' : 'primary'}
              type="submit"
              leftIcon={operation === 'delete' ? <Trash2 size={18} /> : <Save size={18} />}
              isLoading={isSubmitting}
              disabled={isSubmitting || selectedCount === 0}
            >
              {operation === 'delete' ? 'Delete' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BatchOperationsModal;
