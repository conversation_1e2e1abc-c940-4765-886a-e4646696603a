import React, { useState, useEffect } from 'react';
import { X, Eye, EyeOff, Calendar, AlertCircle, Save } from 'lucide-react';
import Button from '../ui/Button';
import { Video, VideoStatus } from '../../types';
import { useVideoStore } from '../../stores/videoStore';

interface VideoStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  video: Video;
}

const VideoStatusModal: React.FC<VideoStatusModalProps> = ({ isOpen, onClose, video }) => {
  const [status, setStatus] = useState<VideoStatus>(video.status || 'public');
  const [scheduledDate, setScheduledDate] = useState<string>('');
  const [scheduledTime, setScheduledTime] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const { updateVideoStatus } = useVideoStore();
  
  // Reset form when video changes
  useEffect(() => {
    if (video) {
      setStatus(video.status || 'public');
      
      if (video.scheduledFor) {
        const date = new Date(video.scheduledFor);
        setScheduledDate(date.toISOString().split('T')[0]);
        setScheduledTime(date.toTimeString().slice(0, 5));
      } else {
        // Default to tomorrow at noon
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(12, 0, 0, 0);
        setScheduledDate(tomorrow.toISOString().split('T')[0]);
        setScheduledTime('12:00');
      }
      
      setError(null);
      setSuccess(false);
    }
  }, [video]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsSubmitting(true);
    setError(null);
    setSuccess(false);
    
    try {
      let scheduledFor: string | undefined;
      
      if (status === 'scheduled') {
        if (!scheduledDate) {
          setError('Please select a publication date');
          setIsSubmitting(false);
          return;
        }
        
        const dateTime = new Date(`${scheduledDate}T${scheduledTime || '12:00'}`);
        
        if (dateTime <= new Date()) {
          setError('Scheduled date must be in the future');
          setIsSubmitting(false);
          return;
        }
        
        scheduledFor = dateTime.toISOString();
      }
      
      const success = await updateVideoStatus(video.id, status, scheduledFor);
      
      if (success) {
        setSuccess(true);
        // Close the modal after a short delay
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setError('Failed to update video status. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      console.error('Error updating video status:', err);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70">
      <div className="bg-gray-800 rounded-lg w-full max-w-md">
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-bold text-white">Change Video Status</h2>
          <button
            className="p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors"
            onClick={onClose}
            disabled={isSubmitting}
          >
            <X size={20} />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-200 mb-2">
                Video Status
              </label>
              <div className="grid grid-cols-3 gap-3">
                <button
                  type="button"
                  className={`flex flex-col items-center justify-center p-3 rounded-md border ${
                    status === 'public' 
                      ? 'border-green-500 bg-green-500/10 text-green-400' 
                      : 'border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600'
                  } transition-colors`}
                  onClick={() => setStatus('public')}
                >
                  <Eye size={24} className="mb-2" />
                  <span className="text-sm font-medium">Public</span>
                </button>
                <button
                  type="button"
                  className={`flex flex-col items-center justify-center p-3 rounded-md border ${
                    status === 'private' 
                      ? 'border-gray-500 bg-gray-500/10 text-gray-300' 
                      : 'border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600'
                  } transition-colors`}
                  onClick={() => setStatus('private')}
                >
                  <EyeOff size={24} className="mb-2" />
                  <span className="text-sm font-medium">Private</span>
                </button>
                <button
                  type="button"
                  className={`flex flex-col items-center justify-center p-3 rounded-md border ${
                    status === 'scheduled' 
                      ? 'border-blue-500 bg-blue-500/10 text-blue-400' 
                      : 'border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600'
                  } transition-colors`}
                  onClick={() => setStatus('scheduled')}
                >
                  <Calendar size={24} className="mb-2" />
                  <span className="text-sm font-medium">Scheduled</span>
                </button>
              </div>
            </div>
            
            {status === 'scheduled' && (
              <div className="bg-gray-700 p-4 rounded-md">
                <label className="block text-sm font-medium text-gray-200 mb-2">
                  Publication Date and Time
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <input
                      type="date"
                      className="w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors"
                      value={scheduledDate}
                      onChange={(e) => setScheduledDate(e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                    />
                  </div>
                  <div>
                    <input
                      type="time"
                      className="w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors"
                      value={scheduledTime}
                      onChange={(e) => setScheduledTime(e.target.value)}
                    />
                  </div>
                </div>
                <p className="text-sm text-gray-400 mt-2">
                  Your video will be automatically published at the specified date and time.
                </p>
              </div>
            )}
            
            {status === 'public' && (
              <div className="bg-green-500/10 border border-green-500/30 rounded-md p-3 text-sm text-green-300">
                <p>Your video will be visible to everyone.</p>
              </div>
            )}
            
            {status === 'private' && (
              <div className="bg-gray-700 border border-gray-600 rounded-md p-3 text-sm text-gray-300">
                <p>Your video will only be visible to you.</p>
              </div>
            )}
            
            {error && (
              <div className="bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start">
                <AlertCircle size={20} className="text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-red-200">{error}</p>
              </div>
            )}
            
            {success && (
              <div className="bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start">
                <AlertCircle size={20} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-green-200">Video status updated successfully!</p>
              </div>
            )}
          </div>
          
          <div className="flex justify-end space-x-3 mt-6">
            <Button
              variant="ghost"
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              type="submit"
              leftIcon={<Save size={18} />}
              isLoading={isSubmitting}
              disabled={isSubmitting}
            >
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VideoStatusModal;
