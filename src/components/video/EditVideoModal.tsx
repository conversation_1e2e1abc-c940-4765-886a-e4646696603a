import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle } from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { Video } from '../../types';
import { useVideoStore } from '../../stores/videoStore';
import { categories } from '../../data/mockData';

interface EditVideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  video: Video;
}

const EditVideoModal: React.FC<EditVideoModalProps> = ({ isOpen, onClose, video }) => {
  const [title, setTitle] = useState(video.title);
  const [description, setDescription] = useState(video.description);
  const [category, setCategory] = useState(video.category);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const { updateVideo } = useVideoStore();
  
  // Reset form when video changes
  useEffect(() => {
    if (video) {
      setTitle(video.title);
      setDescription(video.description);
      setCategory(video.category);
      setErrors({});
      setError(null);
      setSuccess(false);
    }
  }, [video]);
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    setSuccess(false);
    
    try {
      const success = await updateVideo(video.id, {
        title,
        description,
        category
      });
      
      if (success) {
        setSuccess(true);
        // Close the modal after a short delay
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setError('Failed to update video. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      console.error('Error updating video:', err);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70">
      <div className="bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-bold text-white">Edit Video</h2>
          <button
            className="p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors"
            onClick={onClose}
          >
            <X size={20} />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            <Input
              label="Title"
              placeholder="Enter video title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              error={errors.title}
              fullWidth
            />
            
            <div>
              <label className="block text-sm font-medium text-gray-200 mb-1">
                Description
              </label>
              <textarea
                className={`w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-orange-500 focus:ring-2 focus:ring-orange-500 focus:ring-opacity-20 p-3 transition-colors placeholder:text-gray-400 min-h-[120px] ${
                  errors.description ? 'border-red-500' : ''
                }`}
                placeholder="Describe your video"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
              />
              {errors.description && (
                <p className="text-sm text-red-500 mt-1">{errors.description}</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-200 mb-1">
                Category
              </label>
              <select
                className={`w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-orange-500 focus:ring-2 focus:ring-orange-500 focus:ring-opacity-20 p-3 transition-colors ${
                  errors.category ? 'border-red-500' : ''
                }`}
                value={category}
                onChange={(e) => setCategory(e.target.value)}
              >
                <option value="">Select a category</option>
                {categories.map((cat) => (
                  <option key={cat.id} value={cat.slug}>
                    {cat.name}
                  </option>
                ))}
              </select>
              {errors.category && (
                <p className="text-sm text-red-500 mt-1">{errors.category}</p>
              )}
            </div>
            
            {/* Video Preview */}
            <div className="border border-gray-700 rounded-lg overflow-hidden">
              <div className="aspect-video bg-gray-900 relative">
                <img
                  src={video.thumbnailUrl || 'https://placehold.co/640x360/gray/white?text=No+Thumbnail'}
                  alt={video.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = 'https://placehold.co/640x360/gray/white?text=No+Thumbnail';
                  }}
                />
              </div>
            </div>
            
            {error && (
              <div className="bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start">
                <AlertCircle size={20} className="text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-red-200">{error}</p>
              </div>
            )}
            
            {success && (
              <div className="bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start">
                <AlertCircle size={20} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-green-200">Video updated successfully!</p>
              </div>
            )}
          </div>
          
          <div className="flex justify-end space-x-3 mt-6">
            <Button
              variant="ghost"
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              type="submit"
              leftIcon={<Save size={18} />}
              isLoading={isSubmitting}
              disabled={isSubmitting}
            >
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditVideoModal;
