import React, { useState, useEffect } from 'react';
import { X, BarChart2, TrendingUp, Eye, ThumbsUp, Globe, Smartphone, AlertCircle } from 'lucide-react';
import Button from '../ui/Button';
import { Video, VideoAnalytics } from '../../types';
import { useVideoStore } from '../../stores/videoStore';
import { formatCount } from '../../utils/formatters';

interface VideoAnalyticsModalProps {
  isOpen: boolean;
  onClose: () => void;
  video: Video;
}

const VideoAnalyticsModal: React.FC<VideoAnalyticsModalProps> = ({ isOpen, onClose, video }) => {
  const [analytics, setAnalytics] = useState<VideoAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { fetchVideoAnalytics } = useVideoStore();

  // Fetch analytics when the modal opens
  useEffect(() => {
    if (isOpen && video) {
      const loadAnalytics = async () => {
        setIsLoading(true);
        setError(null);

        try {
          const data = await fetchVideoAnalytics(video.id);
          setAnalytics(data);
        } catch (err) {
          setError('Failed to load analytics data');
          console.error('Error loading analytics:', err);
        } finally {
          setIsLoading(false);
        }
      };

      loadAnalytics();
    }
  }, [isOpen, video, fetchVideoAnalytics]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70">
      <div className="bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-bold text-white flex items-center">
            <BarChart2 size={20} className="text-blue-700 mr-2" />
            Video Analytics: {video.title}
          </h2>
          <button
            className="p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors"
            onClick={onClose}
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
            </div>
          ) : error ? (
            <div className="bg-red-500/20 border border-red-500 rounded-md p-4 flex items-start">
              <AlertCircle size={24} className="text-red-500 mr-3 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="text-lg font-medium text-red-300 mb-1">Error Loading Analytics</h3>
                <p className="text-red-200">{error}</p>
              </div>
            </div>
          ) : analytics ? (
            <div className="space-y-6">
              {/* Overview Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Eye size={18} className="text-blue-400 mr-2" />
                    <h3 className="text-gray-300 font-medium">Total Views</h3>
                  </div>
                  <div className="text-2xl font-bold text-white">{formatCount(video.views)}</div>
                  <div className="text-sm text-green-400 mt-1 flex items-center">
                    <TrendingUp size={14} className="mr-1" />
                    +{formatCount(analytics.viewsLast7Days)} in last 7 days
                  </div>
                </div>

                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <ThumbsUp size={18} className="text-green-400 mr-2" />
                    <h3 className="text-gray-300 font-medium">Total Likes</h3>
                  </div>
                  <div className="text-2xl font-bold text-white">{formatCount(video.likes)}</div>
                  <div className="text-sm text-green-400 mt-1 flex items-center">
                    <TrendingUp size={14} className="mr-1" />
                    +{formatCount(analytics.likesLast7Days)} in last 7 days
                  </div>
                </div>

                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Globe size={18} className="text-purple-400 mr-2" />
                    <h3 className="text-gray-300 font-medium">Top Country</h3>
                  </div>
                  <div className="text-2xl font-bold text-white">
                    {analytics.viewsByCountry && analytics.viewsByCountry.length > 0
                      ? analytics.viewsByCountry[0].country
                      : 'N/A'}
                  </div>
                  <div className="text-sm text-gray-400 mt-1">
                    {analytics.viewsByCountry && analytics.viewsByCountry.length > 0
                      ? `${formatCount(analytics.viewsByCountry[0].count)} views`
                      : 'No data available'}
                  </div>
                </div>

                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Smartphone size={18} className="text-orange-400 mr-2" />
                    <h3 className="text-gray-300 font-medium">Top Device</h3>
                  </div>
                  <div className="text-2xl font-bold text-white">
                    {analytics.viewsByDevice && analytics.viewsByDevice.length > 0
                      ? analytics.viewsByDevice[0].device
                      : 'N/A'}
                  </div>
                  <div className="text-sm text-gray-400 mt-1">
                    {analytics.viewsByDevice && analytics.viewsByDevice.length > 0
                      ? `${formatCount(analytics.viewsByDevice[0].count)} views`
                      : 'No data available'}
                  </div>
                </div>
              </div>

              {/* Views Chart */}
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-medium text-white mb-4">Views Over Time</h3>
                <div className="h-64 relative">
                  {/* Simple bar chart visualization */}
                  <div className="absolute inset-0 flex items-end">
                    {analytics.viewsByDay.map((day, index) => {
                      const maxCount = Math.max(...analytics.viewsByDay.map(d => d.count));
                      const height = maxCount > 0 ? (day.count / maxCount) * 100 : 0;

                      return (
                        <div key={index} className="flex-1 flex flex-col items-center">
                          <div
                            className="w-full bg-orange-500 rounded-t"
                            style={{ height: `${height}%` }}
                          ></div>
                          {index % 5 === 0 && (
                            <div className="text-xs text-gray-400 mt-1 rotate-45 origin-left">
                              {new Date(day.date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Geography and Devices */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-700 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-white mb-4">Top Countries</h3>
                  <div className="space-y-2">
                    {analytics.viewsByCountry?.map((country, index) => (
                      <div key={index} className="flex items-center">
                        <div className="w-32 text-gray-300">{country.country}</div>
                        <div className="flex-1 h-4 bg-gray-600 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-blue-500 rounded-full"
                            style={{
                              width: `${analytics.viewsByCountry ?
                                (country.count / analytics.viewsByCountry[0].count) * 100 : 0}%`
                            }}
                          ></div>
                        </div>
                        <div className="w-16 text-right text-gray-400 text-sm">
                          {formatCount(country.count)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-gray-700 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-white mb-4">Devices</h3>
                  <div className="space-y-2">
                    {analytics.viewsByDevice?.map((device, index) => (
                      <div key={index} className="flex items-center">
                        <div className="w-32 text-gray-300">{device.device}</div>
                        <div className="flex-1 h-4 bg-gray-600 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-green-500 rounded-full"
                            style={{
                              width: `${analytics.viewsByDevice ?
                                (device.count / analytics.viewsByDevice[0].count) * 100 : 0}%`
                            }}
                          ></div>
                        </div>
                        <div className="w-16 text-right text-gray-400 text-sm">
                          {formatCount(device.count)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-400">
              No analytics data available
            </div>
          )}

          <div className="flex justify-end mt-6">
            <Button
              variant="primary"
              onClick={onClose}
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoAnalyticsModal;
