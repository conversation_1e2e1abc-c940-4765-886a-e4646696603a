import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { vi, describe, test, expect, beforeEach } from 'vitest';
import NativeVideoPlayer from '../NativeVideoPlayer';
import { Video } from '../../../types';
import * as deviceDetection from '../../../utils/deviceDetection';

// Mock the device detection utilities
vi.mock('../../../utils/deviceDetection', () => ({
  getIsMobile: vi.fn().mockReturnValue(true),
  requestFullscreen: vi.fn(),
  exitFullscreen: vi.fn(),
  isInFullscreen: vi.fn().mockReturnValue(false),
}));

// Mock the stores
vi.mock('../../../stores/userPreferencesStore', () => ({
  useUserPreferencesStore: () => ({
    updateWatchProgress: vi.fn(),
    getWatchProgress: vi.fn().mockReturnValue(null),
  }),
}));

vi.mock('../../../stores/videoStore', () => ({
  useVideoStore: () => ({
    incrementVideoViews: vi.fn(),
  }),
}));

// Mock HTMLMediaElement methods
Object.defineProperty(window.HTMLMediaElement.prototype, 'play', {
  configurable: true,
  value: vi.fn(),
});

Object.defineProperty(window.HTMLMediaElement.prototype, 'pause', {
  configurable: true,
  value: vi.fn(),
});

Object.defineProperty(window.HTMLMediaElement.prototype, 'load', {
  configurable: true,
  value: vi.fn(),
});

// Sample video data
const mockVideo: Video = {
  id: 'test-video-id',
  title: 'Test Video',
  description: 'A test video',
  videoUrl: 'https://example.com/test-video.mp4',
  thumbnailUrl: 'https://example.com/test-thumbnail.jpg',
  duration: 120,
  views: 100,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  userId: 'test-user-id',
  isPublic: true,
  categories: ['test'],
};

describe('NativeVideoPlayer', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Mock user agent for Android
    Object.defineProperty(navigator, 'userAgent', {
      configurable: true,
      value: 'Mozilla/5.0 (Linux; Android 10; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36',
      writable: true,
    });
  });

  test('renders video element with correct attributes', () => {
    render(<NativeVideoPlayer video={mockVideo} />);

    const videoElement = document.querySelector('video');
    expect(videoElement).toBeInTheDocument();
    expect(videoElement).toHaveAttribute('src', mockVideo.videoUrl);
    expect(videoElement).toHaveAttribute('poster', mockVideo.thumbnailUrl);
    expect(videoElement).toHaveAttribute('controls');
    expect(videoElement).toHaveAttribute('playsInline');
  });

  test('renders back button when onBack prop is provided', () => {
    const mockOnBack = vi.fn();
    render(<NativeVideoPlayer video={mockVideo} onBack={mockOnBack} />);

    const backButton = screen.getByRole('button', { name: /back/i });
    expect(backButton).toBeInTheDocument();

    fireEvent.click(backButton);
    expect(mockOnBack).toHaveBeenCalledTimes(1);
  });

  test('does not render back button when onBack prop is not provided', () => {
    render(<NativeVideoPlayer video={mockVideo} />);

    const backButton = screen.queryByRole('button', { name: /back/i });
    expect(backButton).not.toBeInTheDocument();
  });

  test('renders fullscreen button for Android devices', () => {
    // Mock isAndroid state
    vi.spyOn(React, 'useState')
      .mockImplementationOnce(() => [false, vi.fn()]) // isPlaying
      .mockImplementationOnce(() => [0, vi.fn()]) // currentTime
      .mockImplementationOnce(() => [mockVideo.duration, vi.fn()]) // duration
      .mockImplementationOnce(() => [true, vi.fn()]) // isMobileDevice
      .mockImplementationOnce(() => [true, vi.fn()]) // isAndroid
      .mockImplementationOnce(() => [false, vi.fn()]); // isIOS

    render(<NativeVideoPlayer video={mockVideo} />);

    const fullscreenButton = screen.getByRole('button', { name: /toggle fullscreen/i });
    expect(fullscreenButton).toBeInTheDocument();

    fireEvent.click(fullscreenButton);
    expect(deviceDetection.requestFullscreen).toHaveBeenCalledTimes(1);
  });

  test('calls exitFullscreen when already in fullscreen mode', () => {
    // Mock isAndroid state
    vi.spyOn(React, 'useState')
      .mockImplementationOnce(() => [false, vi.fn()]) // isPlaying
      .mockImplementationOnce(() => [0, vi.fn()]) // currentTime
      .mockImplementationOnce(() => [mockVideo.duration, vi.fn()]) // duration
      .mockImplementationOnce(() => [true, vi.fn()]) // isMobileDevice
      .mockImplementationOnce(() => [true, vi.fn()]) // isAndroid
      .mockImplementationOnce(() => [false, vi.fn()]); // isIOS

    // Mock fullscreen state
    (deviceDetection.isInFullscreen as unknown as ReturnType<typeof vi.fn>).mockReturnValue(true);

    render(<NativeVideoPlayer video={mockVideo} />);

    const fullscreenButton = screen.getByRole('button', { name: /toggle fullscreen/i });
    fireEvent.click(fullscreenButton);

    expect(deviceDetection.exitFullscreen).toHaveBeenCalledTimes(1);
  });
});
