import React, { useState } from 'react';
import { Shield, X } from 'lucide-react';
import Button from './ui/Button';

interface AgeVerificationModalProps {
  onVerify: () => void;
  onClose: () => void;
}

const AgeVerificationModal: React.FC<AgeVerificationModalProps> = ({ onVerify, onClose }) => {
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  const handleVerify = () => {
    if (agreeToTerms) {
      onVerify();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 border border-gray-800 rounded-lg max-w-md w-full animate-fade-in">
        <div className="relative p-6">
          <button
            className="absolute top-4 right-4 text-gray-400 hover:text-white"
            onClick={onClose}
          >
            <X size={20} />
          </button>

          <div className="flex flex-col items-center mb-6">
            <div className="w-16 h-16 rounded-full bg-blue-700/20 flex items-center justify-center mb-4">
              <Shield size={32} className="text-blue-700" />
            </div>
            <h2 className="text-2xl font-bold text-white">Age Verification</h2>
          </div>

          <div className="space-y-4 text-gray-300">
            <p>
              This website contains adult content and is only suitable for those who are 18 years of age or older.
            </p>
            <p>
              By entering, you confirm that:
            </p>
            <ul className="list-disc pl-6 space-y-2">
              <li>You are at least 18 years old or the age of majority in your jurisdiction, whichever is greater</li>
              <li>You will not allow any minors to access this content</li>
              <li>You accept our Terms of Service and Privacy Policy</li>
              <li>You understand that the content on this site is intended for adults only</li>
            </ul>

            <div className="mt-6">
              <label className="flex items-start cursor-pointer">
                <input
                  type="checkbox"
                  className="mt-1 text-blue-700 border-gray-600 rounded focus:ring-blue-700 focus:ring-offset-gray-900"
                  checked={agreeToTerms}
                  onChange={(e) => setAgreeToTerms(e.target.checked)}
                />
                <span className="ml-3 text-sm text-gray-300">
                  I confirm that I am at least 18 years old and agree to the Terms of Service and Privacy Policy
                </span>
              </label>
            </div>
          </div>

          <div className="mt-6 flex flex-col space-y-2">
            <Button
              variant="primary"
              fullWidth
              disabled={!agreeToTerms}
              onClick={handleVerify}
            >
              I am over 18 - Enter
            </Button>
            <Button
              variant="ghost"
              fullWidth
              onClick={onClose}
            >
              Exit Site
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgeVerificationModal;