import React from 'react';
import { Flame, TrendingUp, Clock } from 'lucide-react';

interface CategorySelectorProps {
  onSelect: (categorySlug: string) => void;
  selectedCategory?: string;
}

// Predefined categories
const categories = [
  { id: '1', name: 'Hot', slug: 'hot', icon: <Flame size={16} className="text-red-500" /> },
  { id: '2', name: 'Trending', slug: 'trending', icon: <TrendingUp size={16} className="text-blue-500" /> },
  { id: '3', name: 'New', slug: 'new', icon: <Clock size={16} className="text-green-500" /> },
];

const CategorySelector: React.FC<CategorySelectorProps> = ({
  onSelect,
  selectedCategory = 'new'
}) => {

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-white mb-2">
        Category
      </label>

      <div className="grid grid-cols-3 gap-3">
        {categories.map((category) => (
          <button
            key={category.id}
            type="button"
            className={`flex flex-col items-center justify-center p-3 rounded-md transition-colors ${
              selectedCategory === category.slug
                ? 'bg-gray-700 border-2 border-blue-700'
                : 'bg-gray-800 border border-gray-700 hover:border-gray-600'
            }`}
            onClick={() => onSelect(category.slug)}
          >
            <div className="mb-2">
              {category.icon}
            </div>
            <span className="text-sm font-medium">{category.name}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategorySelector;
