import React, { useState, useEffect } from 'react';
import { Al<PERSON><PERSON><PERSON>gle, Refresh<PERSON>w, Trash2, Info, CheckCircle, XCircle } from 'lucide-react';
import { 
  checkForStateConflicts, 
  clearBrowserState, 
  forceBrowserRefresh,
  getBrowserStateInfo,
  type StateConflictReport 
} from '../../utils/browserStateManager';

interface BrowserStateDebuggerProps {
  isOpen: boolean;
  onClose: () => void;
}

const BrowserStateDebugger: React.FC<BrowserStateDebuggerProps> = ({ isOpen, onClose }) => {
  const [conflictReport, setConflictReport] = useState<StateConflictReport | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  const checkConflicts = () => {
    setIsLoading(true);
    try {
      const report = checkForStateConflicts();
      setConflictReport(report);
      setLastCheck(new Date());
    } catch (error) {
      console.error('Error checking conflicts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearState = (type: 'auth' | 'preferences' | 'cache' | 'all') => {
    if (confirm(`Are you sure you want to clear ${type} data? This action cannot be undone.`)) {
      clearBrowserState({ [type]: true });
      checkConflicts(); // Re-check after clearing
    }
  };

  const handleForceRefresh = async () => {
    if (confirm('This will clear all cached data and reload the page. Continue?')) {
      await forceBrowserRefresh();
    }
  };

  useEffect(() => {
    if (isOpen) {
      checkConflicts();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const browserInfo = getBrowserStateInfo();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-yellow-500" />
              Browser State Debugger
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>

          {/* Browser Info */}
          <div className="mb-6 p-4 bg-gray-700 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
              <Info className="w-4 h-4" />
              Browser Information
            </h3>
            <div className="space-y-2 text-sm">
              <div><span className="text-gray-400">User Agent:</span> <span className="text-white">{browserInfo.userAgent}</span></div>
              <div><span className="text-gray-400">Session ID:</span> <span className="text-white">{browserInfo.sessionId}</span></div>
              <div><span className="text-gray-400">App Version:</span> <span className="text-white">{browserInfo.version}</span></div>
              <div><span className="text-gray-400">Timestamp:</span> <span className="text-white">{new Date(browserInfo.timestamp).toLocaleString()}</span></div>
            </div>
          </div>

          {/* Conflict Check */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">State Conflict Analysis</h3>
              <button
                onClick={checkConflicts}
                disabled={isLoading}
                className="flex items-center gap-2 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                {isLoading ? 'Checking...' : 'Check Again'}
              </button>
            </div>

            {lastCheck && (
              <p className="text-sm text-gray-400 mb-3">
                Last checked: {lastCheck.toLocaleString()}
              </p>
            )}

            {conflictReport && (
              <div className="space-y-4">
                <div className={`p-4 rounded-lg ${conflictReport.hasConflicts ? 'bg-red-900/30 border border-red-500' : 'bg-green-900/30 border border-green-500'}`}>
                  <div className="flex items-center gap-2 mb-2">
                    {conflictReport.hasConflicts ? (
                      <XCircle className="w-5 h-5 text-red-500" />
                    ) : (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    )}
                    <span className="font-semibold text-white">
                      {conflictReport.hasConflicts ? 'Conflicts Detected' : 'No Conflicts Found'}
                    </span>
                  </div>

                  {conflictReport.conflicts.length > 0 && (
                    <div className="mb-3">
                      <h4 className="text-sm font-semibold text-red-400 mb-2">Issues:</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm text-red-300">
                        {conflictReport.conflicts.map((conflict, index) => (
                          <li key={index}>{conflict}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {conflictReport.recommendations.length > 0 && (
                    <div>
                      <h4 className="text-sm font-semibold text-yellow-400 mb-2">Recommendations:</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm text-yellow-300">
                        {conflictReport.recommendations.map((rec, index) => (
                          <li key={index}>{rec}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Quick Actions</h3>
            
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => handleClearState('auth')}
                className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
              >
                <Trash2 className="w-4 h-4" />
                Clear Auth Data
              </button>

              <button
                onClick={() => handleClearState('preferences')}
                className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                <Trash2 className="w-4 h-4" />
                Clear Preferences
              </button>

              <button
                onClick={() => handleClearState('cache')}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                <Trash2 className="w-4 h-4" />
                Clear Cache
              </button>

              <button
                onClick={() => handleClearState('all')}
                className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                <Trash2 className="w-4 h-4" />
                Clear All Data
              </button>
            </div>

            <button
              onClick={handleForceRefresh}
              className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-red-700 text-white rounded hover:bg-red-800 font-semibold"
            >
              <RefreshCw className="w-4 h-4" />
              Force Complete Refresh
            </button>
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-gray-700 rounded-lg">
            <h4 className="text-sm font-semibold text-white mb-2">Instructions:</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• <strong>Clear Auth Data:</strong> Removes authentication tokens and session data</li>
              <li>• <strong>Clear Preferences:</strong> Removes user preferences, watch history, and favorites</li>
              <li>• <strong>Clear Cache:</strong> Removes cached data and temporary storage</li>
              <li>• <strong>Clear All Data:</strong> Removes everything from browser storage</li>
              <li>• <strong>Force Complete Refresh:</strong> Clears everything including service workers and reloads</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrowserStateDebugger;
