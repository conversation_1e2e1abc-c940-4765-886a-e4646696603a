import React, { useState, useEffect } from 'react';
import { <PERSON>ert<PERSON>riangle, RefreshCw, CheckCircle, XCircle, Database, Wifi } from 'lucide-react';
// import { supabase } from '../../lib/supabase'; // Removed - using MySQL API
import { useVideos } from '../../hooks/useVideos';
import { useTrendingVideos } from '../../hooks/useTrendingVideos';

interface DiagnosticResult {
  test: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: any;
}

interface VideoLoadingDiagnosticProps {
  isOpen: boolean;
  onClose: () => void;
}

const VideoLoadingDiagnostic: React.FC<VideoLoadingDiagnosticProps> = ({ isOpen, onClose }) => {
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  // Get current hook states
  const { videos, isLoading: videosLoading, error: videosError } = useVideos('', 1, 5);
  const { trendingVideos, isLoading: trendingLoading, error: trendingError } = useTrendingVideos(3);

  const runDiagnostics = async () => {
    setIsRunning(true);
    const results: DiagnosticResult[] = [];

    // Test 1: Environment Variables
    results.push({
      test: 'Environment Variables',
      status: 'pending',
      message: 'Checking environment configuration...'
    });

    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      results[0] = {
        test: 'Environment Variables',
        status: 'error',
        message: 'Missing Supabase environment variables',
        details: { hasUrl: !!supabaseUrl, hasKey: !!supabaseKey }
      };
    } else {
      results[0] = {
        test: 'Environment Variables',
        status: 'success',
        message: 'Environment variables configured',
        details: { url: supabaseUrl.substring(0, 30) + '...', keyLength: supabaseKey.length }
      };
    }

    setDiagnostics([...results]);

    // Test 2: Supabase Connection
    results.push({
      test: 'Supabase Connection',
      status: 'pending',
      message: 'Testing database connection...'
    });
    setDiagnostics([...results]);

    try {
      const { data: healthCheck, error: healthError } = await supabase
        .from('videos')
        .select('count')
        .limit(1);

      if (healthError) {
        results[1] = {
          test: 'Supabase Connection',
          status: 'error',
          message: 'Database connection failed',
          details: healthError
        };
      } else {
        results[1] = {
          test: 'Supabase Connection',
          status: 'success',
          message: 'Database connection successful'
        };
      }
    } catch (error) {
      results[1] = {
        test: 'Supabase Connection',
        status: 'error',
        message: 'Connection error',
        details: error
      };
    }

    setDiagnostics([...results]);

    // Test 3: Video Count
    results.push({
      test: 'Video Count',
      status: 'pending',
      message: 'Checking video count...'
    });
    setDiagnostics([...results]);

    try {
      const { count, error: countError } = await supabase
        .from('videos')
        .select('id', { count: 'exact' });

      if (countError) {
        results[2] = {
          test: 'Video Count',
          status: 'error',
          message: 'Failed to count videos',
          details: countError
        };
      } else {
        results[2] = {
          test: 'Video Count',
          status: 'success',
          message: `Found ${count} videos in database`,
          details: { count }
        };
      }
    } catch (error) {
      results[2] = {
        test: 'Video Count',
        status: 'error',
        message: 'Count query failed',
        details: error
      };
    }

    setDiagnostics([...results]);

    // Test 4: Direct Video Fetch
    results.push({
      test: 'Direct Video Fetch',
      status: 'pending',
      message: 'Testing direct video query...'
    });
    setDiagnostics([...results]);

    try {
      const { data: videoData, error: videoError } = await supabase
        .from('videos')
        .select(`
          id,
          title,
          description,
          thumbnail_url,
          video_url,
          duration,
          views,
          likes,
          is_hd,
          user_id,
          created_at,
          updated_at,
          category,
          tags
        `)
        .order('created_at', { ascending: false })
        .limit(3);

      if (videoError) {
        results[3] = {
          test: 'Direct Video Fetch',
          status: 'error',
          message: 'Video query failed',
          details: videoError
        };
      } else {
        results[3] = {
          test: 'Direct Video Fetch',
          status: 'success',
          message: `Successfully fetched ${videoData?.length || 0} videos`,
          details: { videos: videoData?.slice(0, 2) }
        };
      }
    } catch (error) {
      results[3] = {
        test: 'Direct Video Fetch',
        status: 'error',
        message: 'Query execution failed',
        details: error
      };
    }

    setDiagnostics([...results]);

    // Test 5: SWR Hook Status
    results.push({
      test: 'SWR Hook Status',
      status: videosError ? 'error' : (videos.length > 0 ? 'success' : 'pending'),
      message: videosError ? `Hook error: ${videosError}` : 
               videos.length > 0 ? `Hook loaded ${videos.length} videos` : 
               videosLoading ? 'Hook still loading...' : 'Hook not loading',
      details: {
        videosLoading,
        videosError,
        videosCount: videos.length,
        trendingLoading,
        trendingError,
        trendingCount: trendingVideos.length
      }
    });

    setDiagnostics([...results]);
    setIsRunning(false);
  };

  useEffect(() => {
    if (isOpen) {
      runDiagnostics();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'pending':
        return <RefreshCw className="w-4 h-4 text-yellow-500 animate-spin" />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white flex items-center gap-2">
              <Database className="w-5 h-5 text-blue-500" />
              Video Loading Diagnostic
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>

          <div className="space-y-4">
            {diagnostics.map((result, index) => (
              <div key={index} className="p-4 bg-gray-700 rounded-lg">
                <div className="flex items-center gap-3 mb-2">
                  {getStatusIcon(result.status)}
                  <h3 className="font-semibold text-white">{result.test}</h3>
                </div>
                <p className="text-gray-300 mb-2">{result.message}</p>
                {result.details && (
                  <details className="text-sm">
                    <summary className="text-gray-400 cursor-pointer hover:text-white">
                      View Details
                    </summary>
                    <pre className="mt-2 p-2 bg-gray-900 rounded text-xs overflow-x-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>

          <div className="mt-6 flex gap-3">
            <button
              onClick={runDiagnostics}
              disabled={isRunning}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isRunning ? 'animate-spin' : ''}`} />
              {isRunning ? 'Running...' : 'Run Diagnostics'}
            </button>

            <button
              onClick={() => window.location.reload()}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              <RefreshCw className="w-4 h-4" />
              Reload Page
            </button>
          </div>

          <div className="mt-4 p-4 bg-gray-700 rounded-lg">
            <h4 className="font-semibold text-white mb-2">Quick Fixes:</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• If environment variables are missing, check your .env file</li>
              <li>• If database connection fails, verify Supabase URL and key</li>
              <li>• If video count is 0, check if videos exist in your database</li>
              <li>• If SWR hooks are stuck loading, try clearing browser cache</li>
              <li>• Check browser console for additional error messages</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoLoadingDiagnostic;
