import React, { useState } from 'react';
import { Play, RefreshCw, AlertCircle, CheckCircle } from 'lucide-react';
import { Video } from '../../types';

interface ManualVideoLoaderProps {
  onVideosLoaded: (videos: Video[]) => void;
}

const ManualVideoLoader: React.FC<ManualVideoLoaderProps> = ({ onVideosLoaded }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<string>('Ready to load videos');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const loadVideosManually = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);
    setStatus('Starting manual video load...');

    try {
      // Step 1: Check environment variables
      setStatus('Checking environment variables...');
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
      
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Missing environment variables');
      }
      console.log('✅ Environment variables found');

      // Step 2: Test basic connectivity
      setStatus('Testing basic connectivity...');
      try {
        await fetch('https://httpbin.org/get', { 
          method: 'GET',
          signal: AbortSignal.timeout(3000)
        });
        console.log('✅ Basic internet connectivity working');
      } catch (connError) {
        console.warn('⚠️ Basic connectivity test failed:', connError);
      }

      // Step 3: Direct Supabase API call
      setStatus('Connecting to Supabase...');
      const apiUrl = `${supabaseUrl}/rest/v1/videos`;
      const params = new URLSearchParams({
        select: 'id,title,description,thumbnail_url,video_url,duration,views,likes,is_hd,user_id,created_at,updated_at,category,tags',
        order: 'created_at.desc',
        limit: '8'
      });

      console.log('🔗 Attempting fetch to:', `${apiUrl}?${params}`);

      const response = await fetch(`${apiUrl}?${params}`, {
        method: 'GET',
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Cache-Control': 'no-cache'
        },
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      setStatus(`Response received: ${response.status} ${response.statusText}`);
      console.log('📡 Response status:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      // Step 4: Parse response
      setStatus('Parsing video data...');
      const videoData = await response.json();
      console.log('📊 Raw video data:', videoData);

      if (!Array.isArray(videoData)) {
        throw new Error(`Expected array, got ${typeof videoData}`);
      }

      if (videoData.length === 0) {
        throw new Error('No videos found in database');
      }

      // Step 5: Transform data
      setStatus('Transforming video data...');
      const transformedVideos: Video[] = videoData.map((item, index) => {
        console.log(`🔄 Transforming video ${index + 1}:`, item.title);
        return {
          id: item.id,
          title: item.title || 'Untitled Video',
          description: item.description || '',
          thumbnailUrl: item.thumbnail_url || 'https://placehold.co/400x225/gray/white?text=No+Thumbnail',
          videoUrl: item.video_url || '',
          duration: item.duration || 0,
          views: item.views || 0,
          likes: item.likes || 0,
          createdAt: item.created_at,
          updatedAt: item.updated_at || item.created_at,
          publishedAt: item.created_at,
          scheduledFor: undefined,
          status: 'public',
          isHD: item.is_hd || false,
          isPremium: false,
          tags: Array.isArray(item.tags) ? item.tags : [],
          category: item.category || 'uncategorized',
          creator: {
            id: item.user_id || '',
            email: '',
            avatar: 'https://placehold.co/150/gray/white?text=User',
            isVerified: false,
            isCreator: true,
            subscriberCount: 0
          }
        };
      });

      setStatus(`Successfully loaded ${transformedVideos.length} videos!`);
      console.log('✅ Manual video load successful:', transformedVideos.length, 'videos');
      
      onVideosLoaded(transformedVideos);
      setSuccess(true);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('❌ Manual video load failed:', err);
      setError(errorMessage);
      setStatus(`Failed: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (import.meta.env.PROD) {
    return null; // Don't show in production
  }

  return (
    <div className="fixed top-4 right-4 bg-purple-600 text-white p-4 rounded-lg shadow-lg max-w-sm z-50">
      <div className="flex items-center gap-3 mb-3">
        <Play className="w-5 h-5" />
        <h3 className="font-bold">Manual Video Loader</h3>
      </div>
      
      <div className="space-y-3">
        <div className="text-sm">
          <div className="flex items-center gap-2 mb-2">
            {isLoading ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : success ? (
              <CheckCircle className="w-4 h-4 text-green-300" />
            ) : error ? (
              <AlertCircle className="w-4 h-4 text-red-300" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            <span className={success ? 'text-green-300' : error ? 'text-red-300' : 'text-white'}>
              {status}
            </span>
          </div>
        </div>

        {error && (
          <div className="text-red-300 text-xs bg-red-800 p-2 rounded">
            <strong>Error:</strong> {error}
          </div>
        )}

        <button
          onClick={loadVideosManually}
          disabled={isLoading}
          className="w-full bg-white text-purple-600 px-3 py-2 rounded font-medium hover:bg-gray-100 disabled:opacity-50 text-sm"
        >
          {isLoading ? 'Loading...' : success ? 'Load Again' : 'Load Videos Now'}
        </button>

        <div className="text-xs text-purple-200">
          Click to manually load videos with detailed logging
        </div>
      </div>
    </div>
  );
};

export default ManualVideoLoader;
