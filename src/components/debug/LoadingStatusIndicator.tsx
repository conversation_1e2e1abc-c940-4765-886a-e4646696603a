import React from 'react';
import { CheckCircle, AlertTriangle, RefreshCw } from 'lucide-react';

interface LoadingStatusIndicatorProps {
  videosLoading: boolean;
  videosCount: number;
  videosError: string | null;
  fallbackActive?: boolean;
  manualVideosCount?: number;
}

const LoadingStatusIndicator: React.FC<LoadingStatusIndicatorProps> = ({
  videosLoading,
  videosCount,
  videosError,
  fallbackActive = false,
  manualVideosCount = 0
}) => {
  // Don't show in production
  if (import.meta.env.PROD) {
    return null;
  }

  // Determine status
  const hasVideos = videosCount > 0 || manualVideosCount > 0;
  const isWorking = hasVideos && !videosLoading;
  const hasIssues = videosError || (videosLoading && !hasVideos);

  // Don't show if everything is working normally
  if (isWorking && !fallbackActive && !manualVideosCount) {
    return null;
  }

  const getStatusColor = () => {
    if (isWorking) return 'bg-green-600';
    if (hasIssues) return 'bg-red-600';
    return 'bg-yellow-600';
  };

  const getStatusIcon = () => {
    if (isWorking) return <CheckCircle className="w-4 h-4" />;
    if (videosLoading) return <RefreshCw className="w-4 h-4 animate-spin" />;
    return <AlertTriangle className="w-4 h-4" />;
  };

  const getStatusMessage = () => {
    if (manualVideosCount > 0) {
      return `✅ Manual loader successful: ${manualVideosCount} videos loaded`;
    }
    if (fallbackActive) {
      return `🔄 Fallback system active: ${videosCount} videos loaded`;
    }
    if (isWorking) {
      return `✅ Normal loading: ${videosCount} videos loaded`;
    }
    if (videosError) {
      return `❌ Error: ${videosError}`;
    }
    if (videosLoading) {
      return `⏳ Loading videos...`;
    }
    return '⚠️ Unknown status';
  };

  return (
    <div className={`fixed top-4 left-1/2 transform -translate-x-1/2 ${getStatusColor()} text-white px-4 py-2 rounded-lg shadow-lg z-50 max-w-md`}>
      <div className="flex items-center gap-2 text-sm">
        {getStatusIcon()}
        <span>{getStatusMessage()}</span>
      </div>
      
      {(manualVideosCount > 0 || fallbackActive) && (
        <div className="text-xs mt-1 opacity-90">
          The backup loading system is working. You can continue using the site normally.
        </div>
      )}
    </div>
  );
};

export default LoadingStatusIndicator;
