import React, { useState } from 'react';
import { Wifi, Alert<PERSON>riangle, CheckCircle, XCircle, RefreshCw } from 'lucide-react';

interface NetworkTest {
  name: string;
  status: 'pending' | 'success' | 'error' | 'idle';
  message: string;
  duration?: number;
  details?: any;
}

const NetworkDiagnostic: React.FC = () => {
  const [tests, setTests] = useState<NetworkTest[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (index: number, updates: Partial<NetworkTest>) => {
    setTests(prev => prev.map((test, i) => i === index ? { ...test, ...updates } : test));
  };

  const runNetworkTests = async () => {
    setIsRunning(true);
    const initialTests: NetworkTest[] = [
      { name: 'Basic Connectivity', status: 'idle', message: 'Not started' },
      { name: 'Supabase REST API', status: 'idle', message: 'Not started' },
      { name: 'CORS Headers', status: 'idle', message: 'Not started' },
      { name: 'Browser Fetch API', status: 'idle', message: 'Not started' },
      { name: 'Environment Variables', status: 'idle', message: 'Not started' }
    ];
    setTests(initialTests);

    // Test 1: Basic Connectivity
    updateTest(0, { status: 'pending', message: 'Testing basic connectivity...' });
    try {
      const start = Date.now();
      const response = await fetch('https://httpbin.org/get', { 
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });
      const duration = Date.now() - start;
      
      if (response.ok) {
        updateTest(0, { 
          status: 'success', 
          message: `Basic connectivity working (${duration}ms)`,
          duration 
        });
      } else {
        updateTest(0, { 
          status: 'error', 
          message: `HTTP ${response.status}: ${response.statusText}` 
        });
      }
    } catch (error) {
      updateTest(0, { 
        status: 'error', 
        message: `Connectivity failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      });
    }

    // Test 2: Supabase REST API
    updateTest(1, { status: 'pending', message: 'Testing Supabase API...' });
    try {
      const start = Date.now();
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
      
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Missing environment variables');
      }

      const response = await fetch(`${supabaseUrl}/rest/v1/videos?select=id&limit=1`, {
        method: 'GET',
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json'
        },
        signal: AbortSignal.timeout(10000)
      });
      
      const duration = Date.now() - start;
      
      if (response.ok) {
        const data = await response.json();
        updateTest(1, { 
          status: 'success', 
          message: `Supabase API working (${duration}ms, ${data.length} records)`,
          duration,
          details: { status: response.status, dataLength: data.length }
        });
      } else {
        const errorText = await response.text();
        updateTest(1, { 
          status: 'error', 
          message: `API Error ${response.status}: ${errorText}`,
          details: { status: response.status, error: errorText }
        });
      }
    } catch (error) {
      updateTest(1, { 
        status: 'error', 
        message: `Supabase API failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      });
    }

    // Test 3: CORS Headers
    updateTest(2, { status: 'pending', message: 'Checking CORS headers...' });
    try {
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'OPTIONS',
        signal: AbortSignal.timeout(5000)
      });
      
      const corsHeaders = {
        'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
        'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
        'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
      };
      
      updateTest(2, { 
        status: 'success', 
        message: 'CORS headers present',
        details: corsHeaders
      });
    } catch (error) {
      updateTest(2, { 
        status: 'error', 
        message: `CORS check failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      });
    }

    // Test 4: Browser Fetch API
    updateTest(3, { status: 'pending', message: 'Testing browser fetch capabilities...' });
    try {
      const capabilities = {
        fetchSupported: typeof fetch !== 'undefined',
        abortControllerSupported: typeof AbortController !== 'undefined',
        promiseSupported: typeof Promise !== 'undefined',
        timeoutSupported: typeof AbortSignal !== 'undefined' && 'timeout' in AbortSignal
      };
      
      const allSupported = Object.values(capabilities).every(Boolean);
      
      updateTest(3, { 
        status: allSupported ? 'success' : 'error', 
        message: allSupported ? 'All fetch capabilities supported' : 'Some fetch capabilities missing',
        details: capabilities
      });
    } catch (error) {
      updateTest(3, { 
        status: 'error', 
        message: `Fetch API test failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      });
    }

    // Test 5: Environment Variables
    updateTest(4, { status: 'pending', message: 'Checking environment variables...' });
    try {
      const env = {
        VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
        VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY,
        MODE: import.meta.env.MODE,
        DEV: import.meta.env.DEV
      };
      
      const hasRequired = env.VITE_SUPABASE_URL && env.VITE_SUPABASE_ANON_KEY;
      
      updateTest(4, { 
        status: hasRequired ? 'success' : 'error', 
        message: hasRequired ? 'Environment variables configured' : 'Missing required environment variables',
        details: {
          hasUrl: !!env.VITE_SUPABASE_URL,
          hasKey: !!env.VITE_SUPABASE_ANON_KEY,
          mode: env.MODE,
          dev: env.DEV
        }
      });
    } catch (error) {
      updateTest(4, { 
        status: 'error', 
        message: `Environment check failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      });
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: NetworkTest['status']) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'pending': return <RefreshCw className="w-4 h-4 text-yellow-500 animate-spin" />;
      case 'idle': return <div className="w-4 h-4 rounded-full bg-gray-500" />;
    }
  };

  if (import.meta.env.PROD) {
    return null; // Don't show in production
  }

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-2xl w-full z-50 max-h-96 overflow-y-auto">
      <div className="flex items-center gap-3 mb-4">
        <Wifi className="w-5 h-5 text-blue-500" />
        <h3 className="font-bold text-lg">Network Diagnostic</h3>
        <button
          onClick={runNetworkTests}
          disabled={isRunning}
          className="ml-auto bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm disabled:opacity-50"
        >
          {isRunning ? 'Running...' : 'Run Tests'}
        </button>
      </div>

      {tests.length > 0 && (
        <div className="space-y-3">
          {tests.map((test, index) => (
            <div key={index} className="flex items-start gap-3 p-3 bg-gray-700 rounded">
              {getStatusIcon(test.status)}
              <div className="flex-1">
                <div className="font-medium">{test.name}</div>
                <div className="text-sm text-gray-300">{test.message}</div>
                {test.duration && (
                  <div className="text-xs text-blue-300">Duration: {test.duration}ms</div>
                )}
                {test.details && (
                  <details className="mt-2">
                    <summary className="text-xs text-gray-400 cursor-pointer">Details</summary>
                    <pre className="text-xs bg-gray-900 p-2 rounded mt-1 overflow-x-auto">
                      {JSON.stringify(test.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {tests.length === 0 && (
        <div className="text-center text-gray-400 py-8">
          Click "Run Tests" to diagnose network connectivity issues
        </div>
      )}
    </div>
  );
};

export default NetworkDiagnostic;
