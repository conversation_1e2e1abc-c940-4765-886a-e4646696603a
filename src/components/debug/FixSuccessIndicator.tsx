import React, { useState, useEffect } from 'react';
import { CheckCircle, X } from 'lucide-react';

interface FixSuccessIndicatorProps {
  show: boolean;
  message: string;
  autoHide?: boolean;
  duration?: number;
}

const FixSuccessIndicator: React.FC<FixSuccessIndicatorProps> = ({ 
  show, 
  message, 
  autoHide = true, 
  duration = 5000 
}) => {
  const [visible, setVisible] = useState(show);

  useEffect(() => {
    setVisible(show);
    
    if (show && autoHide) {
      const timer = setTimeout(() => {
        setVisible(false);
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [show, autoHide, duration]);

  if (!visible || import.meta.env.PROD) {
    return null;
  }

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 max-w-md">
      <div className="flex items-center gap-3">
        <CheckCircle className="w-5 h-5 flex-shrink-0" />
        <span className="text-sm font-medium">{message}</span>
        <button
          onClick={() => setVisible(false)}
          className="ml-2 text-green-200 hover:text-white"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default FixSuccessIndicator;
