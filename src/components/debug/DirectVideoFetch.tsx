import React, { useState, useEffect } from 'react';
// import { supabase } from '../../lib/supabase'; // Removed - using MySQL API
import { Video } from '../../types';
import { getWorkingThumbnailUrl, getWorkingVideoUrl } from '../../utils/mediaUtils';

const DirectVideoFetch: React.FC = () => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fetchTime, setFetchTime] = useState<number>(0);

  const fetchVideosDirectly = async () => {
    const startTime = Date.now();
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔍 DirectVideoFetch: Starting direct fetch...');
      
      const { data: videoData, error: videoError } = await supabase
        .from('videos')
        .select(`
          id,
          title,
          description,
          thumbnail_url,
          video_url,
          duration,
          views,
          likes,
          is_hd,
          user_id,
          created_at,
          updated_at,
          category,
          tags
        `)
        .order('created_at', { ascending: false })
        .limit(5);

      const endTime = Date.now();
      setFetchTime(endTime - startTime);

      if (videoError) {
        console.error('❌ DirectVideoFetch error:', videoError);
        setError(videoError.message);
        return;
      }

      console.log('✅ DirectVideoFetch success:', { count: videoData?.length });

      // Transform the data to match our Video type
      const transformedVideos: Video[] = (videoData || []).map(item => ({
        id: item.id,
        title: item.title,
        description: item.description || '',
        thumbnailUrl: getWorkingThumbnailUrl(item.thumbnail_url || ''),
        videoUrl: getWorkingVideoUrl(item.video_url || ''),
        duration: item.duration || 0,
        views: item.views || 0,
        likes: item.likes || 0,
        createdAt: item.created_at,
        updatedAt: item.updated_at || item.created_at,
        publishedAt: item.created_at,
        scheduledFor: undefined,
        status: 'public',
        isHD: item.is_hd || false,
        isPremium: false,
        tags: Array.isArray(item.tags) ? item.tags : [],
        category: item.category || 'uncategorized',
        creator: {
          id: item.user_id || '',
          email: '',
          avatar: 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      }));

      setVideos(transformedVideos);
      console.log('🎯 DirectVideoFetch transformed videos:', transformedVideos.length);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('❌ DirectVideoFetch exception:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVideosDirectly();
  }, []);

  if (import.meta.env.PROD) {
    return null; // Don't show in production
  }

  return (
    <div className="fixed top-20 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-md z-50 max-h-96 overflow-y-auto">
      <h3 className="font-bold text-sm mb-2 flex items-center gap-2">
        🔧 Direct Video Fetch Test
        <button
          onClick={fetchVideosDirectly}
          className="text-xs bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded"
        >
          Retry
        </button>
      </h3>
      
      <div className="text-xs space-y-2">
        <div>
          <strong>Status:</strong> {loading ? 'Loading...' : error ? 'Error' : 'Success'}
        </div>
        
        <div>
          <strong>Fetch Time:</strong> {fetchTime}ms
        </div>
        
        <div>
          <strong>Videos Found:</strong> {videos.length}
        </div>

        {error && (
          <div className="bg-red-600 p-2 rounded text-xs">
            <strong>Error:</strong> {error}
          </div>
        )}

        {videos.length > 0 && (
          <div>
            <strong>Sample Video:</strong>
            <div className="ml-2 text-xs bg-gray-700 p-2 rounded mt-1">
              <div><strong>ID:</strong> {videos[0].id}</div>
              <div><strong>Title:</strong> {videos[0].title.substring(0, 30)}...</div>
              <div><strong>Category:</strong> {videos[0].category}</div>
              <div><strong>Views:</strong> {videos[0].views}</div>
              <div><strong>Thumbnail:</strong> {videos[0].thumbnailUrl ? '✓' : '✗'}</div>
              <div><strong>Video URL:</strong> {videos[0].videoUrl ? '✓' : '✗'}</div>
            </div>
          </div>
        )}

        {!loading && videos.length === 0 && !error && (
          <div className="bg-yellow-600 p-2 rounded text-xs">
            <strong>Warning:</strong> No videos found in database
          </div>
        )}
      </div>
    </div>
  );
};

export default DirectVideoFetch;
