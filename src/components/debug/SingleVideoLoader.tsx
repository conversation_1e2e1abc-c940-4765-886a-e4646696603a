import React, { useState, useEffect } from 'react';
import { Play, RefreshCw, AlertTriangle, CheckCircle } from 'lucide-react';
import { Video } from '../../types';
import { getWorkingThumbnailUrl, getWorkingVideoUrl } from '../../utils/mediaUtils';

interface SingleVideoLoaderProps {
  videoId: string;
  onVideoLoaded: (video: Video) => void;
  isVisible: boolean;
}

const SingleVideoLoader: React.FC<SingleVideoLoaderProps> = ({ videoId, onVideoLoaded, isVisible }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const loadVideoDirectly = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      console.log('🚨 SingleVideoLoader: Loading video directly for ID:', videoId);

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Missing environment variables');
      }

      // Use direct fetch for single video
      const params = new URLSearchParams({
        select: 'id,title,description,thumbnail_url,video_url,duration,views,likes,is_hd,user_id,created_at,updated_at,category,tags',
        id: `eq.${videoId}`
      });

      const response = await fetch(`${supabaseUrl}/rest/v1/videos?${params}`, {
        method: 'GET',
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const videoArray = await response.json();

      if (!Array.isArray(videoArray) || videoArray.length === 0) {
        throw new Error('Video not found');
      }

      const data = videoArray[0];

      // Transform to Video type
      const video: Video = {
        id: data.id,
        title: data.title || 'Untitled Video',
        description: data.description || '',
        thumbnailUrl: getWorkingThumbnailUrl(data.thumbnail_url || ''),
        videoUrl: getWorkingVideoUrl(data.video_url || ''),
        duration: data.duration || 0,
        views: data.views || 0,
        likes: data.likes || 0,
        createdAt: data.created_at,
        updatedAt: data.updated_at || data.created_at,
        publishedAt: data.created_at,
        scheduledFor: undefined,
        status: 'public',
        isHD: data.is_hd || false,
        isPremium: false,
        tags: Array.isArray(data.tags) ? data.tags : [],
        category: data.category || 'uncategorized',
        creator: {
          id: data.user_id || '',
          email: '',
          avatar: 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      };

      // console.log('✅ SingleVideoLoader: Successfully loaded video:', video.title);
      onVideoLoaded(video);
      setSuccess(true);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      // console.error('❌ SingleVideoLoader failed:', err);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-load when visible
  useEffect(() => {
    if (isVisible && !success && videoId) {
      loadVideoDirectly();
    }
  }, [isVisible, success, videoId]);

  if (!isVisible || import.meta.env.PROD) return null;

  return (
    <div className="fixed top-20 right-4 bg-orange-600 text-white p-4 rounded-lg shadow-lg max-w-sm z-50">
      <div className="flex items-center gap-3 mb-3">
        <Play className="w-5 h-5" />
        <h3 className="font-bold">Single Video Loader</h3>
      </div>

      <p className="mb-3 text-sm">
        Loading individual video directly from database...
      </p>

      <div className="flex items-center gap-3 mb-3">
        {isLoading && (
          <div className="flex items-center gap-2 text-sm">
            <RefreshCw className="w-4 h-4 animate-spin" />
            Loading video...
          </div>
        )}

        {success && (
          <div className="flex items-center gap-2 text-sm text-green-200">
            <CheckCircle className="w-4 h-4" />
            Video loaded successfully!
          </div>
        )}

        {error && (
          <div className="text-red-200 text-sm">
            <strong>Error:</strong> {error}
          </div>
        )}
      </div>

      <div className="flex gap-2">
        <button
          onClick={loadVideoDirectly}
          disabled={isLoading}
          className="bg-white text-orange-600 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100 disabled:opacity-50"
        >
          {isLoading ? 'Loading...' : 'Retry'}
        </button>

        <button
          onClick={() => window.history.back()}
          className="bg-orange-800 text-white px-3 py-1 rounded text-sm font-medium hover:bg-orange-900"
        >
          Go Back
        </button>
      </div>

      <div className="text-xs text-orange-200 mt-2">
        Video ID: {videoId}
      </div>
    </div>
  );
};

export default SingleVideoLoader;
