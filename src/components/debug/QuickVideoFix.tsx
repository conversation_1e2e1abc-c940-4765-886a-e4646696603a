import React, { useState, useEffect } from 'react';
import { Refresh<PERSON>w, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>Circle, Trash2 } from 'lucide-react';
// import { supabase } from '../../lib/supabase'; // Removed - using MySQL API
import { Video } from '../../types';
import { getWorkingThumbnailUrl, getWorkingVideoUrl } from '../../utils/mediaUtils';
import { clearSWRCache, clearVideoCache, resetSWRState } from '../../utils/swrUtils';

interface QuickVideoFixProps {
  onVideosLoaded: (videos: Video[]) => void;
  isVisible: boolean;
}

const QuickVideoFix: React.FC<QuickVideoFixProps> = ({ onVideosLoaded, isVisible }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [isClearing, setIsClearing] = useState(false);

  const fetchVideosDirectly = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      console.log('🚀 QuickVideoFix: Fetching videos directly...');

      const { data: videoData, error: videoError } = await supabase
        .from('videos')
        .select(`
          id,
          title,
          description,
          thumbnail_url,
          video_url,
          duration,
          views,
          likes,
          is_hd,
          user_id,
          created_at,
          updated_at,
          category,
          tags
        `)
        .order('created_at', { ascending: false })
        .limit(12);

      if (videoError) {
        throw new Error(videoError.message);
      }

      if (!videoData || videoData.length === 0) {
        throw new Error('No videos found in database');
      }

      // Transform the data
      const transformedVideos: Video[] = videoData.map(item => ({
        id: item.id,
        title: item.title,
        description: item.description || '',
        thumbnailUrl: getWorkingThumbnailUrl(item.thumbnail_url || ''),
        videoUrl: getWorkingVideoUrl(item.video_url || ''),
        duration: item.duration || 0,
        views: item.views || 0,
        likes: item.likes || 0,
        createdAt: item.created_at,
        updatedAt: item.updated_at || item.created_at,
        publishedAt: item.created_at,
        scheduledFor: undefined,
        status: 'public',
        isHD: item.is_hd || false,
        isPremium: false,
        tags: Array.isArray(item.tags) ? item.tags : [],
        category: item.category || 'uncategorized',
        creator: {
          id: item.user_id || '',
          email: '',
          avatar: 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0
        }
      }));

      // console.log('✅ QuickVideoFix: Successfully loaded', transformedVideos.length, 'videos');
      onVideosLoaded(transformedVideos);
      setSuccess(true);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      // console.error('❌ QuickVideoFix error:', err);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const clearSWRAndRetry = async () => {
    setIsClearing(true);
    try {
      // console.log('🧹 Clearing SWR cache and retrying...');
      await resetSWRState();
      await fetchVideosDirectly();
    } catch (err) {
      // console.error('❌ Failed to clear SWR and retry:', err);
    } finally {
      setIsClearing(false);
    }
  };

  // Auto-fetch on mount if visible
  useEffect(() => {
    if (isVisible && !success) {
      fetchVideosDirectly();
    }
  }, [isVisible, success]);

  if (!isVisible) return null;

  return (
    <div className="bg-blue-600 text-white p-4 rounded-lg mb-6">
      <div className="flex items-center gap-3 mb-3">
        <AlertTriangle className="w-5 h-5" />
        <h3 className="font-bold">Quick Video Fix</h3>
      </div>

      <p className="mb-3 text-sm">
        Attempting to bypass SWR and load videos directly from the database...
      </p>

      <div className="flex items-center gap-3">
        {isLoading && (
          <div className="flex items-center gap-2 text-sm">
            <RefreshCw className="w-4 h-4 animate-spin" />
            Loading videos...
          </div>
        )}

        {success && (
          <div className="flex items-center gap-2 text-sm text-green-200">
            <CheckCircle className="w-4 h-4" />
            Videos loaded successfully!
          </div>
        )}

        {error && (
          <div className="text-red-200 text-sm">
            Error: {error}
          </div>
        )}

        <button
          onClick={fetchVideosDirectly}
          disabled={isLoading || isClearing}
          className="bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100 disabled:opacity-50"
        >
          {isLoading ? 'Loading...' : 'Retry'}
        </button>

        <button
          onClick={clearSWRAndRetry}
          disabled={isLoading || isClearing}
          className="bg-red-500 text-white px-3 py-1 rounded text-sm font-medium hover:bg-red-600 disabled:opacity-50 flex items-center gap-1"
        >
          <Trash2 className="w-3 h-3" />
          {isClearing ? 'Clearing...' : 'Clear Cache & Retry'}
        </button>
      </div>
    </div>
  );
};

export default QuickVideoFix;
