import React from 'react';

interface EnvironmentCheckProps {
  visible?: boolean;
}

const EnvironmentCheck: React.FC<EnvironmentCheckProps> = ({ visible = false }) => {
  if (!visible) return null;

  const envVars = {
    VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
    VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY,
    MODE: import.meta.env.MODE,
    DEV: import.meta.env.DEV,
    PROD: import.meta.env.PROD,
  };

  return (
    <div className="fixed top-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-md z-50">
      <h3 className="font-bold mb-2">Environment Variables</h3>
      <div className="space-y-1">
        {Object.entries(envVars).map(([key, value]) => (
          <div key={key} className="flex justify-between">
            <span className="text-gray-300">{key}:</span>
            <span className={value ? 'text-green-400' : 'text-red-400'}>
              {key.includes('KEY') ? (value ? '✓ Set' : '✗ Missing') : String(value)}
            </span>
          </div>
        ))}
      </div>
      <div className="mt-2 pt-2 border-t border-gray-600">
        <div className="text-gray-300">
          URL: {window.location.href}
        </div>
      </div>
    </div>
  );
};

export default EnvironmentCheck;
