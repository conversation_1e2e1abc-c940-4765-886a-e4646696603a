import React from 'react';
import { useVideos } from '../../hooks/useVideos';
import { useTrendingVideos } from '../../hooks/useTrendingVideos';

const VideoDataDebug: React.FC = () => {
  const { videos, isLoading: videosLoading, error: videosError } = useVideos('all', 1, 5);
  const { trendingVideos, isLoading: trendingLoading } = useTrendingVideos(3);

  if (import.meta.env.PROD) {
    return null; // Don't show in production
  }

  return (
    <div className="fixed top-4 left-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-md z-50 max-h-96 overflow-y-auto">
      <h3 className="font-bold text-sm mb-2">📊 Video Data Debug</h3>
      
      <div className="text-xs space-y-2">
        <div>
          <strong>All Videos:</strong>
          <div className="ml-2">
            Loading: {videosLoading ? 'Yes' : 'No'}
            <br />
            Error: {videosError ? 'Yes' : 'No'}
            <br />
            Count: {videos.length}
          </div>
        </div>

        <div>
          <strong>Trending Videos:</strong>
          <div className="ml-2">
            Loading: {trendingLoading ? 'Yes' : 'No'}
            <br />
            Count: {trendingVideos.length}
          </div>
        </div>

        {videos.length > 0 && (
          <div>
            <strong>First Video Sample:</strong>
            <div className="ml-2 text-xs bg-gray-700 p-2 rounded mt-1">
              <div>ID: {videos[0].id}</div>
              <div>Title: {videos[0].title}</div>
              <div>Thumbnail: {videos[0].thumbnailUrl ? '✓ Present' : '✗ Missing'}</div>
              <div>Video URL: {videos[0].videoUrl ? '✓ Present' : '✗ Missing'}</div>
              <div>Views: {videos[0].views}</div>
              <div>Duration: {videos[0].duration}</div>
            </div>
          </div>
        )}

        {videosError && (
          <div className="bg-red-600 p-2 rounded text-xs">
            <strong>Error:</strong> {videosError.message}
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoDataDebug;
