import React, { useState, useEffect } from 'react';
import { apiClient } from '../../lib/api';

const SupabaseDebug: React.FC = () => {
  const [status, setStatus] = useState<string>('Testing...');
  const [results, setResults] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testConnection = async () => {
      try {
        setStatus('Testing MySQL API connection...');

        // Test 1: Basic connection
        const healthResponse = await apiClient.getVideos({ limit: 1 });

        if (!healthResponse.success) {
          throw new Error(`Health check failed: ${healthResponse.error}`);
        }

        // Test 2: Count videos
        const countResponse = await apiClient.getVideos({ limit: 1 });

        if (!countResponse.success) {
          throw new Error(`Count failed: ${countResponse.error}`);
        }

        const count = countResponse.data.pagination?.total || 0;
        setStatus(`Found ${count} videos in database`);

        // Test 3: Fetch sample videos
        const videosResponse = await apiClient.getVideos({
          limit: 5,
          sort: 'created_at',
          order: 'DESC'
        });

        if (!videosResponse.success) {
          throw new Error(`Video fetch failed: ${videosResponse.error}`);
        }

        const videos = videosResponse.data.videos || [];
        setResults(videos);
        setStatus(`Successfully loaded ${videos.length} videos`);

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        setStatus('Connection failed');
        console.error('MySQL API test error:', err);
      }
    };

    testConnection();
  }, []);

  if (import.meta.env.PROD) {
    return null; // Don't show in production
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-md z-50">
      <h3 className="font-bold text-sm mb-2">🔍 MySQL API Debug</h3>
      <p className="text-xs mb-2">{status}</p>

      {error && (
        <div className="bg-red-600 p-2 rounded text-xs mb-2">
          <strong>Error:</strong> {error}
        </div>
      )}

      {results.length > 0 && (
        <div className="text-xs">
          <strong>Sample Videos:</strong>
          <ul className="mt-1 space-y-1">
            {results.slice(0, 3).map((video, index) => (
              <li key={video.id} className="truncate">
                {index + 1}. {video.title}
                <br />
                <span className="text-gray-500">
                  Thumbnail: {video.thumbnail_url ? '✓' : '✗'}
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}

      <div className="mt-2 text-xs text-gray-400">
        Environment: {import.meta.env.DEV ? 'Development' : 'Production'}
      </div>
    </div>
  );
};

export default SupabaseDebug;
