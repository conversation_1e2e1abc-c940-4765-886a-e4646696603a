import React, { useState, useEffect } from 'react';
// import { supabase, getVideoQuery } from '../../lib/supabase'; // Removed - using MySQL API

const SupabaseDebug: React.FC = () => {
  const [status, setStatus] = useState<string>('Testing...');
  const [results, setResults] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testConnection = async () => {
      try {
        setStatus('Testing Supabase connection...');

        // Test 1: Basic connection
        const { data: healthCheck, error: healthError } = await supabase
          .from('videos')
          .select('count')
          .limit(1);

        if (healthError) {
          throw new Error(`Health check failed: ${healthError.message}`);
        }

        // Test 2: Count videos
        const { count, error: countError } = await supabase
          .from('videos')
          .select('id', { count: 'exact' });

        if (countError) {
          throw new Error(`Count failed: ${countError.message}`);
        }

        setStatus(`Found ${count} videos in database`);

        // Test 3: Fetch sample videos
        const { data: videos, error: videosError } = await getVideoQuery(
          supabase.from('videos')
        )
          .order('created_at', { ascending: false })
          .limit(5);

        if (videosError) {
          throw new Error(`Video fetch failed: ${videosError.message}`);
        }

        setResults(videos || []);
        setStatus(`Successfully loaded ${videos?.length || 0} videos`);

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        setStatus('Connection failed');
        console.error('Supabase test error:', err);
      }
    };

    testConnection();
  }, []);

  if (import.meta.env.PROD) {
    return null; // Don't show in production
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-md z-50">
      <h3 className="font-bold text-sm mb-2">🔍 Supabase Debug</h3>
      <p className="text-xs mb-2">{status}</p>

      {error && (
        <div className="bg-red-600 p-2 rounded text-xs mb-2">
          <strong>Error:</strong> {error}
        </div>
      )}

      {results.length > 0 && (
        <div className="text-xs">
          <strong>Sample Videos:</strong>
          <ul className="mt-1 space-y-1">
            {results.slice(0, 3).map((video, index) => (
              <li key={video.id} className="truncate">
                {index + 1}. {video.title}
                <br />
                <span className="text-gray-500">
                  Thumbnail: {video.thumbnail_url ? '✓' : '✗'}
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}

      <div className="mt-2 text-xs text-gray-400">
        Environment: {import.meta.env.DEV ? 'Development' : 'Production'}
      </div>
    </div>
  );
};

export default SupabaseDebug;
