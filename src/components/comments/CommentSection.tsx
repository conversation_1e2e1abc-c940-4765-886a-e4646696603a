import React, { useState, useEffect } from 'react';
import { useCommentStore, Comment } from '../../stores/commentStore';
import { useAuthStore } from '../../stores/authStore';
import { MessageSquare, ThumbsUp, Trash2, Reply, Send, User } from 'lucide-react';
import Button from '../ui/Button';

interface CommentSectionProps {
  videoId: string;
}

const CommentSection: React.FC<CommentSectionProps> = ({ videoId }) => {
  const { user } = useAuthStore();
  const {
    comments,
    isLoading,
    error,
    fetchComments,
    addComment,
    deleteComment,
    likeComment,
    unlikeComment,
    addReply
  } = useCommentStore();

  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [anonymousName, setAnonymousName] = useState('');
  const [anonymousNameError, setAnonymousNameError] = useState('');

  // Load anonymous name from localStorage if available
  useEffect(() => {
    const savedName = localStorage.getItem('anonymousUserName');
    if (savedName) {
      setAnonymousName(savedName);
    }
  }, []);

  useEffect(() => {
    if (videoId) {
      fetchComments(videoId);
    }
  }, [videoId, fetchComments]);

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    // For anonymous users, validate name
    if (!user && !anonymousName.trim()) {
      setAnonymousNameError('Please enter a display name');
      return;
    }

    // Clear any previous error
    setAnonymousNameError('');

    // If anonymous user, save name to localStorage
    if (!user && anonymousName.trim()) {
      localStorage.setItem('anonymousUserName', anonymousName.trim());
    }

    const result = await addComment(videoId, newComment, anonymousName);
    if (result) {
      setNewComment('');
    }
  };

  const handleSubmitReply = async (commentId: string) => {
    if (!replyContent.trim()) return;

    // For anonymous users, validate name
    if (!user && !anonymousName.trim()) {
      setAnonymousNameError('Please enter a display name');
      return;
    }

    // Clear any previous error
    setAnonymousNameError('');

    // If anonymous user, save name to localStorage
    if (!user && anonymousName.trim()) {
      localStorage.setItem('anonymousUserName', anonymousName.trim());
    }

    const result = await addReply(commentId, replyContent, anonymousName);
    if (result) {
      setReplyContent('');
      setReplyingTo(null);
    }
  };

  const handleLikeComment = async (comment: Comment) => {
    // For anonymous users, we'll use localStorage to track liked comments
    if (!user) {
      const likedComments = JSON.parse(localStorage.getItem('anonymousLikedComments') || '[]');

      if (comment.isLiked) {
        // Remove comment from liked comments
        const updatedLikedComments = likedComments.filter((id: string) => id !== comment.id);
        localStorage.setItem('anonymousLikedComments', JSON.stringify(updatedLikedComments));
        await unlikeComment(comment.id, true); // true indicates anonymous user
      } else {
        // Add comment to liked comments
        likedComments.push(comment.id);
        localStorage.setItem('anonymousLikedComments', JSON.stringify(likedComments));
        await likeComment(comment.id, true); // true indicates anonymous user
      }
    } else {
      // For logged-in users, use the existing functionality
      if (comment.isLiked) {
        await unlikeComment(comment.id);
      } else {
        await likeComment(comment.id);
      }
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (window.confirm('Are you sure you want to delete this comment?')) {
      await deleteComment(commentId);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="mt-6">
      <h3 className="text-xl font-bold text-white mb-4">
        <MessageSquare className="inline-block mr-2" size={20} />
        Comments ({comments.length})
      </h3>

      {error && (
        <div className="bg-red-500/20 border border-red-500 rounded-md p-3 mb-4">
          <p className="text-red-200">{error}</p>
        </div>
      )}

      {/* Comment form */}
      <form onSubmit={handleSubmitComment} className="mb-6">
        <div className="flex items-start">
          {user ? (
            <img
              src={user.avatar || 'https://placehold.co/150/gray/white?text=User'}
              alt="User"
              className="w-10 h-10 rounded-full object-cover mr-3"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center mr-3">
              <User size={20} className="text-gray-400" />
            </div>
          )}
          <div className="flex-grow">
            {!user && (
              <div className="mb-3">
                <input
                  type="text"
                  value={anonymousName}
                  onChange={(e) => setAnonymousName(e.target.value)}
                  placeholder="Your display name"
                  className="w-full bg-gray-800 text-white rounded-md p-3 focus:outline-none focus:ring-2 focus:ring-blue-700 mb-1"
                />
                {anonymousNameError && (
                  <p className="text-red-500 text-sm">{anonymousNameError}</p>
                )}
              </div>
            )}
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Add a comment..."
              className="w-full bg-gray-800 text-white rounded-md p-3 focus:outline-none focus:ring-2 focus:ring-blue-700 resize-none"
              rows={3}
            />
            <div className="flex justify-end mt-2">
              <Button
                type="submit"
                variant="primary"
                size="sm"
                disabled={!newComment.trim()}
              >
                Comment
              </Button>
            </div>
          </div>
        </div>
      </form>

      {/* Comments list */}
      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
        </div>
      ) : comments.length === 0 ? (
        <div className="bg-gray-800 rounded-md p-6 text-center">
          <MessageSquare size={32} className="mx-auto text-gray-600 mb-3" />
          <p className="text-gray-400">No comments yet. Be the first to comment!</p>
        </div>
      ) : (
        <div className="space-y-4">
          {comments.map((comment) => (
            <div key={comment.id} className="bg-gray-800 rounded-md p-4">
              <div className="flex items-start">
                <img
                  src={comment.userAvatar || 'https://placehold.co/150/gray/white?text=User'}
                  alt={comment.userName}
                  className="w-10 h-10 rounded-full object-cover mr-3"
                />
                <div className="flex-grow">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="font-medium text-white">{comment.userName}</span>
                      <span className="text-gray-400 text-sm ml-2">{formatDate(comment.createdAt)}</span>
                    </div>
                    {user && user.id === comment.userId && (
                      <button
                        onClick={() => handleDeleteComment(comment.id)}
                        className="text-gray-400 hover:text-red-500 transition-colors"
                        aria-label="Delete comment"
                      >
                        <Trash2 size={16} />
                      </button>
                    )}
                  </div>
                  <p className="text-gray-300 mt-1">{comment.content}</p>
                  <div className="flex items-center mt-2 space-x-4">
                    <button
                      onClick={() => handleLikeComment(comment)}
                      className={`flex items-center text-sm ${
                        comment.isLiked ? 'text-blue-700' : 'text-gray-400 hover:text-blue-700'
                      } transition-colors`}
                      aria-label={comment.isLiked ? 'Unlike comment' : 'Like comment'}
                    >
                      <ThumbsUp size={14} className="mr-1" />
                      <span>{comment.likes}</span>
                    </button>
                    <button
                      onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                      className="flex items-center text-sm text-gray-400 hover:text-blue-700 transition-colors"
                      aria-label="Reply to comment"
                    >
                      <Reply size={14} className="mr-1" />
                      <span>Reply</span>
                    </button>
                  </div>

                  {/* Reply form */}
                  {replyingTo === comment.id && (
                    <div className="mt-3">
                      <div className="flex items-start">
                        {user ? (
                          <img
                            src={user.avatar || 'https://placehold.co/150/gray/white?text=User'}
                            alt="User"
                            className="w-8 h-8 rounded-full object-cover mr-2"
                          />
                        ) : (
                          <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center mr-2">
                            <User size={16} className="text-gray-400" />
                          </div>
                        )}
                        <div className="flex-grow">
                          {!user && (
                            <div className="mb-2">
                              <input
                                type="text"
                                value={anonymousName}
                                onChange={(e) => setAnonymousName(e.target.value)}
                                placeholder="Your display name"
                                className="w-full bg-gray-700 text-white rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-700 mb-1 text-sm"
                              />
                              {anonymousNameError && (
                                <p className="text-red-500 text-xs">{anonymousNameError}</p>
                              )}
                            </div>
                          )}
                          <textarea
                            value={replyContent}
                            onChange={(e) => setReplyContent(e.target.value)}
                            placeholder={`Reply to ${comment.userName}...`}
                            className="w-full bg-gray-700 text-white rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-700 resize-none text-sm"
                            rows={2}
                          />
                          <div className="flex justify-end mt-1">
                            <button
                              onClick={() => setReplyingTo(null)}
                              className="text-sm text-gray-400 hover:text-white transition-colors mr-2 px-2 py-1"
                            >
                              Cancel
                            </button>
                            <button
                              onClick={() => handleSubmitReply(comment.id)}
                              disabled={!replyContent.trim()}
                              className="flex items-center text-sm bg-blue-700 hover:bg-blue-800 text-white rounded px-3 py-1 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <Send size={14} className="mr-1" />
                              Reply
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Replies */}
                  {comment.replies && comment.replies.length > 0 && (
                    <div className="mt-3 pl-4 border-l border-gray-700 space-y-3">
                      {comment.replies.map((reply) => (
                        <div key={reply.id} className="pt-3">
                          <div className="flex items-start">
                            <img
                              src={reply.userAvatar || 'https://placehold.co/150/gray/white?text=User'}
                              alt={reply.userName}
                              className="w-8 h-8 rounded-full object-cover mr-2"
                            />
                            <div>
                              <div className="flex items-center">
                                <span className="font-medium text-white text-sm">{reply.userName}</span>
                                <span className="text-gray-400 text-xs ml-2">{formatDate(reply.createdAt)}</span>
                              </div>
                              <p className="text-gray-300 text-sm mt-1">{reply.content}</p>
                              <div className="flex items-center mt-1">
                                <button
                                  onClick={() => handleLikeComment(reply)}
                                  className={`flex items-center text-xs ${
                                    reply.isLiked ? 'text-blue-700' : 'text-gray-400 hover:text-blue-700'
                                  } transition-colors`}
                                  aria-label={reply.isLiked ? 'Unlike reply' : 'Like reply'}
                                >
                                  <ThumbsUp size={12} className="mr-1" />
                                  <span>{reply.likes}</span>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CommentSection;
