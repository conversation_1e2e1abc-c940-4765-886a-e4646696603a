import React from 'react';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  className = '',
}) => {
  // Don't render pagination if there's only one page
  if (totalPages <= 1) {
    return null;
  }

  // Calculate which page numbers to show
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;
    
    if (totalPages <= maxPagesToShow) {
      // If we have fewer pages than the max, show all pages
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always include first page, last page, current page, and pages adjacent to current page
      const firstPage = 1;
      const lastPage = totalPages;
      
      // Add current page and adjacent pages
      for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
        pageNumbers.push(i);
      }
      
      // Add first and last pages
      if (!pageNumbers.includes(firstPage)) {
        pageNumbers.unshift(firstPage);
        
        // Add ellipsis if there's a gap
        if (pageNumbers[1] > firstPage + 1) {
          pageNumbers.splice(1, 0, -1); // -1 represents ellipsis
        }
      }
      
      if (!pageNumbers.includes(lastPage)) {
        pageNumbers.push(lastPage);
        
        // Add ellipsis if there's a gap
        if (pageNumbers[pageNumbers.length - 2] < lastPage - 1) {
          pageNumbers.splice(pageNumbers.length - 1, 0, -1); // -1 represents ellipsis
        }
      }
    }
    
    return pageNumbers;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className={`flex items-center justify-center space-x-1 ${className}`}>
      {/* First page button */}
      <button
        onClick={() => onPageChange(1)}
        disabled={currentPage === 1}
        className={`p-2 rounded-md ${
          currentPage === 1
            ? 'text-gray-500 cursor-not-allowed'
            : 'text-white hover:bg-gray-700'
        }`}
        aria-label="First page"
      >
        <ChevronsLeft size={18} />
      </button>
      
      {/* Previous page button */}
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`p-2 rounded-md ${
          currentPage === 1
            ? 'text-gray-500 cursor-not-allowed'
            : 'text-white hover:bg-gray-700'
        }`}
        aria-label="Previous page"
      >
        <ChevronLeft size={18} />
      </button>
      
      {/* Page numbers */}
      {pageNumbers.map((pageNumber, index) => (
        pageNumber === -1 ? (
          // Render ellipsis
          <span key={`ellipsis-${index}`} className="px-3 py-2 text-gray-500">
            ...
          </span>
        ) : (
          // Render page number
          <button
            key={pageNumber}
            onClick={() => onPageChange(pageNumber)}
            className={`px-3 py-1 rounded-md ${
              currentPage === pageNumber
                ? 'bg-blue-700 text-white'
                : 'text-white hover:bg-gray-700'
            }`}
            aria-label={`Page ${pageNumber}`}
            aria-current={currentPage === pageNumber ? 'page' : undefined}
          >
            {pageNumber}
          </button>
        )
      ))}
      
      {/* Next page button */}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`p-2 rounded-md ${
          currentPage === totalPages
            ? 'text-gray-500 cursor-not-allowed'
            : 'text-white hover:bg-gray-700'
        }`}
        aria-label="Next page"
      >
        <ChevronRight size={18} />
      </button>
      
      {/* Last page button */}
      <button
        onClick={() => onPageChange(totalPages)}
        disabled={currentPage === totalPages}
        className={`p-2 rounded-md ${
          currentPage === totalPages
            ? 'text-gray-500 cursor-not-allowed'
            : 'text-white hover:bg-gray-700'
        }`}
        aria-label="Last page"
      >
        <ChevronsRight size={18} />
      </button>
    </div>
  );
};

export default Pagination;
