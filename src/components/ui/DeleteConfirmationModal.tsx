import React from 'react';
import { X, AlertTriangle, AlertCircle } from 'lucide-react';
import Button from './Button';

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  isLoading?: boolean;
  error?: string | null;
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  isLoading = false,
  error = null
}) => {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70">
      <div className="bg-gray-800 rounded-lg w-full max-w-md">
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-bold text-white flex items-center">
            <AlertTriangle size={20} className="text-red-500 mr-2" />
            {title}
          </h2>
          <button
            className="p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors"
            onClick={onClose}
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>
        
        <div className="p-4">
          <p className="text-gray-300 mb-4">{message}</p>
          
          {error && (
            <div className="bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start mb-4">
              <AlertCircle size={20} className="text-red-500 mr-2 flex-shrink-0 mt-0.5" />
              <p className="text-red-200">{error}</p>
            </div>
          )}
          
          <div className="flex justify-end space-x-3">
            <Button
              variant="ghost"
              type="button"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              type="button"
              onClick={onConfirm}
              isLoading={isLoading}
              disabled={isLoading}
            >
              Delete
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;
