import React, { useState } from 'react';
import { Heart } from 'lucide-react';
import { useFavoriteStore } from '../../stores/favoriteStore';
import { useAuthStore } from '../../stores/authStore';

interface FavoriteButtonProps {
  videoId: string;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

const FavoriteButton: React.FC<FavoriteButtonProps> = ({
  videoId,
  size = 'md',
  showText = true,
  className = ''
}) => {
  const { user } = useAuthStore();
  const { isFavorite, addToFavorites, removeFromFavorites } = useFavoriteStore();
  const [isProcessing, setIsProcessing] = useState(false);

  const isFav = isFavorite(videoId);

  const iconSize = size === 'sm' ? 16 : size === 'md' ? 20 : 24;
  const paddingClass = size === 'sm' ? 'p-1.5' : size === 'md' ? 'p-2' : 'p-3';

  const handleToggleFavorite = async () => {
    if (!user) {
      // Show login prompt or alert
      alert('You need to be logged in to add videos to favorites');
      return;
    }

    if (isProcessing) return;

    setIsProcessing(true);

    try {
      if (isFav) {
        await removeFromFavorites(videoId);
      } else {
        await addToFavorites(videoId);
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <button
      className={`flex items-center rounded-full ${paddingClass} ${
        isFav
          ? 'bg-blue-700 text-white hover:bg-blue-800'
          : 'bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white'
      } transition-colors ${isProcessing ? 'opacity-70 cursor-wait' : ''} ${className}`}
      onClick={handleToggleFavorite}
      disabled={isProcessing || !user}
      aria-label={isFav ? 'Remove from favorites' : 'Add to favorites'}
    >
      <Heart
        size={iconSize}
        className={isFav ? 'fill-current' : ''}
      />
      {showText && (
        <span className="ml-2">
          {isFav ? 'Favorited' : 'Favorite'}
        </span>
      )}
    </button>
  );
};

export default FavoriteButton;
