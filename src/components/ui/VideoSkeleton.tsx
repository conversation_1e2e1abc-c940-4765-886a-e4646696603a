import React from 'react';

interface VideoSkeletonProps {
  compact?: boolean;
}

const VideoSkeleton: React.FC<VideoSkeletonProps> = ({ compact = false }) => {
  return (
    <div className={`bg-gray-800 rounded-lg overflow-hidden shadow-lg animate-pulse ${
      compact ? 'w-[280px]' : 'w-full'
    }`}>
      <div className="aspect-video bg-gray-700"></div>
      <div className="p-3">
        <div className="flex items-start">
          <div className="w-9 h-9 rounded-full bg-gray-700 mr-3"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoSkeleton;
