import React from 'react';

const HeroSkeleton: React.FC = () => {
  return (
    <div className="relative w-full h-[500px] bg-gray-800 rounded-xl overflow-hidden animate-pulse">
      <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent"></div>
      <div className="absolute bottom-0 left-0 right-0 p-6 md:p-10">
        <div className="flex space-x-2 mb-3">
          <div className="h-5 bg-gray-700 rounded w-20"></div>
          <div className="h-5 bg-gray-700 rounded w-12"></div>
        </div>
        <div className="h-8 bg-gray-700 rounded w-3/4 mb-3"></div>
        <div className="h-8 bg-gray-700 rounded w-1/2 mb-3"></div>
        <div className="h-4 bg-gray-700 rounded w-full max-w-2xl mb-6"></div>
        <div className="h-4 bg-gray-700 rounded w-3/4 max-w-2xl mb-6"></div>
        <div className="flex items-center space-x-2 mb-6">
          <div className="h-4 bg-gray-700 rounded w-32"></div>
          <div className="h-4 bg-gray-700 rounded w-20"></div>
        </div>
        <div className="h-10 bg-gray-700 rounded w-32"></div>
      </div>
    </div>
  );
};

export default HeroSkeleton;
