import React from 'react';
import { Home, Upload, User, Video, Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';

interface MobileNavProps {
  activeTab?: 'home' | 'search' | 'upload' | 'profile';
  onTabChange?: (tab: 'home' | 'search' | 'upload' | 'profile') => void;
}

const MobileNav: React.FC<MobileNavProps> = ({
  activeTab = 'home',
  onTabChange
}) => {
  const navigate = useNavigate();
  const { user } = useAuthStore();

  const handleTabClick = (tab: 'home' | 'search' | 'upload' | 'profile') => {
    if (onTabChange) {
      onTabChange(tab);
    }

    // Navigate to appropriate routes
    if (tab === 'home') {
      navigate('/', { replace: true });
    } else if (tab === 'search') {
      navigate('/search');
    } else if (tab === 'upload') {
      navigate('/upload');
    } else if (tab === 'profile' && user) {
      navigate('/manage');
    } else if (tab === 'profile') {
      // If no user, show login
      navigate('/search');
    }
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-800 lg:hidden z-40">
      <nav className="flex justify-around">
        <button
          onClick={() => handleTabClick('home')}
          className={`flex flex-col items-center justify-center py-2 flex-1 transition-colors
            ${activeTab === 'home' ? 'text-blue-700' : 'text-gray-400 hover:text-white'}`}
        >
          <Home size={20} />
          <span className="text-xs mt-1">Home</span>
        </button>

        <button
          onClick={() => handleTabClick('search')}
          className={`flex flex-col items-center justify-center py-2 flex-1 transition-colors
            ${activeTab === 'search' ? 'text-blue-700' : 'text-gray-400 hover:text-white'}`}
        >
          <Search size={20} />
          <span className="text-xs mt-1">Search</span>
        </button>

        {user && (
          <button
            onClick={() => handleTabClick('upload')}
            className={`flex flex-col items-center justify-center py-2 flex-1 transition-colors
              ${activeTab === 'upload' ? 'text-blue-700' : 'text-gray-400 hover:text-white'}`}
          >
            <Upload size={20} />
            <span className="text-xs mt-1">Upload</span>
          </button>
        )}

        <button
          onClick={() => handleTabClick('profile')}
          className={`flex flex-col items-center justify-center py-2 flex-1 transition-colors
            ${activeTab === 'profile' ? 'text-blue-700' : 'text-gray-400 hover:text-white'}`}
        >
          {user ? (
            <>
              <Video size={20} />
              <span className="text-xs mt-1">My Videos</span>
            </>
          ) : (
            <>
              <User size={20} />
              <span className="text-xs mt-1">Profile</span>
            </>
          )}
        </button>
      </nav>
    </div>
  );
};

export default MobileNav;