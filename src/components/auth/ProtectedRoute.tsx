import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireApproval?: boolean;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requireApproval = false,
  redirectTo = '/'
}) => {
  const navigate = useNavigate();
  const { user, isApproved, isLoading } = useAuthStore();

  useEffect(() => {
    // Don't redirect while still loading
    if (isLoading) return;

    // Check authentication requirement
    if (requireAuth && !user) {
      console.log('User not authenticated, redirecting to:', redirectTo);
      navigate(redirectTo);
      return;
    }

    // Check approval requirement
    if (requireApproval && user && !isApproved) {
      console.log('User not approved, redirecting to:', redirectTo);
      navigate(redirectTo);
      return;
    }
  }, [user, isApproved, isLoading, requireAuth, requireApproval, redirectTo, navigate]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  // Don't render children if requirements aren't met
  if (requireAuth && !user) {
    return null;
  }

  if (requireApproval && user && !isApproved) {
    return null;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
