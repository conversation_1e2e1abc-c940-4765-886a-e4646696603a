/**
 * Media Performance Monitor - Tracks image and video loading performance
 */

import React, { useEffect, useState, useCallback } from 'react';
import { getImageCacheStats } from '../../utils/imageOptimization';

interface MediaMetrics {
  imagesLoaded: number;
  imagesError: number;
  averageLoadTime: number;
  totalDataTransferred: number;
  cacheHitRate: number;
  slowLoadingImages: number;
}

interface MediaPerformanceMonitorProps {
  visible?: boolean;
  onMetricsUpdate?: (metrics: MediaMetrics) => void;
}

const MediaPerformanceMonitor: React.FC<MediaPerformanceMonitorProps> = ({
  visible = false,
  onMetricsUpdate
}) => {
  const [metrics, setMetrics] = useState<MediaMetrics>({
    imagesLoaded: 0,
    imagesError: 0,
    averageLoadTime: 0,
    totalDataTransferred: 0,
    cacheHitRate: 0,
    slowLoadingImages: 0
  });

  const [loadTimes, setLoadTimes] = useState<number[]>([]);
  const [observer, setObserver] = useState<PerformanceObserver | null>(null);

  // Monitor resource loading performance
  const monitorResourcePerformance = useCallback(() => {
    if (!window.PerformanceObserver) return;

    const resourceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        const resourceEntry = entry as PerformanceResourceTiming;
        
        // Only track image resources
        if (resourceEntry.initiatorType === 'img' || 
            resourceEntry.name.match(/\.(jpg|jpeg|png|gif|webp|avif|svg)(\?|$)/i)) {
          
          const loadTime = resourceEntry.responseEnd - resourceEntry.requestStart;
          const transferSize = resourceEntry.transferSize || 0;
          
          setLoadTimes(prev => {
            const newTimes = [...prev, loadTime];
            // Keep only last 100 measurements
            return newTimes.slice(-100);
          });

          setMetrics(prev => {
            const newImagesLoaded = prev.imagesLoaded + 1;
            const newTotalData = prev.totalDataTransferred + transferSize;
            const newSlowLoading = loadTime > 3000 ? prev.slowLoadingImages + 1 : prev.slowLoadingImages;
            
            return {
              ...prev,
              imagesLoaded: newImagesLoaded,
              totalDataTransferred: newTotalData,
              slowLoadingImages: newSlowLoading
            };
          });
        }
      });
    });

    resourceObserver.observe({ entryTypes: ['resource'] });
    setObserver(resourceObserver);

    return () => {
      resourceObserver.disconnect();
    };
  }, []);

  // Calculate average load time
  useEffect(() => {
    if (loadTimes.length > 0) {
      const average = loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length;
      setMetrics(prev => ({ ...prev, averageLoadTime: average }));
    }
  }, [loadTimes]);

  // Monitor image cache performance
  useEffect(() => {
    const interval = setInterval(() => {
      const cacheStats = getImageCacheStats();
      const hitRate = cacheStats.size > 0 ? (cacheStats.size / (metrics.imagesLoaded || 1)) * 100 : 0;
      
      setMetrics(prev => ({ ...prev, cacheHitRate: Math.min(hitRate, 100) }));
    }, 5000);

    return () => clearInterval(interval);
  }, [metrics.imagesLoaded]);

  // Set up performance monitoring
  useEffect(() => {
    if (!visible) return;

    const cleanup = monitorResourcePerformance();

    // Monitor image error events
    const handleImageError = () => {
      setMetrics(prev => ({ ...prev, imagesError: prev.imagesError + 1 }));
    };

    // Listen for global image errors
    document.addEventListener('error', (e) => {
      if (e.target instanceof HTMLImageElement) {
        handleImageError();
      }
    }, true);

    return () => {
      if (cleanup) cleanup();
      if (observer) observer.disconnect();
      document.removeEventListener('error', handleImageError, true);
    };
  }, [visible, monitorResourcePerformance, observer]);

  // Notify parent of metrics updates
  useEffect(() => {
    if (onMetricsUpdate) {
      onMetricsUpdate(metrics);
    }
  }, [metrics, onMetricsUpdate]);

  if (!visible) return null;

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number): string => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const getPerformanceColor = (value: number, thresholds: { good: number; poor: number }): string => {
    if (value <= thresholds.good) return 'text-green-400';
    if (value <= thresholds.poor) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className="fixed bottom-4 right-4 bg-black/90 text-white p-4 rounded-lg shadow-lg text-xs font-mono z-50 max-w-sm">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-bold text-sm">Media Performance</h3>
        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>Images Loaded:</span>
          <span className="text-blue-400">{metrics.imagesLoaded}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Load Errors:</span>
          <span className={metrics.imagesError > 0 ? 'text-red-400' : 'text-green-400'}>
            {metrics.imagesError}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Avg Load Time:</span>
          <span className={getPerformanceColor(metrics.averageLoadTime, { good: 1000, poor: 3000 })}>
            {formatTime(metrics.averageLoadTime)}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Data Transfer:</span>
          <span className="text-purple-400">{formatBytes(metrics.totalDataTransferred)}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Cache Hit Rate:</span>
          <span className={getPerformanceColor(100 - metrics.cacheHitRate, { good: 20, poor: 50 })}>
            {metrics.cacheHitRate.toFixed(1)}%
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Slow Images:</span>
          <span className={metrics.slowLoadingImages > 0 ? 'text-red-400' : 'text-green-400'}>
            {metrics.slowLoadingImages}
          </span>
        </div>
      </div>

      {/* Performance indicators */}
      <div className="mt-3 pt-2 border-t border-gray-600">
        <div className="flex items-center space-x-2 text-xs">
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${
              metrics.averageLoadTime < 1000 ? 'bg-green-400' : 
              metrics.averageLoadTime < 3000 ? 'bg-yellow-400' : 'bg-red-400'
            }`}></div>
            <span>Speed</span>
          </div>
          
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${
              metrics.imagesError === 0 ? 'bg-green-400' : 'bg-red-400'
            }`}></div>
            <span>Reliability</span>
          </div>
          
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${
              metrics.cacheHitRate > 80 ? 'bg-green-400' : 
              metrics.cacheHitRate > 50 ? 'bg-yellow-400' : 'bg-red-400'
            }`}></div>
            <span>Cache</span>
          </div>
        </div>
      </div>

      {/* Connection info if available */}
      {(navigator as any).connection && (
        <div className="mt-2 pt-2 border-t border-gray-600 text-xs">
          <div className="flex justify-between">
            <span>Connection:</span>
            <span className="text-cyan-400">
              {(navigator as any).connection.effectiveType?.toUpperCase() || 'Unknown'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaPerformanceMonitor;
