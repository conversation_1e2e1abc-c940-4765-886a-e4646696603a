import React, { useState, useEffect, useCallback, memo } from 'react';

interface PerformanceMetrics {
  fcp: number | null;
  lcp: number | null;
  cls: number | null;
  fid: number | null;
  ttfb: number | null;
  jsHeapSize: number | null;
  domNodes: number | null;
  resourceCount: number | null;
  resourceSize: number | null;
}

interface PerformanceMonitorProps {
  visible?: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ 
  visible = false,
  position = 'bottom-right'
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fcp: null,
    lcp: null,
    cls: null,
    fid: null,
    ttfb: null,
    jsHeapSize: null,
    domNodes: null,
    resourceCount: null,
    resourceSize: null
  });
  const [isExpanded, setIsExpanded] = useState(false);

  // Format bytes to human-readable format
  const formatBytes = (bytes: number | null): string => {
    if (bytes === null) return 'N/A';
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format time in ms to human-readable format
  const formatTime = (time: number | null): string => {
    if (time === null) return 'N/A';
    return `${time.toFixed(2)} ms`;
  };

  // Get position classes based on the position prop
  const getPositionClasses = (): string => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
      default:
        return 'bottom-4 right-4';
    }
  };

  // Measure First Contentful Paint (FCP)
  const measureFCP = useCallback(() => {
    if (!window.performance || !window.performance.getEntriesByType) return;
    
    const paintEntries = performance.getEntriesByType('paint');
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    
    if (fcpEntry) {
      setMetrics(prev => ({ ...prev, fcp: fcpEntry.startTime }));
    }
  }, []);

  // Measure Largest Contentful Paint (LCP)
  const measureLCP = useCallback(() => {
    if (!window.PerformanceObserver) return;
    
    try {
      const lcpObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        if (lastEntry) {
          setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }));
        }
      });
      
      lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
      
      return () => {
        lcpObserver.disconnect();
      };
    } catch (e) {
      console.error('LCP measurement error:', e);
    }
  }, []);

  // Measure Cumulative Layout Shift (CLS)
  const measureCLS = useCallback(() => {
    if (!window.PerformanceObserver) return;
    
    try {
      let clsValue = 0;
      
      const clsObserver = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
            setMetrics(prev => ({ ...prev, cls: clsValue }));
          }
        }
      });
      
      clsObserver.observe({ type: 'layout-shift', buffered: true });
      
      return () => {
        clsObserver.disconnect();
      };
    } catch (e) {
      console.error('CLS measurement error:', e);
    }
  }, []);

  // Measure First Input Delay (FID)
  const measureFID = useCallback(() => {
    if (!window.PerformanceObserver) return;
    
    try {
      const fidObserver = new PerformanceObserver((entryList) => {
        const firstInput = entryList.getEntries()[0];
        if (firstInput) {
          const delay = firstInput.processingStart - firstInput.startTime;
          setMetrics(prev => ({ ...prev, fid: delay }));
        }
      });
      
      fidObserver.observe({ type: 'first-input', buffered: true });
      
      return () => {
        fidObserver.disconnect();
      };
    } catch (e) {
      console.error('FID measurement error:', e);
    }
  }, []);

  // Measure Time to First Byte (TTFB)
  const measureTTFB = useCallback(() => {
    if (!window.performance || !window.performance.getEntriesByType) return;
    
    const navEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navEntry) {
      setMetrics(prev => ({ ...prev, ttfb: navEntry.responseStart }));
    }
  }, []);

  // Measure memory usage
  const measureMemory = useCallback(() => {
    if (!(performance as any).memory) return;
    
    setMetrics(prev => ({ 
      ...prev, 
      jsHeapSize: (performance as any).memory.usedJSHeapSize 
    }));
  }, []);

  // Count DOM nodes
  const countDOMNodes = useCallback(() => {
    const nodeCount = document.querySelectorAll('*').length;
    setMetrics(prev => ({ ...prev, domNodes: nodeCount }));
  }, []);

  // Measure resource usage
  const measureResources = useCallback(() => {
    if (!window.performance || !window.performance.getEntriesByType) return;
    
    const resources = performance.getEntriesByType('resource');
    let totalSize = 0;
    
    // Calculate total size if available
    resources.forEach(resource => {
      if ((resource as any).transferSize) {
        totalSize += (resource as any).transferSize;
      }
    });
    
    setMetrics(prev => ({ 
      ...prev, 
      resourceCount: resources.length,
      resourceSize: totalSize
    }));
  }, []);

  // Set up performance measurements
  useEffect(() => {
    if (!visible) return;
    
    // Initial measurements
    measureFCP();
    measureTTFB();
    countDOMNodes();
    
    // Set up observers
    const lcpCleanup = measureLCP();
    const clsCleanup = measureCLS();
    const fidCleanup = measureFID();
    
    // Periodic measurements
    const intervalId = setInterval(() => {
      measureMemory();
      countDOMNodes();
      measureResources();
    }, 2000);
    
    // Clean up
    return () => {
      if (lcpCleanup) lcpCleanup();
      if (clsCleanup) clsCleanup();
      if (fidCleanup) fidCleanup();
      clearInterval(intervalId);
    };
  }, [visible, measureFCP, measureLCP, measureCLS, measureFID, measureTTFB, measureMemory, countDOMNodes, measureResources]);

  if (!visible) return null;

  return (
    <div 
      className={`fixed ${getPositionClasses()} z-50 bg-gray-900 border border-blue-700 rounded-lg shadow-lg overflow-hidden transition-all duration-300 ${isExpanded ? 'w-80' : 'w-10'}`}
    >
      <button 
        className="absolute top-2 right-2 text-blue-500 hover:text-blue-400 focus:outline-none"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {isExpanded ? '×' : '≡'}
      </button>
      
      {isExpanded && (
        <div className="p-4">
          <h3 className="text-white text-sm font-bold mb-2">Performance Metrics</h3>
          
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-400">FCP:</span>
              <span className={`font-mono ${metrics.fcp && metrics.fcp < 1000 ? 'text-green-400' : 'text-yellow-400'}`}>
                {formatTime(metrics.fcp)}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-400">LCP:</span>
              <span className={`font-mono ${metrics.lcp && metrics.lcp < 2500 ? 'text-green-400' : 'text-yellow-400'}`}>
                {formatTime(metrics.lcp)}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-400">CLS:</span>
              <span className={`font-mono ${metrics.cls && metrics.cls < 0.1 ? 'text-green-400' : 'text-yellow-400'}`}>
                {metrics.cls !== null ? metrics.cls.toFixed(3) : 'N/A'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-400">FID:</span>
              <span className={`font-mono ${metrics.fid && metrics.fid < 100 ? 'text-green-400' : 'text-yellow-400'}`}>
                {formatTime(metrics.fid)}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-400">TTFB:</span>
              <span className={`font-mono ${metrics.ttfb && metrics.ttfb < 200 ? 'text-green-400' : 'text-yellow-400'}`}>
                {formatTime(metrics.ttfb)}
              </span>
            </div>
            
            <div className="border-t border-gray-700 my-2"></div>
            
            <div className="flex justify-between">
              <span className="text-gray-400">JS Heap:</span>
              <span className="font-mono text-blue-400">{formatBytes(metrics.jsHeapSize)}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-400">DOM Nodes:</span>
              <span className={`font-mono ${metrics.domNodes && metrics.domNodes < 1500 ? 'text-green-400' : 'text-yellow-400'}`}>
                {metrics.domNodes !== null ? metrics.domNodes : 'N/A'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-400">Resources:</span>
              <span className="font-mono text-blue-400">
                {metrics.resourceCount !== null ? `${metrics.resourceCount} (${formatBytes(metrics.resourceSize)})` : 'N/A'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default memo(PerformanceMonitor);
