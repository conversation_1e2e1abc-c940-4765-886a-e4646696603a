# Namecheap Hosting Deployment Guide

## Overview
This guide will help you deploy your React/Vite project from Vercel to Namecheap shared hosting.

## Prerequisites
- Namecheap hosting account with cPanel access
- Domain connected to your Namecheap hosting
- Local development environment with Node.js

## Step 1: Prepare Your Project for Deployment

### 1.1 Build the Project Locally
```bash
# Install dependencies (if not already installed)
npm install

# Create production build
npm run build
```

This will create a `dist/` folder with all your static files.

### 1.2 Verify Build Output
Check that the `dist/` folder contains:
- `index.html`
- `assets/` folder with CSS and JS files
- Any other static assets

## Step 2: Prepare Files for Upload

### 2.1 Create Deployment Package
1. Navigate to your project's `dist/` folder
2. Select ALL files and folders inside `dist/`
3. Create a ZIP archive of these files
4. Name it something like `website-deployment.zip`

**Important**: Do NOT zip the `dist/` folder itself, only its contents.

## Step 3: Upload to Namecheap cPanel

### 3.1 Access cPanel File Manager
1. Log into your Namecheap account
2. Go to cPanel
3. Open "File Manager"

### 3.2 Navigate to Website Root
- For main domain: Go to `public_html/` folder
- For subdomain: Go to the subdomain's root folder

### 3.3 Upload and Extract Files
1. Click "Upload" in the top menu
2. Select your `website-deployment.zip` file
3. Wait for upload to complete
4. Go back to the file manager
5. Right-click the uploaded ZIP file
6. Select "Extract"
7. Extract to the current directory
8. Delete the ZIP file after extraction

## Step 4: Configure .htaccess for React Router

### 4.1 Create .htaccess File
1. In cPanel File Manager, enable "Show Hidden Files" in Settings
2. Click "+ File" to create a new file
3. Name it `.htaccess`
4. Edit the file and add the following content:

```apache
RewriteEngine On
RewriteBase /
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-l
RewriteRule . /index.html [L]

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
```

## Step 5: Environment Variables (If Needed)

Your Supabase configuration should work as-is since you're using environment variables that are built into the static files. However, if you need to update them:

### 5.1 Check Current Environment Variables
Your project uses:
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_ANON_KEY`

### 5.2 Update if Necessary
If you need different environment variables for production:
1. Update your `.env.production` file locally
2. Rebuild the project: `npm run build`
3. Re-upload the new build files

## Step 6: Test Your Deployment

### 6.1 Basic Functionality Test
1. Visit your domain in a web browser
2. Check that the homepage loads correctly
3. Test navigation between pages
4. Verify that videos load properly
5. Test search functionality

### 6.2 Check for Common Issues
- **404 errors on page refresh**: Ensure .htaccess is configured correctly
- **Missing assets**: Check that all files from `dist/` were uploaded
- **API errors**: Verify Supabase connection is working

## Step 7: Performance Optimization

### 7.1 Enable Gzip Compression
The .htaccess file above includes compression rules.

### 7.2 Set Up CDN (Optional)
Consider using Namecheap's CDN service for better performance.

## Troubleshooting

### Common Issues and Solutions

1. **Page shows "Index of /" instead of your app**
   - Ensure `index.html` is in the root directory
   - Check file permissions

2. **404 errors on direct page access**
   - Verify .htaccess file is present and correctly configured
   - Check that mod_rewrite is enabled (usually is on shared hosting)

3. **Assets not loading**
   - Check that all files from `dist/assets/` were uploaded
   - Verify file paths in the browser developer tools

4. **Supabase connection issues**
   - Check browser console for errors
   - Verify environment variables are correctly built into the app

## Maintenance

### Updating Your Site
1. Make changes locally
2. Run `npm run build`
3. Upload new files to replace old ones
4. Clear any caches

### Backup
- Regularly backup your website files
- Keep a local copy of your latest build

## Support
- Namecheap Support: Available 24/7 via live chat
- Check Namecheap's knowledge base for hosting-specific issues
