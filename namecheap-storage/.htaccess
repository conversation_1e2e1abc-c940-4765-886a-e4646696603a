# Media Storage .htaccess Configuration
# Security and optimization for video/image serving

# Prevent access to PHP files in this directory (except upload.php and health.php)
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "upload.php">
    Order Allow,<PERSON>y
    Allow from all
</Files>

<Files "health.php">
    Order Allow,<PERSON>y
    Allow from all
</Files>

# Security headers for media files
<IfModule mod_headers.c>
    # Prevent hotlinking (optional - uncomment and set your domain)
    # SetEnvIf Referer "^https?://yourdomain\.com/" local_referer
    # Header set X-Frame-Options "SAMEORIGIN" env=local_referer
    
    # Security headers for all files
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
    
    # CORS headers for media files
    <FilesMatch "\.(mp4|mov|avi|mkv|webm|jpg|jpeg|png|webp|gif)$">
        Header set Access-Control-Allow-Origin "*"
        Header set Access-Control-Allow-Methods "GET, HEAD, OPTIONS"
        Header set Access-Control-Allow-Headers "Range"
    </FilesMatch>
</IfModule>

# Enable compression for media files
<IfModule mod_deflate.c>
    # Don't compress already compressed files
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|mp4|mov|avi|mkv|webm)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
</IfModule>

# Cache headers for media files
<IfModule mod_expires.c>
    ExpiresActive on
    
    # Videos - cache for 1 week
    ExpiresByType video/mp4 "access plus 1 week"
    ExpiresByType video/quicktime "access plus 1 week"
    ExpiresByType video/x-msvideo "access plus 1 week"
    ExpiresByType video/webm "access plus 1 week"
    
    # Images - cache for 1 month
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>

# Enable range requests for video streaming
<IfModule mod_headers.c>
    <FilesMatch "\.(mp4|mov|avi|mkv|webm)$">
        Header set Accept-Ranges bytes
    </FilesMatch>
</IfModule>

# File size limits
<IfModule mod_php.c>
    php_value upload_max_filesize 100M
    php_value post_max_size 100M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M
</IfModule>

# Prevent directory browsing
Options -Indexes

# Custom error pages
ErrorDocument 403 "Access Forbidden"
ErrorDocument 404 "File Not Found"

# Rewrite rules for clean URLs (if needed)
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Prevent access to sensitive files
    RewriteRule ^\.htaccess$ - [F,L]
    RewriteRule ^\.env$ - [F,L]
    
    # Optional: Redirect old Supabase URLs to new structure
    # RewriteRule ^supabase-migration/(.*)$ /$1 [R=301,L]
</IfModule>
