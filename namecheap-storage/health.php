<?php
/**
 * Health Check Endpoint for Storage Service
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    // Check if upload directories exist and are writable
    $uploadDir = '../media/';
    $videosDir = $uploadDir . 'videos/';
    $thumbnailsDir = $uploadDir . 'thumbnails/';
    
    $checks = [
        'upload_dir_exists' => is_dir($uploadDir),
        'upload_dir_writable' => is_writable($uploadDir),
        'videos_dir_exists' => is_dir($videosDir),
        'videos_dir_writable' => is_writable($videosDir),
        'thumbnails_dir_exists' => is_dir($thumbnailsDir),
        'thumbnails_dir_writable' => is_writable($thumbnailsDir),
        'php_version' => PHP_VERSION,
        'max_file_size' => ini_get('upload_max_filesize'),
        'max_post_size' => ini_get('post_max_size'),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
    ];
    
    // Check if all critical checks pass
    $healthy = $checks['upload_dir_exists'] && 
               $checks['upload_dir_writable'] && 
               $checks['videos_dir_exists'] && 
               $checks['videos_dir_writable'] && 
               $checks['thumbnails_dir_exists'] && 
               $checks['thumbnails_dir_writable'];
    
    $response = [
        'status' => $healthy ? 'healthy' : 'unhealthy',
        'timestamp' => date('c'),
        'checks' => $checks
    ];
    
    http_response_code($healthy ? 200 : 503);
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'error' => $e->getMessage(),
        'timestamp' => date('c')
    ]);
}
?>
