# 🚀 BlueFilmX API Deployment Guide

## Current Status
Your website is showing a 404 error because the PHP API endpoints are not deployed to your server yet. This guide will fix that issue by deploying your MySQL API.

## 📦 What's Ready
- ✅ `api-deployment.zip` - Ready to upload (22KB)
- ✅ `scripts/create-mysql-schema.sql` - Database schema
- ✅ `mysql-import/import-data.sql` - Your video data (248 videos)
- ✅ Database configuration in `namecheap-api/config/database.php`

## 🎯 Step 1: Upload API Files

### 1.1 Access cPanel
1. Log into your Namecheap hosting account
2. Go to cPanel
3. Open **File Manager**

### 1.2 Upload API Package
1. Navigate to `public_html/`
2. Create a new folder called `api` (if it doesn't exist)
3. Enter the `api` folder
4. Upload `api-deployment.zip`
5. Right-click the ZIP file → **Extract**
6. Extract to current directory
7. Delete the ZIP file after extraction

### 1.3 Verify Upload
Your `public_html/api/` folder should now contain:
```
api/
├── videos.php
├── auth.php
├── categories.php
├── upload.php
├── .htaccess
└── config/
    └── database.php
```

## 🗄️ Step 2: Set Up MySQL Database

### 2.1 Create Database
1. In cPanel, go to **MySQL Databases**
2. Create a new database:
   - Database name: `bluerpcm_bluefilmx`
   - Character set: `utf8mb4`
   - Collation: `utf8mb4_unicode_ci`

### 2.2 Create Database User
1. Create a new user:
   - Username: `bluerpcm_dbuser`
   - Password: `kingpatrick100` (or generate a new strong password)
2. Add user to database with **ALL PRIVILEGES**

### 2.3 Import Database Schema
1. In cPanel, go to **phpMyAdmin**
2. Select your database `bluerpcm_bluefilmx`
3. Click **Import** tab
4. Upload `scripts/create-mysql-schema.sql`
5. Click **Go** to execute

### 2.4 Import Your Data
1. Still in phpMyAdmin
2. Click **Import** tab again
3. Upload `mysql-import/import-data.sql`
4. Click **Go** to execute

This will import your 248 videos and all other data.

## 🔧 Step 3: Configure Database Connection

### 3.1 Update Database Credentials
1. In File Manager, navigate to `public_html/api/config/`
2. Edit `database.php`
3. Update these lines if needed:
```php
private $db_name = 'bluerpcm_bluefilmx';     // Your database name
private $username = 'bluerpcm_dbuser';       // Your database username  
private $password = 'kingpatrick100';        // Your database password
```

## 🧪 Step 4: Test API Endpoints

### 4.1 Test Videos API
Open in browser: `https://www.bluefilmx.com/api/videos.php?limit=5`

You should see JSON response with your videos.

### 4.2 Test Categories API
Open in browser: `https://www.bluefilmx.com/api/categories.php`

You should see your categories (Hot, Trending, New).

## 🌐 Step 5: Update Frontend

Once the API is working, your website should automatically start loading videos since it's already configured to use the MySQL API.

## 🔍 Troubleshooting

### API Returns 404
- Check that files are in `public_html/api/` not `public_html/api/api/`
- Verify .htaccess file is present

### Database Connection Error
- Check database credentials in `config/database.php`
- Verify database user has proper privileges
- Check database name matches exactly

### No Videos Loading
- Check phpMyAdmin to verify data was imported
- Look at browser console for error messages
- Test API endpoints directly in browser

## 📞 Need Help?
If you encounter issues:
1. Check browser console for errors
2. Test API endpoints directly
3. Verify database connection in phpMyAdmin
4. Check file permissions (should be 644 for .php files)

## 🎉 Success!
Once complete, your website will:
- ✅ Load videos from your MySQL database
- ✅ Display all 248 imported videos
- ✅ Support categories, search, and pagination
- ✅ Work exactly like before but faster and more reliable
