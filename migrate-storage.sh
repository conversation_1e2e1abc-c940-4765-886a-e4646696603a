#!/bin/bash

# Storage Migration Script: Supabase → Namecheap
# This script will migrate all videos and thumbnails from Supabase to Namecheap hosting

echo "🚀 Starting Storage Migration: Supabase → Namecheap"
echo "=================================================="

# Configuration
SUPABASE_URL="https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public"
NAMECHEAP_HOST="**************"
NAMECHEAP_USER="bluerpcm"
NAMECHEAP_PORT="21098"
SSH_KEY="~/.ssh/namecheap_rsa"

# Create temporary directory for downloads
TEMP_DIR="./temp-migration"
mkdir -p "$TEMP_DIR/videos"
mkdir -p "$TEMP_DIR/thumbnails"

echo "📁 Created temporary directories"

# Function to download file from Supabase
download_from_supabase() {
    local bucket=$1
    local filename=$2
    local local_path=$3
    
    echo "⬇️  Downloading $bucket/$filename..."
    curl -s -o "$local_path" "$SUPABASE_URL/$bucket/$filename"
    
    if [ -f "$local_path" ] && [ -s "$local_path" ]; then
        echo "✅ Downloaded: $filename"
        return 0
    else
        echo "❌ Failed to download: $filename"
        return 1
    fi
}

# Function to upload file to Namecheap
upload_to_namecheap() {
    local local_path=$1
    local remote_path=$2
    
    echo "⬆️  Uploading to Namecheap: $remote_path"
    scp -P "$NAMECHEAP_PORT" -i "$SSH_KEY" "$local_path" "$NAMECHEAP_USER@$NAMECHEAP_HOST:public_html/$remote_path"
    
    if [ $? -eq 0 ]; then
        echo "✅ Uploaded: $remote_path"
        return 0
    else
        echo "❌ Failed to upload: $remote_path"
        return 1
    fi
}

# Function to create directory structure on Namecheap
create_remote_dirs() {
    echo "📁 Creating directory structure on Namecheap..."
    ssh -p "$NAMECHEAP_PORT" -i "$SSH_KEY" "$NAMECHEAP_USER@$NAMECHEAP_HOST" "
        mkdir -p public_html/media/videos
        mkdir -p public_html/media/thumbnails
        chmod 755 public_html/media/videos
        chmod 755 public_html/media/thumbnails
    "
    echo "✅ Directory structure created"
}

# Create directory structure
create_remote_dirs

echo ""
echo "🔍 Starting file migration..."
echo "This script will help you migrate files manually."
echo "Please provide the list of files to migrate."
echo ""

# Cleanup function
cleanup() {
    echo "🧹 Cleaning up temporary files..."
    rm -rf "$TEMP_DIR"
    echo "✅ Cleanup complete"
}

# Set trap for cleanup on exit
trap cleanup EXIT

echo "📋 Migration script ready!"
echo "To migrate a file, use these functions:"
echo "  download_from_supabase <bucket> <filename> <local_path>"
echo "  upload_to_namecheap <local_path> <remote_path>"
echo ""
echo "Example:"
echo "  download_from_supabase videos video.mp4 ./temp-migration/videos/video.mp4"
echo "  upload_to_namecheap ./temp-migration/videos/video.mp4 media/videos/video.mp4"
