#!/bin/bash

# This script applies the SQL migrations to your Supabase project
# Make sure you have the Supabase CLI installed and are logged in

# Set these variables to match your Supabase project
PROJECT_ID="your-project-id"

# Apply migrations
echo "Applying migrations to Supabase project..."
supabase db push --db-url "postgresql://postgres:postgres@localhost:54322/postgres"

echo "Migrations applied successfully!"
