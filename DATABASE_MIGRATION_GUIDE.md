# 🚀 Database Migration Guide: Supabase → Namecheap MySQL

This guide will help you migrate your BlueFilmX database from Supabase to your Namecheap hosting MySQL database.

## 📊 Migration Status

✅ **Completed Steps:**
- [x] Exported all Supabase data (254 records from 8 tables)
- [x] Created MySQL schema conversion
- [x] Generated data import scripts
- [x] Deployed PHP API to Namecheap hosting
- [x] Uploaded migration files to server

🔄 **Next Steps:**
- [ ] Create MySQL database in cPanel
- [ ] Configure database credentials
- [ ] Run schema and data import
- [ ] Update frontend to use new API
- [ ] Test migration
- [ ] Switch from Supabase to MySQL

## 🗃️ Data Export Summary

**Successfully exported from Supabase:**
- **profiles**: 2 records
- **videos**: 248 records  
- **categories**: 3 records
- **collections**: 1 record
- **collection_videos**: 0 records
- **comments**: 0 records
- **comment_replies**: 0 records
- **video_tags**: 0 records

**Total**: 254 records across 8 tables

## 🔧 Step 1: Create MySQL Database

1. **Login to Namecheap cPanel**
   - Go to your hosting control panel
   - Navigate to "MySQL Databases"

2. **Create Database**
   - Database name: `bluerpcm_bluefilmx` (or your preferred name)
   - Character set: `utf8mb4`
   - Collation: `utf8mb4_unicode_ci`

3. **Create Database User**
   - Username: `bluerpcm_dbuser` (or your preferred username)
   - Password: Generate a strong password
   - Grant ALL privileges to the database

4. **Note Down Credentials**
   - Host: `localhost`
   - Database: `bluerpcm_bluefilmx`
   - Username: `bluerpcm_dbuser`
   - Password: [your generated password]

## 🔧 Step 2: Configure API Database Connection

1. **Edit Database Configuration**
   ```bash
   # SSH into your server
   ssh -p 21098 <EMAIL>
   
   # Edit the database config
   nano public_html/api/config/database.php
   ```

2. **Update These Lines:**
   ```php
   private $db_name = 'bluerpcm_bluefilmx';     // Your database name
   private $username = 'bluerpcm_dbuser';       // Your database username  
   private $password = 'your_strong_password';   // Your database password
   ```

## 🔧 Step 3: Import Database Schema and Data

1. **Run Schema Creation**
   ```bash
   # SSH into your server
   ssh -p 21098 <EMAIL>
   
   # Import the schema
   mysql -u bluerpcm_dbuser -p bluerpcm_bluefilmx < create-mysql-schema.sql
   ```

2. **Import Data**
   ```bash
   # Import the data
   mysql -u bluerpcm_dbuser -p bluerpcm_bluefilmx < import-data.sql
   ```

3. **Verify Import**
   ```bash
   # Check if data was imported correctly
   mysql -u bluerpcm_dbuser -p bluerpcm_bluefilmx -e "
   SELECT 'profiles' as table_name, COUNT(*) as count FROM profiles
   UNION ALL
   SELECT 'videos', COUNT(*) FROM videos
   UNION ALL  
   SELECT 'categories', COUNT(*) FROM categories;
   "
   ```

## 🔧 Step 4: Test API Endpoints

Test your new API endpoints:

1. **Test Categories API**
   ```bash
   curl https://www.bluefilmx.com/api/categories
   ```

2. **Test Videos API**
   ```bash
   curl "https://www.bluefilmx.com/api/videos?limit=5"
   ```

3. **Test Authentication**
   ```bash
   curl -X POST https://www.bluefilmx.com/api/auth?action=register \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser","password":"testpass123"}'
   ```

## 📁 Files Deployed to Server

**API Files** (in `/public_html/api/`):
- `videos.php` - Videos CRUD operations
- `auth.php` - Authentication (login/register)
- `categories.php` - Categories listing
- `upload.php` - File upload handling
- `config/database.php` - Database configuration
- `.htaccess` - API routing and CORS

**Migration Files** (in home directory):
- `create-mysql-schema.sql` - Database schema
- `import-data.sql` - Data import script

## 🔄 Step 5: Update Frontend (After Database Setup)

Once the database is working, we'll need to:

1. **Create API Client** - Replace Supabase client
2. **Update Authentication** - Use new auth endpoints
3. **Update Data Fetching** - Use new API endpoints
4. **Update File Uploads** - Use new upload endpoint
5. **Test Everything** - Ensure all functionality works

## 🌐 API Endpoints

Your new API will be available at:

- **Videos**: `https://www.bluefilmx.com/api/videos`
- **Categories**: `https://www.bluefilmx.com/api/categories`
- **Auth**: `https://www.bluefilmx.com/api/auth`
- **Upload**: `https://www.bluefilmx.com/api/upload`

## 💡 Benefits After Migration

- ✅ **No Supabase costs** - Save money on subscription
- ✅ **Full control** - Complete control over your database
- ✅ **Better performance** - Direct database access
- ✅ **No limits** - No storage or bandwidth restrictions
- ✅ **Simplified stack** - Everything in one place

## 🆘 Need Help?

If you encounter any issues:

1. **Check API logs** - Look for PHP errors in cPanel
2. **Verify database connection** - Test credentials
3. **Check file permissions** - Ensure API files are executable
4. **Test step by step** - Verify each component works

## 📞 Next Steps

Ready to proceed? Let me know when you've:

1. ✅ Created the MySQL database in cPanel
2. ✅ Updated the database credentials in the API config
3. ✅ Run the schema and data import

Then I'll help you update the frontend to use the new API!
