-- Migration to set up scheduled refresh for the materialized view
-- This requires the pg_cron extension, which is available in Supabase

-- Check if pg_cron extension exists and create it if needed
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
  ) THEN
    -- Try to create the extension
    BEGIN
      CREATE EXTENSION pg_cron;
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE 'Could not create pg_cron extension. This is expected in development environments or if you do not have superuser privileges.';
      -- Continue with the migration
    END;
  END IF;
END
$$;

-- Create a scheduled job to refresh the materialized view
-- This will only work if pg_cron is available
DO $$
BEGIN
  -- Check if pg_cron is available
  IF EXISTS (
    SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
  ) THEN
    -- Try to schedule the job
    BEGIN
      -- First, check if the job already exists and drop it if it does
      IF EXISTS (
        SELECT 1 FROM cron.job WHERE jobname = 'refresh-top-videos-mv'
      ) THEN
        PERFORM cron.unschedule('refresh-top-videos-mv');
      END IF;
      
      -- Schedule the job to run every 5 minutes
      PERFORM cron.schedule(
        'refresh-top-videos-mv',
        '*/5 * * * *',  -- Every 5 minutes
        $$REFRESH MATERIALIZED VIEW CONCURRENTLY mv_top_videos_with_profiles$$
      );
      
      RAISE NOTICE 'Successfully scheduled materialized view refresh job.';
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE 'Could not schedule cron job: %', SQLERRM;
      -- Continue with the migration
    END;
  END IF;
END
$$;

-- Create a function to monitor materialized view performance
CREATE OR REPLACE FUNCTION public.monitor_mv_performance()
RETURNS json AS $$
DECLARE
  result json;
BEGIN
  SELECT json_build_object(
    'last_refresh', s.last_refresh,
    'row_count', s.row_count,
    'size_bytes', pg_relation_size('mv_top_videos_with_profiles'),
    'index_size_bytes', pg_indexes_size('mv_top_videos_with_profiles')
  ) INTO result
  FROM (
    SELECT 
      pg_stat_get_last_analyze_time(c.oid) AS last_refresh,
      (SELECT COUNT(*) FROM mv_top_videos_with_profiles) AS row_count
    FROM pg_class c
    WHERE c.relname = 'mv_top_videos_with_profiles'
  ) s;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Create a function to handle manual refresh
CREATE OR REPLACE FUNCTION public.refresh_top_videos_now()
RETURNS json AS $$
DECLARE
  start_time timestamptz;
  end_time timestamptz;
  result json;
BEGIN
  start_time := clock_timestamp();
  
  -- Refresh the materialized view
  PERFORM public.refresh_top_videos_mv();
  
  end_time := clock_timestamp();
  
  -- Return performance metrics
  SELECT json_build_object(
    'success', true,
    'refresh_time_ms', extract(epoch from (end_time - start_time)) * 1000,
    'refreshed_at', end_time,
    'stats', public.monitor_mv_performance()
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';
