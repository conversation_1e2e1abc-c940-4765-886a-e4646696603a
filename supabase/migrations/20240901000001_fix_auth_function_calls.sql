-- Migration to fix direct auth function calls in RLS policies
-- This migration replaces all direct auth.uid() calls with (SELECT auth.uid())
-- to avoid per-row re-evaluation performance issues

-- Create a temporary table to log changes
CREATE TEMP TABLE policy_changes (
  id SERIAL PRIMARY KEY,
  table_name TEXT,
  policy_name TEXT,
  original_expression TEXT,
  updated_expression TEXT,
  clause_type TEXT,
  status TEXT
);

-- Function to update policies with direct auth.uid() calls in USING clause
DO $$
DECLARE
  policy RECORD;
  updated_qual TEXT;
BEGIN
  -- Loop through all policies with auth.uid() in USING clause
  FOR policy IN 
    SELECT 
      schemaname, 
      tablename, 
      policyname, 
      qual::text AS using_clause
    FROM pg_policies 
    WHERE 
      schemaname = 'public' AND
      qual::text LIKE '%auth.uid()%' AND
      qual::text NOT LIKE '%(SELECT auth.uid())%'
  LOOP
    -- Replace auth.uid() with (SELECT auth.uid())
    updated_qual := regexp_replace(policy.using_clause, 'auth\.uid\(\)', '(SELECT auth.uid())', 'g');
    
    BEGIN
      -- Update the policy
      EXECUTE format(
        'ALTER POLICY %I ON %I.%I USING (%s)',
        policy.policyname,
        policy.schemaname,
        policy.tablename,
        updated_qual
      );
      
      -- Log the change
      INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
      VALUES (
        policy.tablename, 
        policy.policyname, 
        policy.using_clause, 
        updated_qual, 
        'USING', 
        'SUCCESS'
      );
    EXCEPTION WHEN OTHERS THEN
      -- Log the error
      INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
      VALUES (
        policy.tablename, 
        policy.policyname, 
        policy.using_clause, 
        updated_qual, 
        'USING', 
        'ERROR: ' || SQLERRM
      );
    END;
  END LOOP;
END;
$$;

-- Function to update policies with direct auth.uid() calls in WITH CHECK clause
DO $$
DECLARE
  policy RECORD;
  updated_check TEXT;
BEGIN
  -- Loop through all policies with auth.uid() in WITH CHECK clause
  FOR policy IN 
    SELECT 
      schemaname, 
      tablename, 
      policyname, 
      with_check::text AS with_check_clause
    FROM pg_policies 
    WHERE 
      schemaname = 'public' AND
      with_check::text LIKE '%auth.uid()%' AND
      with_check::text NOT LIKE '%(SELECT auth.uid())%' AND
      with_check::text IS NOT NULL
  LOOP
    -- Replace auth.uid() with (SELECT auth.uid())
    updated_check := regexp_replace(policy.with_check_clause, 'auth\.uid\(\)', '(SELECT auth.uid())', 'g');
    
    BEGIN
      -- Update the policy
      EXECUTE format(
        'ALTER POLICY %I ON %I.%I WITH CHECK (%s)',
        policy.policyname,
        'public',
        policy.tablename,
        updated_check
      );
      
      -- Log the change
      INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
      VALUES (
        policy.tablename, 
        policy.policyname, 
        policy.with_check_clause, 
        updated_check, 
        'WITH CHECK', 
        'SUCCESS'
      );
    EXCEPTION WHEN OTHERS THEN
      -- Log the error
      INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
      VALUES (
        policy.tablename, 
        policy.policyname, 
        policy.with_check_clause, 
        updated_check, 
        'WITH CHECK', 
        'ERROR: ' || SQLERRM
      );
    END;
  END LOOP;
END;
$$;

-- Function to update storage policies with direct auth.uid() calls
DO $$
DECLARE
  policy RECORD;
  updated_qual TEXT;
  updated_check TEXT;
BEGIN
  -- Loop through all storage policies with auth.uid() in USING clause
  FOR policy IN 
    SELECT 
      schemaname, 
      tablename, 
      policyname, 
      qual::text AS using_clause,
      with_check::text AS with_check_clause
    FROM pg_policies 
    WHERE 
      schemaname = 'storage' AND
      (qual::text LIKE '%auth.uid()%' OR with_check::text LIKE '%auth.uid()%') AND
      (qual::text NOT LIKE '%(SELECT auth.uid())%' OR with_check::text NOT LIKE '%(SELECT auth.uid())%')
  LOOP
    -- Handle USING clause
    IF policy.using_clause IS NOT NULL AND policy.using_clause LIKE '%auth.uid()%' AND policy.using_clause NOT LIKE '%(SELECT auth.uid())%' THEN
      updated_qual := regexp_replace(policy.using_clause, 'auth\.uid\(\)', '(SELECT auth.uid())', 'g');
      
      BEGIN
        -- Update the policy USING clause
        EXECUTE format(
          'ALTER POLICY %I ON %I.%I USING (%s)',
          policy.policyname,
          policy.schemaname,
          policy.tablename,
          updated_qual
        );
        
        -- Log the change
        INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
        VALUES (
          policy.tablename, 
          policy.policyname, 
          policy.using_clause, 
          updated_qual, 
          'USING (storage)', 
          'SUCCESS'
        );
      EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
        VALUES (
          policy.tablename, 
          policy.policyname, 
          policy.using_clause, 
          updated_qual, 
          'USING (storage)', 
          'ERROR: ' || SQLERRM
        );
      END;
    END IF;
    
    -- Handle WITH CHECK clause
    IF policy.with_check_clause IS NOT NULL AND policy.with_check_clause LIKE '%auth.uid()%' AND policy.with_check_clause NOT LIKE '%(SELECT auth.uid())%' THEN
      updated_check := regexp_replace(policy.with_check_clause, 'auth\.uid\(\)', '(SELECT auth.uid())', 'g');
      
      BEGIN
        -- Update the policy WITH CHECK clause
        EXECUTE format(
          'ALTER POLICY %I ON %I.%I WITH CHECK (%s)',
          policy.policyname,
          policy.schemaname,
          policy.tablename,
          updated_check
        );
        
        -- Log the change
        INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
        VALUES (
          policy.tablename, 
          policy.policyname, 
          policy.with_check_clause, 
          updated_check, 
          'WITH CHECK (storage)', 
          'SUCCESS'
        );
      EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
        VALUES (
          policy.tablename, 
          policy.policyname, 
          policy.with_check_clause, 
          updated_check, 
          'WITH CHECK (storage)', 
          'ERROR: ' || SQLERRM
        );
      END;
    END IF;
  END LOOP;
END;
$$;

-- Output summary of changes
SELECT 
  COUNT(*) AS total_policies_checked,
  COUNT(*) FILTER (WHERE status LIKE 'SUCCESS%') AS policies_updated,
  COUNT(*) FILTER (WHERE status LIKE 'ERROR%') AS policies_with_errors
FROM policy_changes;

-- Output detailed log of changes
SELECT * FROM policy_changes ORDER BY id;

-- Clean up
DROP TABLE policy_changes;
