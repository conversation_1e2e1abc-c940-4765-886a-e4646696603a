-- Migration to optimize video queries performance
-- This migration implements:
-- 1. Optimized indexes for the videos table
-- 2. Materialized view for pre-computed video + profile data
-- 3. Functions for querying with both offset and keyset pagination
-- 4. Triggers and scheduled refreshes for the materialized view

-- Part 1: Create optimized indexes
-- Index for sorting by views
CREATE INDEX IF NOT EXISTS idx_videos_views_desc ON public.videos (views DESC);

-- Index for the join between videos and profiles
CREATE INDEX IF NOT EXISTS idx_videos_user_id ON public.videos (user_id);

-- Composite index for both sorting and joining
CREATE INDEX IF NOT EXISTS idx_videos_views_user_id ON public.videos (views DESC, user_id);

-- Index for timestamp-based filtering
CREATE INDEX IF NOT EXISTS idx_videos_created_at ON public.videos (created_at DESC);

-- Covering index for common query patterns
CREATE INDEX IF NOT EXISTS idx_videos_covering ON public.videos 
  (views DESC, user_id) 
  INCLUDE (title, thumbnail_url, duration, likes, is_hd);

-- Part 2: Create materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_top_videos_with_profiles AS
SELECT 
    v.*,
    jsonb_build_object(
        'id', p.id,
        'username', p.username,
        'avatar_url', p.avatar_url
    ) AS profiles
FROM 
    public.videos v
LEFT JOIN 
    public.profiles p ON p.id = v.user_id
ORDER BY 
    v.views DESC;

-- Create indexes on the materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_top_videos_id ON mv_top_videos_with_profiles (id);
CREATE INDEX IF NOT EXISTS idx_mv_top_videos_views_desc ON mv_top_videos_with_profiles (views DESC);

-- Part 3: Create functions for querying the materialized view
-- Function to fetch videos with profiles using the materialized view (offset pagination)
CREATE OR REPLACE FUNCTION public.get_top_videos_with_profiles(
  video_limit int DEFAULT 10,
  video_offset int DEFAULT 0
)
RETURNS json AS $$
DECLARE
  total_count bigint;
  result_data json;
BEGIN
  -- Get total count once
  SELECT COUNT(*) INTO total_count FROM public.videos;
  
  -- Query the materialized view with pagination
  WITH paged_results AS (
    SELECT *
    FROM mv_top_videos_with_profiles
    LIMIT video_limit OFFSET video_offset
  )
  SELECT 
    json_build_object(
      'total_result_set', total_count,
      'page_total', COUNT(*),
      'body', COALESCE(json_agg(paged_results), '[]'::json)
    ) INTO result_data
  FROM paged_results;
  
  RETURN result_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Function for keyset pagination (more efficient for large offsets)
CREATE OR REPLACE FUNCTION public.get_top_videos_keyset(
  last_views int DEFAULT NULL,
  last_id uuid DEFAULT NULL,
  page_size int DEFAULT 10
)
RETURNS json AS $$
DECLARE
  total_count bigint;
  result_data json;
  next_views int;
  next_id uuid;
BEGIN
  -- Get total count once
  SELECT COUNT(*) INTO total_count FROM public.videos;
  
  -- Query with keyset pagination
  WITH keyset_results AS (
    SELECT *
    FROM mv_top_videos_with_profiles
    WHERE 
      -- First page condition (when last_views and last_id are NULL)
      (last_views IS NULL AND last_id IS NULL) OR
      -- Subsequent pages condition
      (views < last_views OR (views = last_views AND id > last_id))
    ORDER BY views DESC, id ASC
    LIMIT page_size + 1  -- Get one extra for next page marker
  ),
  limited_results AS (
    SELECT * FROM keyset_results LIMIT page_size
  ),
  next_marker AS (
    SELECT * FROM keyset_results OFFSET page_size LIMIT 1
  )
  SELECT 
    json_build_object(
      'total_result_set', total_count,
      'page_total', (SELECT COUNT(*) FROM limited_results),
      'body', COALESCE((SELECT json_agg(r) FROM limited_results r), '[]'::json),
      'next_views', (SELECT views FROM next_marker),
      'next_id', (SELECT id FROM next_marker)
    ) INTO result_data;
  
  RETURN result_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Part 4: Create function to refresh the materialized view
CREATE OR REPLACE FUNCTION public.refresh_top_videos_mv()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_top_videos_with_profiles;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Create trigger function to refresh the materialized view when data changes
CREATE OR REPLACE FUNCTION public.refresh_top_videos_mv_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Queue a refresh of the materialized view
  -- Using pg_notify to avoid blocking the transaction
  PERFORM pg_notify('refresh_top_videos_mv', '');
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Create triggers for videos table
DROP TRIGGER IF EXISTS videos_refresh_mv_trigger ON public.videos;
CREATE TRIGGER videos_refresh_mv_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.videos
FOR EACH STATEMENT EXECUTE FUNCTION public.refresh_top_videos_mv_trigger();

-- Create triggers for profiles table
DROP TRIGGER IF EXISTS profiles_refresh_mv_trigger ON public.profiles;
CREATE TRIGGER profiles_refresh_mv_trigger
AFTER UPDATE ON public.profiles
FOR EACH STATEMENT EXECUTE FUNCTION public.refresh_top_videos_mv_trigger();

-- Part 5: Create fallback function in case materialized view is unavailable
CREATE OR REPLACE FUNCTION public.get_top_videos_fallback(
  video_limit int DEFAULT 10,
  video_offset int DEFAULT 0
)
RETURNS json AS $$
DECLARE
  total_count bigint;
  result_data json;
BEGIN
  -- Get total count
  SELECT COUNT(*) INTO total_count FROM public.videos;
  
  -- Direct query as fallback
  WITH pgrst_source AS (
    SELECT
      v.*,
      row_to_json(p.*)::jsonb AS profiles
    FROM public.videos v
    LEFT JOIN LATERAL (
      SELECT id, username, avatar_url
      FROM public.profiles
      WHERE id = v.user_id
    ) p ON true
    ORDER BY v.views DESC
    LIMIT video_limit OFFSET video_offset
  )
  SELECT 
    json_build_object(
      'total_result_set', total_count,
      'page_total', COUNT(*),
      'body', COALESCE(json_agg(pgrst_source), '[]'::json)
    ) INTO result_data
  FROM pgrst_source;
  
  RETURN result_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Initial refresh of the materialized view
SELECT public.refresh_top_videos_mv();
