-- Migration to optimize database performance
-- 1. Fix any remaining direct auth function calls in RLS policies
-- 2. Optimize slow schema-introspection queries

-- Part 1: Fix RLS Policies
-- Create a temporary table to log changes
CREATE TEMP TABLE policy_changes (
  id SERIAL PRIMARY KEY,
  table_name TEXT,
  policy_name TEXT,
  original_expression TEXT,
  updated_expression TEXT,
  clause_type TEXT,
  status TEXT
);

-- Function to update policies with direct auth function calls in USING clause
DO $$
DECLARE
  policy RECORD;
  updated_qual TEXT;
BEGIN
  -- Loop through all policies with direct auth function calls in USING clause
  FOR policy IN 
    SELECT 
      schemaname, 
      tablename, 
      policyname, 
      qual::text AS using_clause
    FROM pg_policies 
    WHERE 
      qual::text ~ 'auth\.[a-zA-Z0-9_]+\(\)' AND
      qual::text !~ '\(SELECT auth\.[a-zA-Z0-9_]+\(\)'
  LOOP
    -- Replace auth.function() with (SELECT auth.function())
    updated_qual := regexp_replace(policy.using_clause, 'auth\.([a-zA-Z0-9_]+)\(\)', '(SELECT auth.\1())', 'g');
    
    BEGIN
      -- Update the policy
      EXECUTE format(
        'ALTER POLICY %I ON %I.%I USING (%s)',
        policy.policyname,
        policy.schemaname,
        policy.tablename,
        updated_qual
      );
      
      -- Log the change
      INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
      VALUES (
        policy.tablename, 
        policy.policyname, 
        policy.using_clause, 
        updated_qual, 
        'USING', 
        'SUCCESS'
      );
    EXCEPTION WHEN OTHERS THEN
      -- Log the error
      INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
      VALUES (
        policy.tablename, 
        policy.policyname, 
        policy.using_clause, 
        updated_qual, 
        'USING', 
        'ERROR: ' || SQLERRM
      );
    END;
  END LOOP;
END;
$$;

-- Function to update policies with direct auth function calls in WITH CHECK clause
DO $$
DECLARE
  policy RECORD;
  updated_check TEXT;
BEGIN
  -- Loop through all policies with direct auth function calls in WITH CHECK clause
  FOR policy IN 
    SELECT 
      schemaname, 
      tablename, 
      policyname, 
      with_check::text AS with_check_clause
    FROM pg_policies 
    WHERE 
      with_check::text IS NOT NULL AND
      with_check::text ~ 'auth\.[a-zA-Z0-9_]+\(\)' AND
      with_check::text !~ '\(SELECT auth\.[a-zA-Z0-9_]+\(\)'
  LOOP
    -- Replace auth.function() with (SELECT auth.function())
    updated_check := regexp_replace(policy.with_check_clause, 'auth\.([a-zA-Z0-9_]+)\(\)', '(SELECT auth.\1())', 'g');
    
    BEGIN
      -- Update the policy
      EXECUTE format(
        'ALTER POLICY %I ON %I.%I WITH CHECK (%s)',
        policy.policyname,
        'public',
        policy.tablename,
        updated_check
      );
      
      -- Log the change
      INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
      VALUES (
        policy.tablename, 
        policy.policyname, 
        policy.with_check_clause, 
        updated_check, 
        'WITH CHECK', 
        'SUCCESS'
      );
    EXCEPTION WHEN OTHERS THEN
      -- Log the error
      INSERT INTO policy_changes (table_name, policy_name, original_expression, updated_expression, clause_type, status)
      VALUES (
        policy.tablename, 
        policy.policyname, 
        policy.with_check_clause, 
        updated_check, 
        'WITH CHECK', 
        'ERROR: ' || SQLERRM
      );
    END;
  END LOOP;
END;
$$;

-- Output summary of RLS policy changes
SELECT 
  COUNT(*) AS total_policies_checked,
  COUNT(*) FILTER (WHERE status LIKE 'SUCCESS%') AS policies_updated,
  COUNT(*) FILTER (WHERE status LIKE 'ERROR%') AS policies_with_errors
FROM policy_changes;

-- Part 2: Optimize Schema-Introspection Queries
-- Create an optimized function to replace the slow CTE-based query
CREATE OR REPLACE FUNCTION public.get_schema_sql(
  schemas text[],
  relkinds text[],
  limit_val int,
  offset_val int,
  include_columns boolean DEFAULT true,
  include_constraints boolean DEFAULT true,
  include_indexes boolean DEFAULT true
) RETURNS jsonb AS $$
DECLARE
  result jsonb;
BEGIN
  -- Step 1: Extract the list of object oids first
  WITH targets AS (
    SELECT 
      c.oid, 
      nc.nspname, 
      c.relname, 
      c.relkind
    FROM 
      pg_namespace nc
      JOIN pg_class c ON c.relnamespace = nc.oid
    WHERE 
      c.relkind = ANY(relkinds)
      AND nc.nspname = ANY(schemas)
      AND NOT pg_is_other_temp_schema(nc.oid)
      AND (
        pg_has_role(c.relowner, 'USAGE')
        OR has_table_privilege(c.oid, 'SELECT')
        OR has_any_column_privilege(c.oid, 'SELECT')
      )
    ORDER BY 
      c.relname ASC
    LIMIT 
      limit_val
    OFFSET 
      offset_val
  )
  -- Step 2: Use a LATERAL join to call each def-function exactly once
  SELECT 
    jsonb_build_object(
      'data',
      COALESCE(
        jsonb_agg(
          jsonb_build_object(
            'id', t.oid,
            'sql', COALESCE(
              CASE 
                WHEN t.relkind = 'r' THEN pg_temp.pg_get_tabledef(t.nspname, t.relname, include_columns, include_constraints, include_indexes)
                WHEN t.relkind = 'v' THEN 'CREATE VIEW ' || t.nspname || '.' || t.relname || ' AS ' || pg_get_viewdef(t.nspname || '.' || t.relname, true)
                WHEN t.relkind = 'm' THEN 'CREATE MATERIALIZED VIEW ' || t.nspname || '.' || t.relname || ' AS ' || pg_get_viewdef(t.nspname || '.' || t.relname, true)
                WHEN t.relkind = 'S' THEN 'CREATE SEQUENCE ' || t.nspname || '.' || t.relname
                WHEN t.relkind = 'f' THEN pg_temp.pg_get_tabledef(t.nspname, t.relname, include_columns, include_constraints, include_indexes)
              END,
              ''
            )
          )
        ),
        '[]'::jsonb
      )
    ) INTO result
  FROM 
    targets t;

  RETURN result;
END;
$$ LANGUAGE plpgsql STABLE;

-- Create a comment for the function
COMMENT ON FUNCTION public.get_schema_sql(text[], text[], int, int, boolean, boolean, boolean) IS 
'Optimized function to retrieve schema SQL definitions with better performance.
Parameters:
- schemas: Array of schema names to include
- relkinds: Array of relation kinds to include (r=table, v=view, m=materialized view, S=sequence, f=foreign table)
- limit_val: Maximum number of objects to return
- offset_val: Number of objects to skip
- include_columns: Whether to include column definitions (for tables)
- include_constraints: Whether to include constraint definitions (for tables)
- include_indexes: Whether to include index definitions (for tables)';

-- Clean up
DROP TABLE policy_changes;
