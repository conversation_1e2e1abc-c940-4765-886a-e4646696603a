# Supabase Auth Function Call Fixes

This document explains the security and performance fixes implemented in the migration `20240901000001_fix_auth_function_calls.sql`.

## The Problem: Direct `auth.<function>()` Calls in RLS Policies

When Row Level Security (RLS) policies directly call `auth.<function>()` (e.g., `auth.uid()`, `auth.role()`, etc.), these functions are re-evaluated for each row being processed. This can lead to significant performance issues, especially for tables with many rows.

### Example of problematic policy:

```sql
CREATE POLICY "Users can update own profile"
ON public.profiles
FOR UPDATE
USING (profiles.id = auth.uid());
```

In this example, `auth.uid()` is called for every row in the `profiles` table when an update operation is performed.

## The Solution: Scalar Subqueries

The recommended approach is to wrap these function calls in scalar subqueries, like `(SELECT auth.uid())`. This ensures the function is evaluated only once per query, rather than once per row.

### Example of fixed policy:

```sql
CREATE POLICY "Users can update own profile"
ON public.profiles
FOR UPDATE
USING (profiles.id = (SELECT auth.uid()));
```

## What This Migration Does

The migration `20240901000001_fix_auth_function_calls.sql` automatically:

1. Identifies all RLS policies in the `public` and `storage` schemas that directly call `auth.uid()` or other auth functions
2. Generates and executes `ALTER POLICY` statements to replace direct calls with scalar subqueries
3. Logs each change, including:
   - Table name
   - Policy name
   - Original expression
   - Updated expression
   - Type of clause (USING or WITH CHECK)
   - Status of the update (SUCCESS or ERROR with message)

## Benefits

- **Improved Performance**: Reduces the number of function calls, especially for large tables
- **Consistent Behavior**: Ensures the auth function returns the same value throughout the query execution
- **Best Practice**: Follows Supabase's recommended approach for RLS policies

## How to Apply This Migration

### Using Supabase CLI

1. Make sure you have the Supabase CLI installed:
   ```
   npm install -g supabase
   ```

2. Log in to your Supabase account:
   ```
   supabase login
   ```

3. Link your project (if not already linked):
   ```
   supabase link --project-ref your-project-ref
   ```
   Replace `your-project-ref` with your actual Supabase project reference ID.

4. Apply the migration:
   ```
   supabase db push
   ```

### Using the Supabase Dashboard

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Select your project
3. Navigate to the SQL Editor
4. Copy and paste the contents of the migration file
5. Execute the SQL statements

## Verifying the Changes

After applying the migration, you can verify that all policies have been updated correctly by running:

```sql
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  qual::text AS using_clause, 
  with_check::text AS with_check_clause
FROM pg_policies 
WHERE 
  (qual::text LIKE '%auth.uid()%' AND qual::text NOT LIKE '%(SELECT auth.uid())%')
  OR 
  (with_check::text LIKE '%auth.uid()%' AND with_check::text NOT LIKE '%(SELECT auth.uid())%');
```

This query should return no rows if all policies have been updated successfully.
