-- Create comments table
CREATE TABLE IF NOT EXISTS public.comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  video_id UUID NOT NULL REFERENCES public.videos(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  user_name TEXT,
  user_avatar TEXT,
  content TEXT NOT NULL,
  likes INTEGER DEFAULT 0,
  is_liked BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- <PERSON>reate comment_replies table
CREATE TABLE IF NOT EXISTS public.comment_replies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  comment_id UUID NOT NULL REFERENCES public.comments(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  user_name TEXT,
  user_avatar TEXT,
  content TEXT NOT NULL,
  likes INTEGER DEFAULT 0,
  is_liked BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_comments_video_id ON public.comments (video_id);
CREATE INDEX IF NOT EXISTS idx_comment_replies_comment_id ON public.comment_replies (comment_id);

-- Enable RLS on comments tables
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comment_replies ENABLE ROW LEVEL SECURITY;

-- Create policies for comments
-- Anyone can view comments
CREATE POLICY "Anyone can view comments" 
ON public.comments
FOR SELECT 
USING (true);

-- Only authenticated users can insert comments
CREATE POLICY "Authenticated users can insert comments" 
ON public.comments
FOR INSERT 
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Users can update their own comments
CREATE POLICY "Users can update their own comments" 
ON public.comments
FOR UPDATE 
TO authenticated
USING (auth.uid() = user_id);

-- Users can delete their own comments
CREATE POLICY "Users can delete their own comments" 
ON public.comments
FOR DELETE 
TO authenticated
USING (auth.uid() = user_id);

-- Create policies for comment replies
-- Anyone can view comment replies
CREATE POLICY "Anyone can view comment replies" 
ON public.comment_replies
FOR SELECT 
USING (true);

-- Only authenticated users can insert comment replies
CREATE POLICY "Authenticated users can insert comment replies" 
ON public.comment_replies
FOR INSERT 
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Users can update their own comment replies
CREATE POLICY "Users can update their own comment replies" 
ON public.comment_replies
FOR UPDATE 
TO authenticated
USING (auth.uid() = user_id);

-- Users can delete their own comment replies
CREATE POLICY "Users can delete their own comment replies" 
ON public.comment_replies
FOR DELETE 
TO authenticated
USING (auth.uid() = user_id);

-- Create functions for incrementing and decrementing comment likes
CREATE OR REPLACE FUNCTION increment_comment_likes(comment_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.comments
  SET likes = likes + 1,
      updated_at = now()
  WHERE id = comment_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION decrement_comment_likes(comment_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.comments
  SET likes = GREATEST(0, likes - 1),
      updated_at = now()
  WHERE id = comment_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
