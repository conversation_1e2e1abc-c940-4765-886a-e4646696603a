/*
  # Initial schema setup for video streaming platform

  1. New Tables
    - `profiles`
      - `id` (uuid, primary key, references auth.users)
      - `username` (text, unique)
      - `avatar_url` (text)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `videos`
      - `id` (uuid, primary key)
      - `title` (text)
      - `description` (text)
      - `thumbnail_url` (text)
      - `video_url` (text)
      - `duration` (integer)
      - `views` (integer)
      - `likes` (integer)
      - `is_hd` (boolean)
      - `user_id` (uuid, references profiles)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

    - `video_tags`
      - `id` (uuid, primary key)
      - `video_id` (uuid, references videos)
      - `tag` (text)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Create profiles table
CREATE TABLE public.profiles (
  id uuid PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  username text UNIQUE NOT NULL,
  avatar_url text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create videos table
CREATE TABLE public.videos (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text,
  thumbnail_url text,
  video_url text NOT NULL,
  duration integer DEFAULT 0,
  views integer DEFAULT 0,
  likes integer DEFAULT 0,
  is_hd boolean DEFAULT false,
  user_id uuid REFERENCES public.profiles ON DELETE CASCADE NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create video tags table
CREATE TABLE public.video_tags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  video_id uuid REFERENCES public.videos ON DELETE CASCADE NOT NULL,
  tag text NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.video_tags ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Public profiles are viewable by everyone"
  ON public.profiles
  FOR SELECT
  USING (true);

CREATE POLICY "Users can update own profile"
  ON public.profiles
  FOR UPDATE
  USING (auth.uid() = id);

-- Videos policies
CREATE POLICY "Videos are viewable by everyone"
  ON public.videos
  FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can insert videos"
  ON public.videos
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own videos"
  ON public.videos
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own videos"
  ON public.videos
  FOR DELETE
  USING (auth.uid() = user_id);

-- Video tags policies
CREATE POLICY "Video tags are viewable by everyone"
  ON public.video_tags
  FOR SELECT
  USING (true);

CREATE POLICY "Users can manage tags for own videos"
  ON public.video_tags
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.videos
      WHERE videos.id = video_tags.video_id
      AND videos.user_id = auth.uid()
    )
  );