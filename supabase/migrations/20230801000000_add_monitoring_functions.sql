-- Create user_roles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_roles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  role text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  UNIQUE(user_id)
);

-- Add RLS policies to user_roles
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own roles"
  ON public.user_roles
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Only admins can insert roles"
  ON public.user_roles
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can update roles"
  ON public.user_roles
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can delete roles"
  ON public.user_roles
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Create function to get connection stats
CREATE OR REPLACE FUNCTION public.get_connection_stats()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result json;
BEGIN
  SELECT json_build_object(
    'active_connections', count(*) FILTER (WHERE state = 'active'),
    'idle_connections', count(*) FILTER (WHERE state = 'idle'),
    'max_connections', setting::int,
    'connection_utilization_percent', (count(*)::float / setting::int * 100)::numeric(5,2)
  ) INTO result
  FROM pg_stat_activity, pg_settings
  WHERE pg_settings.name = 'max_connections';
  
  RETURN result;
END;
$$;

-- Create function to get table stats
CREATE OR REPLACE FUNCTION public.get_table_stats()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result json;
BEGIN
  SELECT json_agg(json_build_object(
    'table_name', relname,
    'live_rows', n_live_tup,
    'dead_rows', n_dead_tup,
    'size_bytes', pg_relation_size(relid),
    'index_size_bytes', pg_indexes_size(relid),
    'last_vacuum', last_vacuum,
    'last_analyze', last_analyze
  ))
  INTO result
  FROM pg_stat_user_tables
  WHERE schemaname = 'public';
  
  RETURN result;
END;
$$;

-- Create function to get index stats
CREATE OR REPLACE FUNCTION public.get_index_stats()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result json;
BEGIN
  SELECT json_agg(json_build_object(
    'index_name', indexrelname,
    'table_name', relname,
    'size_bytes', pg_relation_size(indexrelid),
    'index_scans', idx_scan
  ))
  INTO result
  FROM pg_stat_user_indexes
  WHERE schemaname = 'public';
  
  RETURN result;
END;
$$;

-- Create function to get slow queries
CREATE OR REPLACE FUNCTION public.get_slow_queries()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result json;
BEGIN
  -- This requires pg_stat_statements extension
  -- Check if it exists
  IF EXISTS (
    SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements'
  ) THEN
    SELECT json_agg(json_build_object(
      'query', query,
      'duration', mean_exec_time,
      'calls', calls,
      'rows_returned', rows
    ))
    INTO result
    FROM pg_stat_statements
    WHERE mean_exec_time > 100 -- queries taking more than 100ms on average
    ORDER BY mean_exec_time DESC
    LIMIT 10;
  ELSE
    -- Return empty array if extension is not available
    result := '[]'::json;
  END IF;
  
  RETURN result;
END;
$$;

-- Create function to get cache hit ratio
CREATE OR REPLACE FUNCTION public.get_cache_hit_ratio()
RETURNS numeric
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result numeric;
BEGIN
  SELECT
    sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read) + 0.001) * 100
  INTO result
  FROM pg_statio_user_tables;
  
  RETURN result;
END;
$$;

-- Create function to get query stats
CREATE OR REPLACE FUNCTION public.get_query_stats()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result json;
BEGIN
  -- This requires pg_stat_statements extension
  -- Check if it exists
  IF EXISTS (
    SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements'
  ) THEN
    SELECT json_agg(json_build_object(
      'query', query,
      'calls', calls,
      'total_time', total_exec_time,
      'mean_time', mean_exec_time,
      'rows', rows
    ))
    INTO result
    FROM pg_stat_statements
    ORDER BY total_exec_time DESC
    LIMIT 20;
  ELSE
    -- Return empty array if extension is not available
    result := '[]'::json;
  END IF;
  
  RETURN result;
END;
$$;
