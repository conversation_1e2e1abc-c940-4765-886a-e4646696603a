-- Migration to fix role mutable search_path issues and add admin auto-assignment

-- Fix approve_user function
CREATE OR REPLACE FUNCTION public.approve_user(user_id uuid)
RETURNS void AS $$
BEGIN
  UPDATE public.profiles
  SET is_approved = true
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Fix is_user_approved function
CREATE OR REPLACE FUNCTION public.is_user_approved(user_id uuid)
RETURNS boolean AS $$
DECLARE
  is_approved boolean;
BEGIN
  SELECT p.is_approved INTO is_approved
  FROM public.profiles p
  WHERE p.id = user_id;
  
  RETURN COALESCE(is_approved, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Fix is_user_admin function
CREATE OR REPLACE FUNCTION public.is_user_admin(user_id uuid)
RETURNS boolean AS $$
DECLARE
  is_admin boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = $1
    AND role = 'admin'
  ) INTO is_admin;
  
  RETURN COALESCE(is_admin, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Fix increment_comment_likes function
CREATE OR REPLACE FUNCTION public.increment_comment_likes(comment_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.comments
  SET likes = likes + 1,
      updated_at = now()
  WHERE id = comment_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Fix decrement_comment_likes function
CREATE OR REPLACE FUNCTION public.decrement_comment_likes(comment_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.comments
  SET likes = GREATEST(0, likes - 1),
      updated_at = now()
  WHERE id = comment_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Fix handle_new_user function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, username, created_at, updated_at)
  VALUES (new.id, split_part(new.email, '@', 1), now(), now())
  ON CONFLICT (id) DO NOTHING;
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Fix create_missing_profiles function
CREATE OR REPLACE FUNCTION public.create_missing_profiles()
RETURNS void AS $$
DECLARE
  missing_user RECORD;
BEGIN
  FOR missing_user IN
    SELECT au.id, au.email
    FROM auth.users au
    LEFT JOIN public.profiles p ON p.id = au.id
    WHERE p.id IS NULL
  LOOP
    INSERT INTO public.profiles (id, username, created_at, updated_at)
    VALUES (
      missing_user.id,
      split_part(missing_user.email, '@', 1),
      now(),
      now()
    )
    ON CONFLICT (id) DO NOTHING;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Fix increment_collection_video_count function
CREATE OR REPLACE FUNCTION public.increment_collection_video_count(collection_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.collections
  SET video_count = video_count + 1,
      updated_at = now()
  WHERE id = collection_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Fix decrement_collection_video_count function
CREATE OR REPLACE FUNCTION public.decrement_collection_video_count(collection_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.collections
  SET video_count = GREATEST(0, video_count - 1),
      updated_at = now()
  WHERE id = collection_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Fix reorder_collection_videos function
CREATE OR REPLACE FUNCTION public.reorder_collection_videos(updates_json JSONB)
RETURNS void AS $$
DECLARE
  update_item JSONB;
BEGIN
  FOR update_item IN SELECT * FROM jsonb_array_elements(updates_json)
  LOOP
    UPDATE public.collection_videos
    SET position = (update_item->>'position')::INTEGER
    WHERE collection_id = (update_item->>'collection_id')::UUID
      AND video_id = (update_item->>'video_id')::UUID;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Create function to automatically make specific users admins
CREATE OR REPLACE FUNCTION public.handle_new_user_admin_check()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the new user's email is in the list of admin emails
  IF new.email = '<EMAIL>' OR new.email = '<EMAIL>' THEN
    -- Insert a record into user_roles to make this user an admin
    INSERT INTO public.user_roles (user_id, role)
    VALUES (new.id, 'admin')
    ON CONFLICT (user_id) DO NOTHING;
    
    -- Also approve the user automatically
    UPDATE public.profiles
    SET is_approved = true
    WHERE id = new.id;
  END IF;
  
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- Create a trigger to call the admin check function when a new user is created
DO $$
BEGIN
  -- Drop the trigger if it exists
  DROP TRIGGER IF EXISTS on_auth_user_created_admin_check ON auth.users;

  -- Create the trigger
  CREATE TRIGGER on_auth_user_created_admin_check
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user_admin_check();
EXCEPTION
  WHEN OTHERS THEN
    -- If there's an error (like insufficient permissions), log it but continue
    RAISE NOTICE 'Could not create admin check trigger: %', SQLERRM;
END
$$;
