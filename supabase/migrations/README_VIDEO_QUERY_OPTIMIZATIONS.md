# Video Query Performance Optimizations

This document explains the performance optimizations implemented for video queries in the migrations:
- `20240910000001_optimize_video_queries.sql`
- `20240910000002_setup_scheduled_refresh.sql`

## Problem Statement

The original query pattern used PostgREST-generated SQL with LATERAL joins and row_to_json operations:

```sql
WITH pgrst_source AS (
  SELECT
    public.videos.*,
    row_to_json(videos_profiles_1.*)::jsonb AS profiles
  FROM public.videos
  LEFT JOIN LATERAL (
    SELECT id, username, avatar_url
    FROM public.profiles AS profiles_1
    WHERE profiles_1.id = public.videos.user_id
    LIMIT $1 OFFSET $2
  ) AS videos_profiles_1 ON true
  ORDER BY public.videos.views DESC
  LIMIT $3 OFFSET $4
)
SELECT
  $6::bigint    AS total_result_set,
  count(*)      AS page_total,
  coalesce(json_agg(_postgrest_t), $7) AS body
FROM pgrst_source _postgrest_t;
```

This query pattern had several performance issues:
1. Repeated `row_to_json` function calls for every row
2. LATERAL joins requiring per-row subqueries
3. Inefficient sorting without proper indexes
4. High CPU and I/O load under high query rates

## Implemented Optimizations

### 1. Optimized Indexes

```sql
-- Index for sorting by views
CREATE INDEX IF NOT EXISTS idx_videos_views_desc ON public.videos (views DESC);

-- Index for the join between videos and profiles
CREATE INDEX IF NOT EXISTS idx_videos_user_id ON public.videos (user_id);

-- Composite index for both sorting and joining
CREATE INDEX IF NOT EXISTS idx_videos_views_user_id ON public.videos (views DESC, user_id);

-- Index for timestamp-based filtering
CREATE INDEX IF NOT EXISTS idx_videos_created_at ON public.videos (created_at DESC);

-- Covering index for common query patterns
CREATE INDEX IF NOT EXISTS idx_videos_covering ON public.videos 
  (views DESC, user_id) 
  INCLUDE (title, thumbnail_url, duration, likes, is_hd);
```

These indexes provide:
- Efficient sorting by views
- Fast joins between videos and profiles
- Support for keyset pagination
- Covering indexes to reduce table lookups

### 2. Materialized View

```sql
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_top_videos_with_profiles AS
SELECT 
    v.*,
    jsonb_build_object(
        'id', p.id,
        'username', p.username,
        'avatar_url', p.avatar_url
    ) AS profiles
FROM 
    public.videos v
LEFT JOIN 
    public.profiles p ON p.id = v.user_id
ORDER BY 
    v.views DESC;
```

Benefits:
- Pre-computes the expensive join and JSON construction
- Eliminates per-query JSON serialization overhead
- Provides a denormalized view for fast access
- Refreshed periodically and on data changes

### 3. Optimized Query Functions

Two query functions were created:

1. **Offset Pagination** (`get_top_videos_with_profiles`):
   - Traditional LIMIT/OFFSET pagination
   - Uses the materialized view for performance
   - Includes total count and page metadata

2. **Keyset Pagination** (`get_top_videos_keyset`):
   - More efficient for large datasets
   - Avoids the performance penalty of large offsets
   - Provides next page markers for continuous scrolling

### 4. Automatic Refresh Mechanism

The materialized view is kept up-to-date through:

1. **Scheduled Refresh**:
   - Uses pg_cron to refresh every 5 minutes
   - Runs `REFRESH MATERIALIZED VIEW CONCURRENTLY` to avoid blocking reads

2. **Trigger-Based Refresh**:
   - Triggers on INSERT/UPDATE/DELETE to videos table
   - Triggers on UPDATE to profiles table
   - Uses pg_notify to queue refreshes asynchronously

### 5. Caching Strategy

The Edge Function implements:
- HTTP Cache-Control headers
- ETag-based conditional responses
- Stale-while-revalidate pattern
- Fallback to direct query if materialized view is unavailable

## Performance Impact

Expected improvements:
- Reduced database CPU usage by ~80%
- Lower I/O requirements
- Faster response times (especially for frequently accessed pages)
- Better scalability under high load
- Reduced database connection time

## Monitoring

A monitoring function `monitor_mv_performance()` provides insights into:
- Last refresh time
- Row count
- Size of the materialized view
- Index size

## Manual Operations

To manually refresh the materialized view:
```sql
SELECT public.refresh_top_videos_now();
```

This will refresh the view and return performance metrics.

## Fallback Mechanism

If the materialized view is unavailable or the query fails, the system will automatically fall back to a direct query using the original pattern, ensuring system resilience.
