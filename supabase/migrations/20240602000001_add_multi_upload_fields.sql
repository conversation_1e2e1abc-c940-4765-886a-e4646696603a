-- Add multi-upload fields to videos table
DO $$
BEGIN
    -- Check if the columns exist before adding them
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_schema = 'public'
                  AND table_name = 'videos'
                  AND column_name = 'is_part_of_multi_upload') THEN
        ALTER TABLE public.videos
        ADD COLUMN is_part_of_multi_upload BOOLEAN DEFAULT FALSE;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_schema = 'public'
                  AND table_name = 'videos'
                  AND column_name = 'is_first_in_multi_upload') THEN
        ALTER TABLE public.videos
        ADD COLUMN is_first_in_multi_upload BOOLEAN DEFAULT FALSE;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_schema = 'public'
                  AND table_name = 'videos'
                  AND column_name = 'total_parts_in_multi_upload') THEN
        ALTER TABLE public.videos
        ADD COLUMN total_parts_in_multi_upload INTEGER;
    END IF;
END
$$;

-- Create an index to quickly find videos that are part of multi-uploads
CREATE INDEX IF NOT EXISTS idx_videos_multi_upload
ON public.videos (is_part_of_multi_upload, is_first_in_multi_upload);

-- Create a function to get all videos in a multi-upload group
CREATE OR REPLACE FUNCTION get_multi_upload_videos(first_video_id UUID)
RETURNS SETOF public.videos AS $$
DECLARE
    collection_id UUID;
BEGIN
    -- First, check if the video is part of a collection
    SELECT cv.collection_id INTO collection_id
    FROM public.collection_videos cv
    WHERE cv.video_id = first_video_id
    LIMIT 1;

    IF collection_id IS NOT NULL THEN
        -- Return all videos in the collection, ordered by position
        RETURN QUERY
        SELECT v.*
        FROM public.videos v
        JOIN public.collection_videos cv ON v.id = cv.video_id
        WHERE cv.collection_id = collection_id
        ORDER BY cv.position;
    ELSE
        -- If not in a collection, just return the single video
        RETURN QUERY
        SELECT * FROM public.videos WHERE id = first_video_id;
    END IF;
END;
$$ LANGUAGE plpgsql;
