-- Add is_approved column to profiles table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'profiles'
    AND column_name = 'is_approved'
  ) THEN
    ALTER TABLE public.profiles
    ADD COLUMN is_approved BOOLEAN DEFAULT false;
  END IF;
END
$$;

-- Update RLS policy for videos table to check for user approval
DO $$
BEGIN
  -- Drop the existing policy if it exists
  DROP POLICY IF EXISTS "Authenticated users can insert videos" ON public.videos;
  
  -- Create the new policy that checks for approval
  CREATE POLICY "Approved users can insert videos"
    ON public.videos
    FOR INSERT
    WITH CHECK (
      auth.uid() = user_id AND 
      EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND is_approved = true
      )
    );
END
$$;

-- Create a function to approve a user
CREATE OR REPLACE FUNCTION public.approve_user(user_id uuid)
RETURNS void AS $$
BEGIN
  UPDATE public.profiles
  SET is_approved = true
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user is approved
CREATE OR REPLACE FUNCTION public.is_user_approved(user_id uuid)
RETURNS boolean AS $$
DECLARE
  is_approved boolean;
BEGIN
  SELECT p.is_approved INTO is_approved
  FROM public.profiles p
  WHERE p.id = user_id;
  
  RETURN COALESCE(is_approved, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user is an admin
CREATE OR REPLACE FUNCTION public.is_user_admin(user_id uuid)
RETURNS boolean AS $$
DECLARE
  is_admin boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = $1
    AND role = 'admin'
  ) INTO is_admin;
  
  RETURN COALESCE(is_admin, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
