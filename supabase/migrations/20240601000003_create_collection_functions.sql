-- Function to increment collection video count
CREATE OR REPLACE FUNCTION increment_collection_video_count(collection_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.collections
  SET video_count = video_count + 1,
      updated_at = now()
  WHERE id = collection_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrement collection video count
CREATE OR REPLACE FUNCTION decrement_collection_video_count(collection_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.collections
  SET video_count = GREATEST(0, video_count - 1),
      updated_at = now()
  WHERE id = collection_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reorder collection videos
CREATE OR REPLACE FUNCTION reorder_collection_videos(updates_json JSONB)
RETURNS void AS $$
DECLARE
  update_item JSONB;
BEGIN
  FOR update_item IN SELECT * FROM jsonb_array_elements(updates_json)
  LOOP
    UPDATE public.collection_videos
    SET position = (update_item->>'position')::INTEGER
    WHERE collection_id = (update_item->>'collection_id')::UUID
      AND video_id = (update_item->>'video_id')::UUID;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
