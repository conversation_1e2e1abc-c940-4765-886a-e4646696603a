-- Create collections table
CREATE TABLE IF NOT EXISTS public.collections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  thumbnail_url TEXT,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  is_public BOOLEAN DEFAULT true NOT NULL,
  video_count INTEGER DEFAULT 0 NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create collection_videos junction table
CREATE TABLE IF NOT EXISTS public.collection_videos (
  collection_id UUID NOT NULL REFERENCES public.collections(id) ON DELETE CASCADE,
  video_id UUID NOT NULL REFERENCES public.videos(id) ON DELETE CASCADE,
  position INTEGER NOT NULL DEFAULT 0,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  PRIMARY KEY (collection_id, video_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS collection_videos_collection_id_idx ON public.collection_videos (collection_id);
CREATE INDEX IF NOT EXISTS collection_videos_video_id_idx ON public.collection_videos (video_id);

-- Enable RLS on collections tables
ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collection_videos ENABLE ROW LEVEL SECURITY;

-- Create policies for collections
-- Users can view their own collections and public collections
CREATE POLICY "Users can view their own collections and public collections" 
ON public.collections
FOR SELECT 
USING (
  auth.uid() = user_id OR is_public = true
);

-- Users can insert their own collections
CREATE POLICY "Users can insert their own collections" 
ON public.collections
FOR INSERT 
TO authenticated
WITH CHECK (
  auth.uid() = user_id
);

-- Users can update their own collections
CREATE POLICY "Users can update their own collections" 
ON public.collections
FOR UPDATE 
TO authenticated
USING (
  auth.uid() = user_id
);

-- Users can delete their own collections
CREATE POLICY "Users can delete their own collections" 
ON public.collections
FOR DELETE 
TO authenticated
USING (
  auth.uid() = user_id
);

-- Create policies for collection_videos
-- Users can view videos in their collections and public collections
CREATE POLICY "Users can view videos in their collections and public collections" 
ON public.collection_videos
FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = collection_id AND (c.user_id = auth.uid() OR c.is_public = true)
  )
);

-- Users can insert videos into their own collections
CREATE POLICY "Users can insert videos into their own collections" 
ON public.collection_videos
FOR INSERT 
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = collection_id AND c.user_id = auth.uid()
  )
);

-- Users can update videos in their own collections
CREATE POLICY "Users can update videos in their own collections" 
ON public.collection_videos
FOR UPDATE 
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = collection_id AND c.user_id = auth.uid()
  )
);

-- Users can delete videos from their own collections
CREATE POLICY "Users can delete videos from their own collections" 
ON public.collection_videos
FOR DELETE 
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = collection_id AND c.user_id = auth.uid()
  )
);
