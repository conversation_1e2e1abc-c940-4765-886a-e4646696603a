# Supabase Security Fixes

This document explains the security fixes implemented in the recent migrations.

## 1. Role Mutable Search Path Fixes

The migration `20240801000001_fix_search_path_issues.sql` addresses the "role mutable search_path" warnings for several functions in the database. This is a security best practice to prevent potential SQL injection attacks.

### What is the "role mutable search_path" issue?

When a function is created without explicitly setting the search path, it inherits the search path of the user who calls it. This can be a security risk for functions with `SECURITY DEFINER` because they run with the privileges of the function owner. An attacker could potentially manipulate the search path to execute malicious code.

### How was it fixed?

All functions with `SECURITY DEFINER` have been updated to include `SET search_path = ''` or `SET search_path = public`. This ensures that the search path is explicitly set and cannot be manipulated by the caller.

The following functions were fixed:
- `approve_user`
- `is_user_approved`
- `is_user_admin`
- `increment_comment_likes`
- `decrement_comment_likes`
- `handle_new_user`
- `create_missing_profiles`
- `increment_collection_video_count`
- `decrement_collection_video_count`
- `reorder_collection_videos`

## 2. Automatic Admin Assignment

A new function `handle_new_user_admin_check` has been created to automatically make users with specific emails (jay<PERSON><PERSON>ght@gmail.<NAME_EMAIL>) admins when they sign up.

This function:
1. Checks if a new user's email matches one of the predefined admin emails
2. If it does, inserts a record into the `user_roles` table with the role 'admin'
3. Also automatically approves the user by setting `is_approved = true` in their profile

A trigger `on_auth_user_created_admin_check` has been created to call this function whenever a new user is created.

## 3. Compromised Password Checking

The migration `20240801000002_enable_compromised_password_checking.sql` enables the compromised password checking feature in Supabase Auth. This feature checks passwords against the HaveIBeenPwned.org database to prevent users from using passwords that have been compromised in data breaches.

## How to Apply These Migrations

### Using Supabase CLI

1. Make sure you have the Supabase CLI installed:
   ```
   npm install -g supabase
   ```

2. Log in to your Supabase account:
   ```
   supabase login
   ```

3. Link your project (if not already linked):
   ```
   supabase link --project-ref your-project-ref
   ```
   Replace `your-project-ref` with your actual Supabase project reference ID.

4. Apply the migrations:
   ```
   supabase db push
   ```

### Using the Supabase Dashboard

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Select your project
3. Navigate to the SQL Editor
4. Copy and paste the contents of each migration file
5. Execute the SQL statements in order:
   - First: `20240801000001_fix_search_path_issues.sql`
   - Second: `20240801000002_enable_compromised_password_checking.sql`

## Verification

After applying these migrations, you can verify that they were successful by:

1. Checking that the "role mutable search_path" warnings no longer appear in the Supabase dashboard
2. Creating a new user with one of the admin emails and verifying that they are automatically made an admin
3. Attempting to sign up with a known compromised password and verifying that it is rejected
