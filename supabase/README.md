# Supabase Migrations

This directory contains SQL migrations for setting up the database schema for the video platform.

## Migrations

1. `20240601000001_create_categories_table.sql` - Creates the categories table and adds default categories
2. `20240601000002_create_collections_tables.sql` - Creates the collections and collection_videos tables
3. `20240601000003_create_collection_functions.sql` - Creates functions for managing collection video counts
4. `20240601000004_update_videos_table.sql` - Updates the videos table to include the category field

## How to Apply Migrations

### Using Supabase CLI

1. Make sure you have the Supabase CLI installed:
   ```
   npm install -g supabase
   ```

2. Log in to your Supabase account:
   ```
   supabase login
   ```

3. Link your project (if not already linked):
   ```
   supabase link --project-ref your-project-ref
   ```

4. Apply the migrations:
   ```
   supabase db push
   ```

### Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to your project
3. Navigate to the SQL Editor
4. Copy and paste each migration file's contents
5. Execute the SQL statements in order

## Manual Database Setup

If you prefer to set up the database manually, you can run the following SQL statements in the Supabase SQL Editor:

1. Create the categories table:
   ```sql
   CREATE TABLE IF NOT EXISTS public.categories (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     name TEXT NOT NULL,
     slug TEXT NOT NULL UNIQUE,
     description TEXT,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
   );
   ```

2. Create the collections table:
   ```sql
   CREATE TABLE IF NOT EXISTS public.collections (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     name TEXT NOT NULL,
     description TEXT,
     thumbnail_url TEXT,
     user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
     is_public BOOLEAN DEFAULT true NOT NULL,
     video_count INTEGER DEFAULT 0 NOT NULL,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
   );
   ```

3. Create the collection_videos table:
   ```sql
   CREATE TABLE IF NOT EXISTS public.collection_videos (
     collection_id UUID NOT NULL REFERENCES public.collections(id) ON DELETE CASCADE,
     video_id UUID NOT NULL REFERENCES public.videos(id) ON DELETE CASCADE,
     position INTEGER NOT NULL DEFAULT 0,
     added_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
     PRIMARY KEY (collection_id, video_id)
   );
   ```

4. Add the category field to the videos table:
   ```sql
   ALTER TABLE public.videos
   ADD COLUMN IF NOT EXISTS category TEXT DEFAULT 'uncategorized' NOT NULL;
   ```

5. Create the necessary functions and policies by running the SQL from the migration files.

## Troubleshooting

If you encounter any issues with the migrations, check the following:

1. Make sure your Supabase project has the `uuid-ossp` extension enabled
2. Verify that the `profiles` and `videos` tables already exist
3. Check that you have the necessary permissions to create tables and functions
