// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface QueryStats {
  query: string;
  calls: number;
  total_time: number;
  mean_time: number;
  rows: number;
}

interface SlowQuery {
  query: string;
  duration: number;
  calls: number;
  rows_returned: number;
}

interface DatabaseStats {
  connection_stats: {
    active_connections: number;
    idle_connections: number;
    max_connections: number;
    connection_utilization_percent: number;
  };
  table_stats: {
    table_name: string;
    live_rows: number;
    dead_rows: number;
    size_bytes: number;
    index_size_bytes: number;
    last_vacuum: string | null;
    last_analyze: string | null;
  }[];
  index_stats: {
    index_name: string;
    table_name: string;
    size_bytes: number;
    index_scans: number;
  }[];
  slow_queries: SlowQuery[];
  cache_hit_ratio: number;
  query_stats: QueryStats[];
}

serve(async (req) => {
  try {
    // Create a Supabase client with the Admin key
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header is required' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Extract the token
    const token = authHeader.replace('Bearer ', '')

    // Create a Supabase client with the service_role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verify the token
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token)
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid token' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Check if user has admin role
    const { data: roles, error: rolesError } = await supabaseAdmin
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .single()

    if (rolesError || !roles || roles.role !== 'admin') {
      return new Response(
        JSON.stringify({ error: 'Unauthorized. Admin role required.' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Get connection stats
    const { data: connectionStats, error: connectionError } = await supabaseAdmin.rpc('get_connection_stats')
    if (connectionError) throw connectionError

    // Get table stats
    const { data: tableStats, error: tableError } = await supabaseAdmin.rpc('get_table_stats')
    if (tableError) throw tableError

    // Get index stats
    const { data: indexStats, error: indexError } = await supabaseAdmin.rpc('get_index_stats')
    if (indexError) throw indexError

    // Get slow queries
    const { data: slowQueries, error: slowQueryError } = await supabaseAdmin.rpc('get_slow_queries')
    if (slowQueryError) throw slowQueryError

    // Get cache hit ratio
    const { data: cacheHitRatio, error: cacheError } = await supabaseAdmin.rpc('get_cache_hit_ratio')
    if (cacheError) throw cacheError

    // Get query stats
    const { data: queryStats, error: queryError } = await supabaseAdmin.rpc('get_query_stats')
    if (queryError) throw queryError

    // Compile all stats
    const stats: DatabaseStats = {
      connection_stats: connectionStats,
      table_stats: tableStats,
      index_stats: indexStats,
      slow_queries: slowQueries,
      cache_hit_ratio: cacheHitRatio,
      query_stats: queryStats
    }

    return new Response(
      JSON.stringify(stats),
      { headers: { 'Content-Type': 'application/json' } },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } },
    )
  }
})

// To deploy:
// supabase functions deploy monitor-db --project-ref <project-ref>
