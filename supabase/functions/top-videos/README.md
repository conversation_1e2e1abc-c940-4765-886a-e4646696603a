# Top Videos API Optimization

This implementation provides an optimized API for fetching top videos with user profiles. It includes several performance enhancements:

1. Optimized database indexes
2. Materialized view for pre-computed results
3. Efficient pagination options (offset and keyset)
4. HTTP caching with ETag support
5. Scheduled refresh of materialized view

## Database Optimizations

The following database optimizations have been implemented:

### Indexes

```sql
-- Index for sorting by views
CREATE INDEX IF NOT EXISTS idx_videos_views_desc ON public.videos (views DESC);

-- Index for the join between videos and profiles
CREATE INDEX IF NOT EXISTS idx_videos_user_id ON public.videos (user_id);

-- Composite index for both sorting and joining
CREATE INDEX IF NOT EXISTS idx_videos_views_user_id ON public.videos (views DESC, user_id);

-- Index for timestamp-based filtering
CREATE INDEX IF NOT EXISTS idx_videos_created_at ON public.videos (created_at DESC);
```

### Materialized View

A materialized view `mv_top_videos_with_profiles` has been created to pre-compute the join between videos and profiles, along with the JSON structure needed for the API response.

```sql
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_top_videos_with_profiles AS
SELECT 
    v.*,
    jsonb_build_object(
        'id', p.id,
        'username', p.username,
        'avatar_url', p.avatar_url
    ) AS profiles
FROM 
    public.videos v
LEFT JOIN 
    public.profiles p ON p.id = v.user_id
ORDER BY 
    v.views DESC;
```

This view is automatically refreshed:
1. Every 5 minutes via a scheduled job
2. When videos are inserted, updated, or deleted
3. When profiles are updated

## API Usage

### Edge Function Endpoint

The Edge Function provides a cached API endpoint for accessing top videos:

```
https://[YOUR_PROJECT_REF].supabase.co/functions/v1/top-videos
```

### Query Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| limit | number | Number of videos to return | 10 |
| offset | number | Offset for pagination | 0 |
| use_keyset | boolean | Use keyset pagination instead of offset | false |
| last_views | number | Last video's view count (for keyset pagination) | null |
| last_id | string | Last video's ID (for keyset pagination) | null |

### Examples

#### Offset Pagination

```
/functions/v1/top-videos?limit=20&offset=0
```

#### Keyset Pagination

Initial request:
```
/functions/v1/top-videos?use_keyset=true
```

Follow-up request (using values from previous response):
```
/functions/v1/top-videos?use_keyset=true&last_views=1000&last_id=123e4567-e89b-12d3-a456-426614174000
```

### Response Format

```json
{
  "total_result_set": 150,
  "page_total": 10,
  "body": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "title": "Example Video",
      "views": 1500,
      "profiles": {
        "id": "456e7890-e12b-34d5-a678-426614174000",
        "username": "example_user",
        "avatar_url": "https://example.com/avatar.jpg"
      }
    },
    // More videos...
  ],
  "next_views": 1000,
  "next_id": "789e0123-e45b-67d8-a901-426614174000"
}
```

## Caching

The API implements the following caching strategy:

1. **HTTP Cache Headers**:
   - `Cache-Control: public, max-age=300, stale-while-revalidate=60`
   - `ETag` based on response content

2. **Client-Side Caching**:
   - Clients should respect the Cache-Control headers
   - Clients should send `If-None-Match` header with the ETag value to receive 304 Not Modified responses when appropriate

3. **Database Caching**:
   - Materialized view refreshes every 5 minutes
   - Triggers refresh the view when data changes

## Performance Considerations

1. **Keyset vs. Offset Pagination**:
   - For large datasets, use keyset pagination (`use_keyset=true`)
   - Offset pagination becomes slower as the offset increases

2. **Cache Invalidation**:
   - The materialized view is refreshed automatically
   - Edge Function respects HTTP caching standards

3. **Monitoring**:
   - Monitor the performance of the materialized view refresh
   - Adjust refresh frequency based on data change patterns
