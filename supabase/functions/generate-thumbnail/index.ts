// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface ThumbnailRequest {
  videoId: string;
  videoUrl: string;
  timeOffset?: number; // Time in seconds to capture thumbnail
  width?: number;
  height?: number;
}

serve(async (req) => {
  try {
    // Parse request body
    const { videoId, videoUrl, timeOffset = 5, width = 640, height = 360 } = await req.json() as ThumbnailRequest;

    // Validate required parameters
    if (!videoId || !videoUrl) {
      return new Response(
        JSON.stringify({ error: 'videoId and videoUrl are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Create a Supabase client with the service_role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Verify the video exists in the database
    const { data: video, error: videoError } = await supabaseAdmin
      .from('videos')
      .select('id, user_id')
      .eq('id', videoId)
      .single();

    if (videoError || !video) {
      return new Response(
        JSON.stringify({ error: 'Video not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Verify the user making the request is the owner of the video
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header is required' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Extract the token
    const token = authHeader.replace('Bearer ', '');

    // Verify the token
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid token' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Check if the user is the owner of the video
    if (user.id !== video.user_id) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized. You do not own this video.' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Generate a unique filename for the thumbnail
    const timestamp = new Date().getTime();
    const thumbnailFilename = `${videoId}_${timestamp}.jpg`;
    const thumbnailPath = `${user.id}/${thumbnailFilename}`;

    // In a real implementation, we would use FFmpeg to generate the thumbnail
    // For this example, we'll simulate the process by downloading the video
    // and generating a thumbnail using canvas or another method
    
    // For demonstration purposes, we'll just use a placeholder image
    // In a real implementation, you would:
    // 1. Download the video (or a portion of it)
    // 2. Use FFmpeg to extract a frame at the specified timeOffset
    // 3. Resize the frame to the specified width and height
    // 4. Upload the resulting image to Supabase Storage

    // Simulate thumbnail generation (in a real implementation, this would be actual processing)
    console.log(`Generating thumbnail for video ${videoId} at ${timeOffset} seconds`);
    
    // Create a placeholder thumbnail (in a real implementation, this would be the actual thumbnail)
    const placeholderThumbnail = new Uint8Array([
      // ... binary data for a JPEG image would go here
      // For demonstration, we're using an empty array
    ]);

    // Upload the thumbnail to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabaseAdmin
      .storage
      .from('thumbnails')
      .upload(thumbnailPath, placeholderThumbnail, {
        contentType: 'image/jpeg',
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {
      return new Response(
        JSON.stringify({ error: `Failed to upload thumbnail: ${uploadError.message}` }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get the public URL for the thumbnail
    const { data: { publicUrl } } = supabaseAdmin
      .storage
      .from('thumbnails')
      .getPublicUrl(thumbnailPath);

    // Update the video record with the new thumbnail URL
    const { data: updateData, error: updateError } = await supabaseAdmin
      .from('videos')
      .update({ thumbnail_url: publicUrl })
      .eq('id', videoId);

    if (updateError) {
      return new Response(
        JSON.stringify({ error: `Failed to update video record: ${updateError.message}` }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        thumbnailUrl: publicUrl,
        message: 'Thumbnail generated successfully'
      }),
      { headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
})

// To deploy:
// supabase functions deploy generate-thumbnail --project-ref <project-ref>
