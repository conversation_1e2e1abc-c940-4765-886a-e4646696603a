<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Video Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .video-container {
            margin-bottom: 30px;
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .video-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .video-description {
            color: #666;
            margin-bottom: 15px;
        }
        video {
            width: 100%;
            max-height: 500px;
            border-radius: 4px;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .thumbnail {
            max-width: 320px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Supabase Video Test</h1>
    
    <div id="videoContainers"></div>

    <script>
        // Video URLs from your Supabase storage
        const videos = [
            {
                title: "Appreciate a true Nyash Glory Widely Opened and natured by a Queen 1",
                description: "Nyash is Life",
                videoUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746147162269-4_5802976136631687807.MP4",
                thumbnailUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746147185564-Screenshot_20250502-005053.jpg"
            },
            {
                title: "Just Enjoy the Exotic View 1",
                description: "Beauty of Life",
                videoUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746151672757-1732811566180.mp4",
                thumbnailUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746151714983-Screenshot_20250502-020728.jpg"
            },
            {
                title: "Chantelle Kenyan Hottie 2",
                description: "Chantelle displays her Talent",
                videoUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746083876786-VID_20250127_125639_549.mp4",
                thumbnailUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746083914163-thumbnail-1746083872518.jpg"
            }
        ];

        const videoContainers = document.getElementById('videoContainers');

        // Create video elements for each video
        videos.forEach((video, index) => {
            const container = document.createElement('div');
            container.className = 'video-container';
            
            const title = document.createElement('div');
            title.className = 'video-title';
            title.textContent = video.title;
            
            const description = document.createElement('div');
            description.className = 'video-description';
            description.textContent = video.description;
            
            // Add thumbnail
            const thumbnail = document.createElement('img');
            thumbnail.className = 'thumbnail';
            thumbnail.src = video.thumbnailUrl;
            thumbnail.alt = `Thumbnail for ${video.title}`;
            
            // Create video element
            const videoElement = document.createElement('video');
            videoElement.controls = true;
            videoElement.preload = 'metadata';
            videoElement.src = video.videoUrl;
            
            // Status element to show loading/success/error
            const status = document.createElement('div');
            status.className = 'status';
            status.textContent = 'Loading...';
            
            // Add event listeners to check if video loads successfully
            videoElement.addEventListener('loadeddata', () => {
                status.textContent = 'Video loaded successfully!';
                status.className = 'status success';
            });
            
            videoElement.addEventListener('error', (e) => {
                status.textContent = `Error loading video: ${e.target.error ? e.target.error.message : 'Unknown error'}`;
                status.className = 'status error';
                console.error('Video error:', e);
            });
            
            // Append elements to container
            container.appendChild(title);
            container.appendChild(description);
            container.appendChild(thumbnail);
            container.appendChild(videoElement);
            container.appendChild(status);
            
            // Append container to page
            videoContainers.appendChild(container);
        });
    </script>
</body>
</html>
