<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Top Videos Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .video-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .thumbnail {
            width: 100%;
            height: 180px;
            object-fit: cover;
        }
        .video-info {
            padding: 15px;
        }
        .video-title {
            font-weight: bold;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .video-meta {
            display: flex;
            align-items: center;
            color: #666;
            font-size: 14px;
        }
        .username {
            margin-left: 5px;
        }
        .views {
            margin-left: auto;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            gap: 10px;
        }
        button {
            padding: 8px 16px;
            background-color: #0070f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .tab {
            padding: 8px 16px;
            background-color: #f0f0f0;
            border-radius: 4px;
            cursor: pointer;
        }
        .tab.active {
            background-color: #0070f3;
            color: white;
        }
        .loading {
            text-align: center;
            margin: 50px 0;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <h1>Top Videos</h1>
    
    <div class="tabs">
        <div class="tab active" data-type="offset">Offset Pagination</div>
        <div class="tab" data-type="keyset">Keyset Pagination</div>
    </div>
    
    <div id="videos-container" class="video-grid"></div>
    
    <div class="pagination">
        <button id="prev-btn" disabled>Previous</button>
        <button id="next-btn">Next</button>
    </div>

    <script>
        // Replace with your Supabase anon key
        const SUPABASE_ANON_KEY = 'YOUR_ANON_KEY';
        const SUPABASE_URL = 'https://vsnsglgyapexhwyfylic.supabase.co';
        
        // Pagination state
        let currentPage = 0;
        let pageSize = 6;
        let totalItems = 0;
        let paginationType = 'offset';
        let lastViews = null;
        let lastId = null;
        
        // DOM elements
        const videosContainer = document.getElementById('videos-container');
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const tabs = document.querySelectorAll('.tab');
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            loadVideos();
            
            // Tab click handlers
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    paginationType = tab.dataset.type;
                    resetPagination();
                    loadVideos();
                });
            });
            
            // Pagination handlers
            prevBtn.addEventListener('click', () => {
                if (currentPage > 0) {
                    currentPage--;
                    loadVideos();
                }
            });
            
            nextBtn.addEventListener('click', () => {
                if (paginationType === 'offset') {
                    currentPage++;
                    loadVideos();
                } else {
                    // For keyset pagination, we use the last item's values
                    loadVideos();
                }
            });
        });
        
        function resetPagination() {
            currentPage = 0;
            lastViews = null;
            lastId = null;
            prevBtn.disabled = true;
        }
        
        async function loadVideos() {
            showLoading();
            
            try {
                let url;
                
                if (paginationType === 'offset') {
                    url = `${SUPABASE_URL}/rest/v1/rpc/get_public_top_videos?video_limit=${pageSize}&video_offset=${currentPage * pageSize}`;
                } else {
                    url = `${SUPABASE_URL}/rest/v1/rpc/get_public_top_videos_keyset?page_size=${pageSize}`;
                    if (lastViews !== null && lastId !== null) {
                        url += `&last_views=${lastViews}&last_id=${lastId}`;
                    }
                }
                
                const response = await fetch(url, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (paginationType === 'offset') {
                    renderVideos(data.body);
                    totalItems = data.total_result_set;
                    prevBtn.disabled = currentPage === 0;
                    nextBtn.disabled = (currentPage + 1) * pageSize >= totalItems;
                } else {
                    renderVideos(data.body);
                    totalItems = data.total_result_set;
                    prevBtn.disabled = currentPage === 0;
                    
                    // Update last values for keyset pagination
                    if (data.next_views && data.next_id) {
                        lastViews = data.next_views;
                        lastId = data.next_id;
                        nextBtn.disabled = false;
                    } else {
                        nextBtn.disabled = true;
                    }
                    
                    // Increment page counter for keyset pagination too (just for UI)
                    if (lastViews !== null && lastId !== null) {
                        currentPage++;
                    }
                }
            } catch (error) {
                console.error('Error loading videos:', error);
                videosContainer.innerHTML = `<p>Error loading videos: ${error.message}</p>`;
            }
        }
        
        function renderVideos(videos) {
            videosContainer.innerHTML = '';
            
            if (!videos || videos.length === 0) {
                videosContainer.innerHTML = '<p>No videos found</p>';
                return;
            }
            
            videos.forEach(video => {
                const videoCard = document.createElement('div');
                videoCard.className = 'video-card';
                
                videoCard.innerHTML = `
                    <img class="thumbnail" src="${video.thumbnail_url || 'https://via.placeholder.com/300x180?text=No+Thumbnail'}" alt="${video.title}">
                    <div class="video-info">
                        <div class="video-title">${video.title}</div>
                        <div class="video-meta">
                            <span class="username">${video.profile?.username || 'Unknown'}</span>
                            <span class="views">${video.views} views</span>
                        </div>
                    </div>
                `;
                
                videoCard.addEventListener('click', () => {
                    window.open(video.video_url, '_blank');
                });
                
                videosContainer.appendChild(videoCard);
            });
        }
        
        function showLoading() {
            videosContainer.innerHTML = '<div class="loading">Loading videos...</div>';
        }
    </script>
</body>
</html>
