// Simple script to fetch videos from Supabase
import { createClient } from '@supabase/supabase-js'

// Your Supabase URL and anon key
const supabaseUrl = 'https://vsnsglgyapexhwyfylic.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzbnNnbGd5YXBleGh3eWZ5bGljIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExOTEsImV4cCI6MjA2MDgzNzE5MX0.6CQWpMT14h2kaIOk1_LMECuJrfRdmiGRo3vGyEDW9tM'

const supabase = createClient(supabaseUrl, supabaseKey)

async function fetchData() {
  try {
    // Fetch 5 videos from the videos table
    const { data: videos, error: videosError } = await supabase
      .from('videos')
      .select('*')
      .limit(5)

    if (videosError) {
      throw videosError
    }

    console.log('Videos fetched successfully:')
    console.log(JSON.stringify(videos, null, 2))

    // Fetch profiles
    console.log('\nFetching profiles...')
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(3)

    if (profilesError) {
      console.log('Error fetching profiles:', profilesError.message)
    } else {
      console.log('Profiles fetched successfully:')
      console.log(JSON.stringify(profiles, null, 2))
    }

    // List all tables in the database
    console.log('\nListing all tables...')
    const { data: tables, error: tablesError } = await supabase
      .rpc('list_tables')
      .select()

    if (tablesError) {
      console.log('Error listing tables (this is expected if the function does not exist):', tablesError.message)

      // Alternative approach to list tables
      console.log('Trying to fetch schema information...')
      const { data: schema, error: schemaError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')

      if (schemaError) {
        console.log('Error fetching schema information:', schemaError.message)
      } else {
        console.log('Tables in the database:')
        console.log(JSON.stringify(schema, null, 2))
      }
    } else {
      console.log('Tables in the database:')
      console.log(JSON.stringify(tables, null, 2))
    }

    return { videos, profiles }
  } catch (error) {
    console.error('Error fetching data:', error.message)
  }
}

fetchData()
