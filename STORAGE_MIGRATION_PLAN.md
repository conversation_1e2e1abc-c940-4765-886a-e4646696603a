# 📁 Storage Migration Plan: Supabase → Namecheap

## 🎯 Overview

This plan will help you migrate your video and thumbnail storage from Supabase to Namecheap hosting while maintaining all functionality.

## 📊 Current Storage Analysis

### What's Currently Using Supabase Storage:
- **Video files** (`.mp4`, `.mov`, etc.) in `videos` bucket
- **Thumbnail images** (`.jpg`, `.png`) in `thumbnails` bucket
- **Upload system** with chunked uploads for large files
- **Automatic thumbnail generation**
- **File validation and progress tracking**

### Current URL Structure:
```
Videos: https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/[user-id]/[filename]
Thumbnails: https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/[user-id]/[filename]
```

## 🚀 Migration Strategy

### Phase 1: Setup Namecheap Storage Structure
1. **Create storage directories** on Namecheap hosting
2. **Set up upload endpoint** (PHP script for file uploads)
3. **Configure proper permissions** and security

### Phase 2: Update Application Code
1. **Replace Supabase storage calls** with direct HTTP uploads
2. **Update URL generation** for new storage location
3. **Maintain existing file validation** and progress tracking

### Phase 3: Data Migration
1. **Download existing files** from Supabase
2. **Upload to Namecheap** hosting
3. **Update database URLs** to point to new location

### Phase 4: Testing & Cleanup
1. **Test all upload/download functionality**
2. **Verify video playback** and thumbnail display
3. **Clean up old Supabase storage** (optional)

## 📁 New Storage Structure on Namecheap

```
public_html/
├── media/
│   ├── videos/
│   │   ├── [user-id]/
│   │   │   ├── [video-id].[ext]
│   │   │   └── ...
│   │   └── ...
│   ├── thumbnails/
│   │   ├── [user-id]/
│   │   │   ├── [video-id].[ext]
│   │   │   └── ...
│   │   └── ...
│   └── uploads/
│       ├── upload.php
│       ├── validate.php
│       └── thumbnail-generator.php
├── .htaccess (updated with media rules)
└── [your app files]
```

## 🔧 Technical Implementation

### New URL Structure:
```
Videos: https://yourdomain.com/media/videos/[user-id]/[filename]
Thumbnails: https://yourdomain.com/media/thumbnails/[user-id]/[filename]
Upload Endpoint: https://yourdomain.com/media/uploads/upload.php
```

### Key Changes Required:
1. **Upload Store** (`src/stores/uploadStore.ts`) - Replace Supabase calls
2. **Media Utils** (`src/utils/mediaUtils.ts`) - Update URL validation
3. **Video Components** - Update source URLs
4. **Environment Variables** - Add new storage configuration

## 📋 Implementation Steps

### Step 1: Create Upload Infrastructure
- PHP upload script with chunked upload support
- File validation and security checks
- Progress tracking endpoint
- Thumbnail generation script

### Step 2: Update Frontend Code
- New storage service abstraction
- Replace Supabase storage calls
- Update URL generation functions
- Maintain existing UI/UX

### Step 3: Database Migration
- Update existing video/thumbnail URLs
- Batch update script for URL conversion
- Backup before migration

## 🔒 Security Considerations

### File Upload Security:
- **File type validation** (whitelist approach)
- **File size limits** (configurable)
- **User authentication** verification
- **Directory traversal** protection
- **Malware scanning** (basic checks)

### Access Control:
- **Direct file access** (public URLs)
- **Hotlink protection** (optional)
- **Rate limiting** for uploads
- **User quota management**

## 📈 Benefits of Migration

### Cost Savings:
- **No Supabase storage fees** beyond free tier
- **Unlimited storage** (within hosting plan limits)
- **No bandwidth charges** for file serving

### Performance:
- **Direct file serving** (no API overhead)
- **CDN integration** possible with Namecheap
- **Local storage** (same server as app)

### Control:
- **Full file system access**
- **Custom upload logic**
- **Backup control**
- **No vendor lock-in**

## ⚠️ Considerations & Limitations

### Namecheap Hosting Limits:
- **File size limits** (typically 50-100MB per file)
- **Upload timeout** (may need chunked uploads)
- **Storage space** (depends on hosting plan)
- **Bandwidth limits** (check your plan)

### Technical Challenges:
- **PHP upload handling** (more complex than Supabase)
- **Progress tracking** (requires custom implementation)
- **Error handling** (need robust error management)
- **Concurrent uploads** (may need queuing)

## 🛠️ Tools & Scripts Needed

1. **PHP Upload Handler** - Secure file upload processing
2. **Progress Tracker** - Real-time upload progress
3. **Thumbnail Generator** - Server-side image processing
4. **Migration Script** - Download and re-upload existing files
5. **URL Updater** - Batch update database URLs
6. **Validation Service** - File type and security checks

## 📅 Estimated Timeline

- **Phase 1** (Setup): 1-2 days
- **Phase 2** (Code Updates): 2-3 days  
- **Phase 3** (Data Migration): 1 day
- **Phase 4** (Testing): 1-2 days

**Total: 5-8 days** (depending on data volume)

## 💾 Backup Strategy

### Before Migration:
1. **Export all video metadata** from database
2. **Download all files** from Supabase (backup)
3. **Document current URLs** for rollback

### During Migration:
1. **Incremental uploads** to Namecheap
2. **Verify each file** after upload
3. **Keep Supabase active** until complete

## 🔄 Rollback Plan

If migration fails:
1. **Restore original URLs** in database
2. **Keep Supabase storage** active
3. **Remove Namecheap files** if needed
4. **Revert code changes**

## 📞 Next Steps

1. **Review hosting plan limits** with Namecheap
2. **Estimate storage requirements** (current usage)
3. **Plan maintenance window** for migration
4. **Prepare backup strategy**
5. **Start with Phase 1 implementation**

Would you like me to proceed with creating the implementation files for this migration?
