# 📁 Storage Migration Guide: Supabase → Namecheap

## 🎯 Quick Start

This guide will help you migrate your video and thumbnail storage from Supabase to Namecheap hosting in 4 phases.

## 📋 Prerequisites

- ✅ Namecheap hosting with cPanel access
- ✅ Domain connected to hosting
- ✅ At least 2GB free storage space
- ✅ PHP 7.4+ with file upload support
- ✅ Current Supabase project access

## 🚀 Phase 1: Setup Storage Infrastructure

### Step 1.1: Upload PHP Files to Namecheap

1. **Access cPanel File Manager**
2. **Navigate to your domain root** (usually `public_html/`)
3. **Create media directory structure**:
   ```
   public_html/
   ├── media/
   │   ├── uploads/
   │   ├── videos/
   │   └── thumbnails/
   ```

4. **Upload these files to `public_html/media/uploads/`**:
   - `upload.php` (main upload handler)
   - `health.php` (health check endpoint)
   - `.htaccess` (security and optimization)

### Step 1.2: Set Directory Permissions

In cPanel File Manager:
1. **Right-click on `media/` folder** → Permissions
2. **Set to 755** (rwxr-xr-x)
3. **Apply to subdirectories**: ✅ Check this option
4. **Click "Change Permissions"**

### Step 1.3: Test Storage Setup

Visit: `https://yourdomain.com/media/uploads/health.php`

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00+00:00",
  "checks": {
    "upload_dir_exists": true,
    "upload_dir_writable": true,
    "videos_dir_exists": true,
    "videos_dir_writable": true,
    "thumbnails_dir_exists": true,
    "thumbnails_dir_writable": true
  }
}
```

## 🔧 Phase 2: Update Application Code

### Step 2.1: Add Environment Variable

Add to your `.env` file:
```env
VITE_STORAGE_BASE_URL=https://yourdomain.com
```

### Step 2.2: Update Upload Store

Replace your current upload store with the new Namecheap version:

```bash
# Backup current upload store
cp src/stores/uploadStore.ts src/stores/uploadStore.backup.ts

# Replace with Namecheap version
cp src/stores/uploadStoreNamecheap.ts src/stores/uploadStore.ts
```

### Step 2.3: Update Media Utils

Update `src/utils/mediaUtils.ts` to handle both Supabase and Namecheap URLs:

```typescript
// Add this function to handle URL validation
export const validateStorageUrl = (url: string): string => {
  // Handle Namecheap URLs
  if (url.includes(window.location.origin)) {
    return url;
  }
  
  // Handle legacy Supabase URLs
  if (url.includes('supabase.co')) {
    return url;
  }
  
  // Return placeholder for invalid URLs
  return getPlaceholderThumbnail();
};
```

### Step 2.4: Test Upload Functionality

1. **Build and deploy** your updated application
2. **Test video upload** with a small file
3. **Verify file appears** in `media/videos/[user-id]/`
4. **Check video playback** works correctly

## 📦 Phase 3: Migrate Existing Data

### Step 3.1: Prepare Migration Script

```bash
# Install dependencies for migration script
npm install form-data node-fetch

# Set environment variables
export NAMECHEAP_DOMAIN=yourdomain.com
export VITE_SUPABASE_URL=your_supabase_url
export VITE_SUPABASE_ANON_KEY=your_supabase_key
```

### Step 3.2: Run Migration

```bash
# Run the migration script
node scripts/migrate-storage.js
```

The script will:
- ✅ Download all videos from Supabase
- ✅ Upload them to Namecheap hosting
- ✅ Update database URLs
- ✅ Clean up temporary files

### Step 3.3: Monitor Migration Progress

Watch the console output for:
- **Download progress** for each file
- **Upload success/failure** messages
- **Database update** confirmations
- **Final summary** with success/failure counts

## ✅ Phase 4: Testing & Cleanup

### Step 4.1: Verify Migration

1. **Check video playback**:
   - Visit your video pages
   - Ensure videos load and play correctly
   - Verify thumbnails display properly

2. **Test new uploads**:
   - Upload a new video
   - Confirm it uses Namecheap storage
   - Check file appears in correct directory

3. **Check database URLs**:
   ```sql
   SELECT id, title, video_url, thumbnail_url 
   FROM videos 
   WHERE video_url LIKE '%yourdomain.com%' 
   LIMIT 5;
   ```

### Step 4.2: Performance Testing

1. **Video loading speed**: Compare before/after
2. **Upload performance**: Test with various file sizes
3. **Concurrent uploads**: Test multiple users uploading

### Step 4.3: Cleanup (Optional)

Once everything works correctly:

1. **Remove Supabase storage** (if desired):
   - Delete files from Supabase buckets
   - Cancel Supabase storage plan

2. **Remove old code**:
   - Delete `src/stores/uploadStore.backup.ts`
   - Remove Supabase storage imports

## 🔒 Security Checklist

- ✅ **File type validation** active
- ✅ **File size limits** enforced
- ✅ **Directory permissions** correct (755)
- ✅ **PHP file access** restricted
- ✅ **User authentication** required for uploads
- ✅ **No directory browsing** enabled

## 📊 Monitoring & Maintenance

### Health Checks

Set up monitoring for:
- `https://yourdomain.com/media/uploads/health.php`
- Disk space usage in cPanel
- Upload error rates

### Backup Strategy

1. **Regular backups** of media directory
2. **Database backups** with URL references
3. **Monitor storage usage** in cPanel

## 🐛 Troubleshooting

### Common Issues

**Upload fails with "413 Request Entity Too Large"**
- Increase PHP limits in `.htaccess`
- Contact Namecheap support for server limits

**Videos don't play**
- Check file permissions (644 for files)
- Verify MIME types in server configuration
- Test direct file URL access

**Slow upload speeds**
- Enable chunked uploads for large files
- Check network conditions
- Consider CDN for better performance

**Database update fails**
- Check Supabase connection
- Verify user permissions
- Review migration script logs

### Getting Help

1. **Check health endpoint**: `/media/uploads/health.php`
2. **Review PHP error logs** in cPanel
3. **Test with small files** first
4. **Contact Namecheap support** for server issues

## 📈 Benefits After Migration

- ✅ **No storage fees** beyond hosting plan
- ✅ **Unlimited bandwidth** (within hosting limits)
- ✅ **Full control** over files and structure
- ✅ **Direct file access** (no API overhead)
- ✅ **Easy backup** and management
- ✅ **No vendor lock-in**

## 🎉 Success!

Your storage migration is complete! Your video platform now uses Namecheap hosting for all media files while maintaining the same functionality and user experience.
