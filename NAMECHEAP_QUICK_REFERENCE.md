# Namecheap Deployment Quick Reference

## 🚀 Quick Deployment Steps

### 1. Prepare Deployment Package
```bash
# Run the deployment script
./scripts/deploy-namecheap.sh
```

This will:
- Build your project
- Create a deployment package
- Generate the necessary .htaccess file
- Create `namecheap-deployment.zip`

### 2. Upload to Namecheap
1. **Login to cPanel** → File Manager
2. **Navigate** to `public_html/` (main domain) or subdomain folder
3. **Upload** `namecheap-deployment.zip`
4. **Extract** the ZIP file in the current directory
5. **Delete** the ZIP file after extraction

### 3. Verify Deployment
- Visit your domain
- Test page navigation
- Check video functionality
- Verify search works

## 📁 File Structure After Deployment
```
public_html/
├── index.html
├── .htaccess
├── assets/
│   ├── index-[hash].js
│   ├── index-[hash].css
│   └── [other assets]
└── [other static files]
```

## 🔧 Important Configuration

### .htaccess (Auto-generated)
The script creates an .htaccess file that:
- Enables React Router support
- Sets up compression
- Configures caching
- Adds security headers

### Environment Variables
Your Supabase configuration is built into the static files:
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_ANON_KEY`

## 🐛 Troubleshooting

| Issue | Solution |
|-------|----------|
| 404 on page refresh | Check .htaccess file exists and is correct |
| Assets not loading | Verify all files from dist/ were uploaded |
| Blank page | Check browser console for errors |
| Supabase errors | Verify environment variables in build |

## 🔄 Updating Your Site

1. Make changes locally
2. Run `./scripts/deploy-namecheap.sh`
3. Upload new `namecheap-deployment.zip`
4. Extract and replace files

## 📞 Support Resources

- **Namecheap Support**: 24/7 live chat
- **Documentation**: NAMECHEAP_DEPLOYMENT_GUIDE.md
- **cPanel Help**: Available in your hosting dashboard

## ⚡ Performance Tips

1. **Enable CDN**: Consider Namecheap's CDN service
2. **Monitor Loading**: Use browser dev tools
3. **Optimize Images**: Compress images before deployment
4. **Cache Strategy**: The .htaccess sets up proper caching

## 🔐 Security Notes

- Environment variables are built into static files (safe for client-side)
- Supabase keys are anon keys (designed for client-side use)
- .htaccess includes basic security headers
- Consider additional security measures for production

## 📊 Differences from Vercel

| Feature | Vercel | Namecheap |
|---------|--------|-----------|
| Deployment | Git-based | Manual upload |
| Environment Variables | Dashboard | Build-time only |
| Custom Domains | Automatic | Manual DNS setup |
| SSL | Automatic | Available (may need setup) |
| CDN | Built-in | Optional add-on |

Your Supabase backend remains unchanged - only the frontend hosting changes!
