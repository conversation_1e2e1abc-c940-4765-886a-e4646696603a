#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Configuration
const SUPABASE_URL = 'https://vsnsglgyapexhwyfylic.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzbnNnbGd5YXBleGh3eWZ5bGljIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExOTEsImV4cCI6MjA2MDgzNzE5MX0.6CQWpMT14h2kaIOk1_LMECuJrfRdmiGRo3vGyEDW9tM';
const NAMECHEAP_DOMAIN = 'www.bluefilmx.com';

// SSH Configuration
const SSH_CONFIG = {
  host: '**************',
  port: '21098',
  username: 'bluerpcm',
  keyPath: '~/.ssh/namecheap_rsa'
};

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * Test migration with one video
 */
async function testMigration() {
  console.log('🧪 Testing migration with one video...');
  
  try {
    // Get one video
    const { data: videos, error } = await supabase
      .from('videos')
      .select('id, title, video_url, thumbnail_url')
      .ilike('video_url', '%supabase%')
      .limit(1);
    
    if (error || !videos || videos.length === 0) {
      console.error('❌ No videos found or error:', error);
      return;
    }
    
    const video = videos[0];
    console.log(`📹 Testing with: ${video.title}`);
    console.log(`🔗 Video URL: ${video.video_url}`);
    console.log(`🖼️  Thumbnail URL: ${video.thumbnail_url}`);
    
    // Test SSH connection
    console.log('\n🔐 Testing SSH connection...');
    try {
      const { stdout } = await execAsync(`ssh -p ${SSH_CONFIG.port} -i ${SSH_CONFIG.keyPath} ${SSH_CONFIG.username}@${SSH_CONFIG.host} "pwd"`);
      console.log(`✅ SSH connection successful: ${stdout.trim()}`);
    } catch (error) {
      console.error('❌ SSH connection failed:', error.message);
      return;
    }
    
    // Test directory creation
    console.log('\n📁 Testing directory creation...');
    try {
      await execAsync(`ssh -p ${SSH_CONFIG.port} -i ${SSH_CONFIG.keyPath} ${SSH_CONFIG.username}@${SSH_CONFIG.host} "mkdir -p public_html/media/test-migration"`);
      console.log('✅ Directory creation successful');
    } catch (error) {
      console.error('❌ Directory creation failed:', error.message);
      return;
    }
    
    // Test file download from Supabase
    console.log('\n⬇️  Testing Supabase download...');
    
    // Extract file path from URL
    function extractFilePathFromUrl(url) {
      if (!url) return null;
      const cleanUrl = url.replace(/%0A/g, '');
      const match = cleanUrl.match(/\/storage\/v1\/object\/public\/([^\/]+)\/(.+)$/);
      if (match) {
        return { bucket: match[1], filePath: match[2] };
      }
      return null;
    }
    
    const videoInfo = extractFilePathFromUrl(video.video_url);
    if (!videoInfo) {
      console.error('❌ Could not extract file path from URL');
      return;
    }
    
    console.log(`📂 Bucket: ${videoInfo.bucket}`);
    console.log(`📄 File path: ${videoInfo.filePath}`);
    
    try {
      const { data, error } = await supabase.storage
        .from(videoInfo.bucket)
        .download(videoInfo.filePath);
      
      if (error) {
        throw error;
      }
      
      const size = data.size;
      console.log(`✅ Download successful: ${(size / 1024 / 1024).toFixed(2)} MB`);
      
      // Test creating a small test file and uploading it
      console.log('\n⬆️  Testing file upload...');
      
      const testContent = 'This is a test file for migration';
      const testFileName = 'test-migration.txt';
      const tempFile = `./${testFileName}`;
      
      fs.writeFileSync(tempFile, testContent);
      
      const scpCommand = `scp -P ${SSH_CONFIG.port} -i ${SSH_CONFIG.keyPath} "${tempFile}" ${SSH_CONFIG.username}@${SSH_CONFIG.host}:public_html/media/test-migration/${testFileName}`;
      await execAsync(scpCommand);
      
      // Verify upload
      const testUrl = `http://${NAMECHEAP_DOMAIN}/media/test-migration/${testFileName}`;
      console.log(`✅ Upload successful: ${testUrl}`);
      
      // Test HTTP access
      const { exec: execSync } = await import('child_process');
      execSync(`curl -s "${testUrl}"`, (error, stdout) => {
        if (error) {
          console.error('❌ HTTP test failed:', error.message);
        } else {
          console.log(`✅ HTTP access successful: ${stdout}`);
        }
      });
      
      // Cleanup
      fs.unlinkSync(tempFile);
      await execAsync(`ssh -p ${SSH_CONFIG.port} -i ${SSH_CONFIG.keyPath} ${SSH_CONFIG.username}@${SSH_CONFIG.host} "rm -rf public_html/media/test-migration"`);
      
      console.log('\n🎉 All tests passed! Migration should work correctly.');
      console.log('\n📋 Next steps:');
      console.log('1. Run: node migrate-files.mjs');
      console.log('2. Monitor the migration progress');
      console.log('3. Verify videos play correctly after migration');
      
    } catch (error) {
      console.error('❌ Download test failed:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testMigration();
