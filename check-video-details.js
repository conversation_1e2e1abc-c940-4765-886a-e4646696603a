// <PERSON>ript to check detailed information about video URLs
import fetch from 'node-fetch';

// Video URLs from your Supabase storage
const videos = [
    {
        title: "Appreciate a true Nyash Glory Widely Opened and natured by a Queen 1",
        videoUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746147162269-4_5802976136631687807.MP4",
        thumbnailUrl: "https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/c2157ddd-2f88-436a-8ae7-f2b828b30145/1746147185564-Screenshot_20250502-005053.jpg"
    }
];

async function checkUrlDetails(url, type) {
    try {
        console.log(`Checking ${type}: ${url}`);
        
        // Make a HEAD request to check headers
        const response = await fetch(url, { method: 'HEAD' });
        
        console.log(`\nStatus: ${response.status} ${response.statusText}`);
        
        // Print all headers
        console.log('\nHeaders:');
        for (const [key, value] of response.headers.entries()) {
            console.log(`${key}: ${value}`);
        }
        
        // Check for important headers
        const contentType = response.headers.get('content-type');
        const contentLength = response.headers.get('content-length');
        const cacheControl = response.headers.get('cache-control');
        const corsHeaders = {
            'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
            'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
            'access-control-allow-headers': response.headers.get('access-control-allow-headers')
        };
        
        // Format size
        const formattedSize = contentLength ? `${(parseInt(contentLength) / (1024 * 1024)).toFixed(2)} MB` : 'Unknown size';
        
        console.log('\nKey Information:');
        console.log(`Content-Type: ${contentType || 'Not specified'}`);
        console.log(`Size: ${formattedSize}`);
        console.log(`Cache-Control: ${cacheControl || 'Not specified'}`);
        
        console.log('\nCORS Headers:');
        for (const [key, value] of Object.entries(corsHeaders)) {
            console.log(`${key}: ${value || 'Not specified'}`);
        }
        
        // Check for range support (important for video streaming)
        const acceptRanges = response.headers.get('accept-ranges');
        console.log(`\nRange Support: ${acceptRanges || 'Not specified'}`);
        
        if (acceptRanges === 'bytes') {
            console.log('✅ Server supports byte ranges (good for video streaming)');
        } else {
            console.log('⚠️ Server may not support byte ranges (could affect video streaming)');
        }
        
        // Check for content-disposition header
        const contentDisposition = response.headers.get('content-disposition');
        console.log(`\nContent-Disposition: ${contentDisposition || 'Not specified'}`);
        
        if (contentDisposition && contentDisposition.includes('attachment')) {
            console.log('⚠️ Content is set to download rather than display inline');
        } else {
            console.log('✅ Content should display inline (good for video playback)');
        }
        
        return response.ok;
    } catch (error) {
        console.log(`\n❌ Error checking ${type}: ${url}`);
        console.log(`Error: ${error.message}`);
        return false;
    }
}

async function checkAllDetails() {
    for (const video of videos) {
        console.log(`\n======= ${video.title} =======`);
        
        // Check video URL
        await checkUrlDetails(video.videoUrl, 'Video');
        
        console.log('\n-----------------------------------');
        
        // Check thumbnail URL
        await checkUrlDetails(video.thumbnailUrl, 'Thumbnail');
    }
}

checkAllDetails();
