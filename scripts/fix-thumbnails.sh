#!/bin/bash

# Fix Thumbnail Matching
# Matches thumbnail files to videos using various strategies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"

print_status "🖼️  Starting comprehensive thumbnail matching..."

# Create thumbnail matching script
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST << 'EOF'
#!/bin/bash

echo "🔍 Analyzing thumbnail matching opportunities..."

# Get all files
find public_html/media/videos -name "*.mp4" -o -name "*.MP4" | sed 's|public_html/media/videos/||' | sort > /tmp/all_videos.txt
find public_html/media/thumbnails -name "*.jpg" -o -name "*.png" | sed 's|public_html/media/thumbnails/||' | sort > /tmp/all_thumbnails.txt

video_count=$(wc -l < /tmp/all_videos.txt)
thumbnail_count=$(wc -l < /tmp/all_thumbnails.txt)

echo "   📹 Videos: $video_count"
echo "   🖼️  Thumbnails: $thumbnail_count"

# Create comprehensive thumbnail matching script
cat > /tmp/fix_thumbnails.php << 'PHPEOF'
<?php
$host = 'localhost';
$dbname = 'bluerpcm_bluefilmx';
$username = 'bluerpcm_dbuser';
$password = 'kingpatrick100';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔗 Connected to database\n";
    
    // Get all files
    $all_videos = file('/tmp/all_videos.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $all_thumbnails = file('/tmp/all_thumbnails.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    echo "📹 Processing " . count($all_videos) . " videos\n";
    echo "🖼️  Processing " . count($all_thumbnails) . " thumbnails\n";
    
    // Build comprehensive thumbnail lookup strategies
    $thumbnail_strategies = [];
    
    foreach ($all_thumbnails as $thumbnail) {
        // Strategy 1: UUID matching
        if (preg_match('/^([a-f0-9-]{36})_/', $thumbnail, $matches)) {
            $uuid = $matches[1];
            $thumbnail_strategies['uuid'][$uuid] = $thumbnail;
        }
        
        // Strategy 2: Timestamp matching
        if (preg_match('/(\d{13})/', $thumbnail, $matches)) {
            $timestamp = $matches[1];
            $thumbnail_strategies['timestamp'][$timestamp] = $thumbnail;
        }
        
        // Strategy 3: Filename similarity
        $base_name = pathinfo($thumbnail, PATHINFO_FILENAME);
        $clean_name = preg_replace('/[^a-zA-Z0-9]/', '', strtolower($base_name));
        $thumbnail_strategies['filename'][$clean_name] = $thumbnail;
        
        // Strategy 4: Partial name matching
        $words = preg_split('/[^a-zA-Z0-9]+/', $base_name);
        foreach ($words as $word) {
            if (strlen($word) > 3) {
                $thumbnail_strategies['words'][strtolower($word)][] = $thumbnail;
            }
        }
    }
    
    echo "🔍 Built thumbnail lookup strategies\n";
    echo "   📋 UUID matches: " . count($thumbnail_strategies['uuid'] ?? []) . "\n";
    echo "   ⏰ Timestamp matches: " . count($thumbnail_strategies['timestamp'] ?? []) . "\n";
    echo "   📝 Filename matches: " . count($thumbnail_strategies['filename'] ?? []) . "\n";
    echo "   🔤 Word matches: " . count($thumbnail_strategies['words'] ?? []) . "\n";
    
    // Get all videos from database
    $stmt = $pdo->query("SELECT id, title, video_url, thumbnail_url FROM videos");
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $updated_count = 0;
    $strategy_stats = ['uuid' => 0, 'timestamp' => 0, 'filename' => 0, 'words' => 0];
    $base_thumbnail_url = 'https://www.bluefilmx.com/media/thumbnails/';
    
    foreach ($videos as $video) {
        // Skip if already has thumbnail
        if (!empty($video['thumbnail_url'])) {
            continue;
        }
        
        $video_filename = basename($video['video_url']);
        $found_thumbnail = null;
        $strategy_used = null;
        
        // Strategy 1: UUID matching
        if (!$found_thumbnail && preg_match('/^([a-f0-9-]{36})_/', $video_filename, $matches)) {
            $uuid = $matches[1];
            if (isset($thumbnail_strategies['uuid'][$uuid])) {
                $found_thumbnail = $thumbnail_strategies['uuid'][$uuid];
                $strategy_used = 'uuid';
            }
        }
        
        // Strategy 2: Timestamp matching
        if (!$found_thumbnail && preg_match('/(\d{13})/', $video_filename, $matches)) {
            $timestamp = $matches[1];
            if (isset($thumbnail_strategies['timestamp'][$timestamp])) {
                $found_thumbnail = $thumbnail_strategies['timestamp'][$timestamp];
                $strategy_used = 'timestamp';
            }
        }
        
        // Strategy 3: Filename similarity
        if (!$found_thumbnail) {
            $video_base = pathinfo($video_filename, PATHINFO_FILENAME);
            $clean_video_name = preg_replace('/[^a-zA-Z0-9]/', '', strtolower($video_base));
            
            if (isset($thumbnail_strategies['filename'][$clean_video_name])) {
                $found_thumbnail = $thumbnail_strategies['filename'][$clean_video_name];
                $strategy_used = 'filename';
            }
        }
        
        // Strategy 4: Word matching (find best match)
        if (!$found_thumbnail) {
            $video_words = preg_split('/[^a-zA-Z0-9]+/', pathinfo($video_filename, PATHINFO_FILENAME));
            $best_match = null;
            $best_score = 0;
            
            foreach ($video_words as $word) {
                if (strlen($word) > 3) {
                    $word_lower = strtolower($word);
                    if (isset($thumbnail_strategies['words'][$word_lower])) {
                        foreach ($thumbnail_strategies['words'][$word_lower] as $candidate) {
                            // Score based on word matches
                            $score = 0;
                            foreach ($video_words as $vw) {
                                if (strlen($vw) > 3 && stripos($candidate, $vw) !== false) {
                                    $score++;
                                }
                            }
                            if ($score > $best_score) {
                                $best_score = $score;
                                $best_match = $candidate;
                            }
                        }
                    }
                }
            }
            
            if ($best_match && $best_score >= 2) {
                $found_thumbnail = $best_match;
                $strategy_used = 'words';
            }
        }
        
        // Update database if thumbnail found
        if ($found_thumbnail) {
            $thumbnail_url = $base_thumbnail_url . $found_thumbnail;
            
            $stmt = $pdo->prepare("UPDATE videos SET thumbnail_url = ? WHERE id = ?");
            $stmt->execute([$thumbnail_url, $video['id']]);
            
            $updated_count++;
            $strategy_stats[$strategy_used]++;
            
            if ($updated_count % 25 == 0) {
                echo "   ✅ Updated $updated_count thumbnails...\n";
            }
        }
    }
    
    echo "\n📊 Thumbnail matching complete!\n";
    echo "   ✅ Total thumbnails matched: $updated_count\n";
    echo "   📋 UUID strategy: " . $strategy_stats['uuid'] . "\n";
    echo "   ⏰ Timestamp strategy: " . $strategy_stats['timestamp'] . "\n";
    echo "   📝 Filename strategy: " . $strategy_stats['filename'] . "\n";
    echo "   🔤 Word strategy: " . $strategy_stats['words'] . "\n";
    
    // Final verification
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE thumbnail_url IS NOT NULL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "\n🔍 Final verification:\n";
    echo "   🖼️  Videos with thumbnails: " . $result['count'] . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE thumbnail_url IS NULL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   ❌ Videos without thumbnails: " . $result['count'] . "\n";
    
    // Show some examples
    echo "\n📋 Sample matched thumbnails:\n";
    $stmt = $pdo->query("SELECT title, thumbnail_url FROM videos WHERE thumbnail_url IS NOT NULL ORDER BY updated_at DESC LIMIT 5");
    $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($samples as $i => $sample) {
        echo "   " . ($i+1) . ". " . $sample['title'] . "\n";
        echo "      Thumbnail: " . basename($sample['thumbnail_url']) . "\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
}
PHPEOF

echo "🚀 Running comprehensive thumbnail matching..."
php /tmp/fix_thumbnails.php

echo "🗑️  Cleaning up..."
rm -f /tmp/all_videos.txt /tmp/all_thumbnails.txt /tmp/fix_thumbnails.php

echo "✅ Thumbnail matching completed!"
EOF

print_success "🎉 Thumbnail matching completed!"

# Test a few thumbnail URLs
print_status "🧪 Testing thumbnail accessibility..."
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "mysql -u bluerpcm_dbuser -pkingpatrick100 bluerpcm_bluefilmx -e 'SELECT thumbnail_url FROM videos WHERE thumbnail_url IS NOT NULL LIMIT 3;' | tail -n +2" > /tmp/test_thumbnails.txt

while read -r thumbnail_url; do
    if [[ -n "$thumbnail_url" ]]; then
        status=$(curl -s -o /dev/null -w "%{http_code}" "$thumbnail_url")
        if [[ "$status" == "200" ]]; then
            print_success "✅ Thumbnail accessible: $(basename "$thumbnail_url")"
        else
            print_warning "⚠️  Thumbnail issue ($status): $(basename "$thumbnail_url")"
        fi
    fi
done < /tmp/test_thumbnails.txt

rm -f /tmp/test_thumbnails.txt

print_success "✅ Thumbnail fix process completed!"
print_status "🌐 Your website should now display many more thumbnails!"
print_status "🔗 Test your website: https://www.bluefilmx.com"
