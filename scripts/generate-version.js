#!/usr/bin/env node

/**
 * Generate version.json file for cache busting and version monitoring
 */

import { writeFileSync } from 'fs';
import { resolve } from 'path';

const generateVersion = () => {
  const version = process.env.npm_package_version || '1.0.0';
  const buildId = process.env.VERCEL_GIT_COMMIT_SHA || 
                  process.env.GITHUB_SHA || 
                  Date.now().toString();
  
  const versionInfo = {
    version,
    buildId: buildId.substring(0, 8), // Short commit hash
    timestamp: new Date().toISOString(),
    features: [
      'browser-state-management',
      'cache-busting',
      'conflict-detection',
      'version-monitoring'
    ],
    environment: process.env.NODE_ENV || 'development'
  };

  // Write to dist directory
  const distPath = resolve(process.cwd(), 'dist', 'version.json');
  writeFileSync(distPath, JSON.stringify(versionInfo, null, 2));
  
  // Also update public directory for development
  const publicPath = resolve(process.cwd(), 'public', 'version.json');
  writeFileSync(publicPath, JSON.stringify(versionInfo, null, 2));

  console.log('✅ Version file generated:', versionInfo);
};

generateVersion();
