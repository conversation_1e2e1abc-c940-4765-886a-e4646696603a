-- Revert Media URLs back to Supabase Storage
-- This script updates Namecheap URLs back to point to Supabase storage temporarily

-- First, let's see what URLs we currently have
SELECT 
    'Current URLs Sample' as info,
    id,
    title,
    LEFT(video_url, 60) as video_url_start,
    LEFT(thumbnail_url, 60) as thumbnail_url_start
FROM videos 
LIMIT 3;

-- Update video URLs from Namecheap back to Supabase storage
UPDATE videos 
SET video_url = REPLACE(
    video_url, 
    'https://www.bluefilmx.com/media/videos/', 
    'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/'
)
WHERE video_url LIKE 'https://www.bluefilmx.com/media/videos/%';

-- Update thumbnail URLs from Namecheap back to Supabase storage  
UPDATE videos 
SET thumbnail_url = REPLACE(
    thumbnail_url, 
    'https://www.bluefilmx.com/media/thumbnails/', 
    'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/'
)
WHERE thumbnail_url LIKE 'https://www.bluefilmx.com/media/thumbnails/%';

-- Also update any collection thumbnail URLs
UPDATE collections 
SET thumbnail_url = REPLACE(
    thumbnail_url, 
    'https://www.bluefilmx.com/media/thumbnails/', 
    'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/'
)
WHERE thumbnail_url LIKE 'https://www.bluefilmx.com/media/thumbnails/%';

-- Update profile avatar URLs if any
UPDATE profiles 
SET avatar_url = REPLACE(
    avatar_url, 
    'https://www.bluefilmx.com/media/avatars/', 
    'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/avatars/'
)
WHERE avatar_url LIKE 'https://www.bluefilmx.com/media/avatars/%';

-- Show updated URLs sample
SELECT 
    'Reverted URLs Sample' as info,
    id,
    title,
    LEFT(video_url, 60) as video_url_start,
    LEFT(thumbnail_url, 60) as thumbnail_url_start
FROM videos 
LIMIT 3;

-- Show count of reverted records
SELECT 
    'Revert Summary' as info,
    COUNT(*) as total_videos,
    SUM(CASE WHEN video_url LIKE 'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/%' THEN 1 ELSE 0 END) as videos_with_supabase_urls,
    SUM(CASE WHEN thumbnail_url LIKE 'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/%' THEN 1 ELSE 0 END) as thumbnails_with_supabase_urls
FROM videos;
