#!/bin/bash

# Robust Video ZIP Upload Script with Resume Support
# Uploads video.zip with better error handling and resume capability

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"

print_status "🚀 Starting ROBUST video.zip upload to Namecheap server!"

# Check if video.zip exists locally
if [ ! -f "video.zip" ]; then
    print_error "❌ video.zip not found in current directory"
    exit 1
fi

# Get file size
video_size=$(du -h video.zip | cut -f1)
print_status "📊 video.zip size: $video_size"

# Upload video.zip with compression and keep-alive
print_status "📤 Uploading video.zip with robust settings..."
print_status "   Using compression and keep-alive for better reliability"

# Use rsync for more robust upload with resume capability
if command -v rsync >/dev/null 2>&1; then
    print_status "🔄 Using rsync for robust upload with resume capability..."
    
    if rsync -avz --progress --partial --inplace -e "ssh -p $SSH_PORT" video.zip $SSH_USER@$SSH_HOST:; then
        print_success "✅ video.zip uploaded successfully with rsync!"
    else
        print_warning "⚠️  rsync failed, falling back to scp..."
        # Fallback to scp with compression
        if scp -C -P $SSH_PORT video.zip $SSH_USER@$SSH_HOST:; then
            print_success "✅ video.zip uploaded successfully with scp!"
        else
            print_error "❌ Failed to upload video.zip"
            exit 1
        fi
    fi
else
    # Use scp with compression
    print_status "📤 Using scp with compression..."
    if scp -C -P $SSH_PORT video.zip $SSH_USER@$SSH_HOST:; then
        print_success "✅ video.zip uploaded successfully!"
    else
        print_error "❌ Failed to upload video.zip"
        exit 1
    fi
fi

# Verify upload
print_status "🔍 Verifying upload..."
remote_size=$(ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "ls -lh video.zip | awk '{print \$5}'" 2>/dev/null || echo "0")
print_status "📊 Remote file size: $remote_size"

# Upload thumbnail.zip
print_status "📤 Uploading thumbnail.zip..."
if [ -f "thumbnail.zip" ]; then
    thumbnail_size=$(du -h thumbnail.zip | cut -f1)
    print_status "📊 thumbnail.zip size: $thumbnail_size"
    
    if rsync -avz --progress -e "ssh -p $SSH_PORT" thumbnail.zip $SSH_USER@$SSH_HOST: 2>/dev/null || scp -C -P $SSH_PORT thumbnail.zip $SSH_USER@$SSH_HOST:; then
        print_success "✅ thumbnail.zip uploaded successfully!"
    else
        print_error "❌ Failed to upload thumbnail.zip"
        exit 1
    fi
else
    print_warning "⚠️  thumbnail.zip not found, skipping..."
fi

# Extract files on server
print_status "📂 Extracting files on server..."

ssh -p $SSH_PORT $SSH_USER@$SSH_HOST << 'EOF'
    echo "🔧 Setting up directories..."
    mkdir -p public_html/media/videos
    mkdir -p public_html/media/thumbnails
    
    echo "📹 Extracting videos..."
    if [ -f video.zip ]; then
        echo "Extracting video.zip (this may take a few minutes)..."
        unzip -o video.zip -d public_html/media/videos/ > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "✅ Videos extracted successfully"
            rm video.zip
            echo "🗑️  video.zip cleaned up"
        else
            echo "❌ Video extraction failed"
        fi
    else
        echo "❌ video.zip not found"
    fi
    
    echo "🖼️  Extracting thumbnails..."
    if [ -f thumbnail.zip ]; then
        echo "Extracting thumbnail.zip..."
        unzip -o thumbnail.zip -d public_html/media/thumbnails/ > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "✅ Thumbnails extracted successfully"
            rm thumbnail.zip
            echo "🗑️  thumbnail.zip cleaned up"
        else
            echo "❌ Thumbnail extraction failed"
        fi
    else
        echo "⚠️  thumbnail.zip not found"
    fi
    
    echo "🔧 Setting permissions..."
    find public_html/media/videos -name "*.mp4" -exec chmod 644 {} \; 2>/dev/null || true
    find public_html/media/videos -name "*.MP4" -exec chmod 644 {} \; 2>/dev/null || true
    find public_html/media/thumbnails -name "*.jpg" -exec chmod 644 {} \; 2>/dev/null || true
    find public_html/media/thumbnails -name "*.png" -exec chmod 644 {} \; 2>/dev/null || true
    
    echo "📊 Final counts:"
    video_count=$(find public_html/media/videos -name '*.mp4' -o -name '*.MP4' | wc -l)
    thumbnail_count=$(find public_html/media/thumbnails -name '*.jpg' -o -name '*.png' | wc -l)
    echo "   📹 Videos: $video_count"
    echo "   🖼️  Thumbnails: $thumbnail_count"
EOF

# Get final counts
print_status "🔍 Verifying final results..."
final_videos=$(ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "find public_html/media/videos -name '*.mp4' -o -name '*.MP4' | wc -l" 2>/dev/null || echo "0")
final_thumbnails=$(ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "find public_html/media/thumbnails -name '*.jpg' -o -name '*.png' | wc -l" 2>/dev/null || echo "0")

print_success "🎉 Upload and extraction complete!"
echo ""
print_status "📊 Final Results:"
print_success "   📹 Videos on server: $final_videos"
print_success "   🖼️  Thumbnails on server: $final_thumbnails"

if [ "$final_videos" -gt 200 ]; then
    print_success "🎉 Excellent! Most/all videos have been uploaded!"
    print_status "🌐 Your website should now display all videos and thumbnails."
    print_status "🔗 Test your website: https://www.bluefilmx.com"
elif [ "$final_videos" -gt 50 ]; then
    print_success "🎉 Good progress! Many videos uploaded successfully."
    print_status "🔗 Test your website: https://www.bluefilmx.com"
else
    print_warning "⚠️  Fewer videos than expected. Check extraction logs above."
fi

print_success "✅ Robust upload completed!"
