#!/usr/bin/env node

/**
 * Storage Migration Script: Supabase → Namecheap
 * Downloads files from Supabase and uploads them to Namecheap hosting
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import https from 'https';
import http from 'http';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;
const NAMECHEAP_DOMAIN = process.env.NAMECHEAP_DOMAIN || 'yourdomain.com';
const UPLOAD_ENDPOINT = `https://${NAMECHEAP_DOMAIN}/media/uploads/upload.php`;

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Create directories for temporary downloads
const TEMP_DIR = path.join(__dirname, '../temp-migration');
const VIDEOS_TEMP = path.join(TEMP_DIR, 'videos');
const THUMBNAILS_TEMP = path.join(TEMP_DIR, 'thumbnails');

// Ensure temp directories exist
function createTempDirectories() {
  [TEMP_DIR, VIDEOS_TEMP, THUMBNAILS_TEMP].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// Download file from URL
function downloadFile(url, filepath) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    const file = fs.createWriteStream(filepath);
    
    protocol.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve(filepath);
      });
      
      file.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Delete partial file
        reject(err);
      });
    }).on('error', reject);
  });
}

// Upload file to Namecheap
async function uploadToNamecheap(filepath, type, authToken) {
  const FormData = (await import('form-data')).default;
  const fetch = (await import('node-fetch')).default;
  
  const form = new FormData();
  form.append('file', fs.createReadStream(filepath));
  form.append('type', type);
  
  const response = await fetch(UPLOAD_ENDPOINT, {
    method: 'POST',
    body: form,
    headers: {
      'Authorization': `Bearer ${authToken}`,
      ...form.getHeaders()
    }
  });
  
  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Upload failed: ${error}`);
  }
  
  return await response.json();
}

// Get all videos from database
async function getAllVideos() {
  console.log('📊 Fetching all videos from database...');
  
  const { data: videos, error } = await supabase
    .from('videos')
    .select('id, title, video_url, thumbnail_url, user_id')
    .order('created_at', { ascending: true });
  
  if (error) {
    throw new Error(`Failed to fetch videos: ${error.message}`);
  }
  
  console.log(`✅ Found ${videos.length} videos to migrate`);
  return videos;
}

// Migrate a single video
async function migrateVideo(video, index, total) {
  console.log(`\n🎬 Migrating video ${index + 1}/${total}: ${video.title}`);
  console.log(`   ID: ${video.id}`);
  
  const updates = {};
  
  try {
    // Download and upload video file
    if (video.video_url && video.video_url.includes('supabase.co')) {
      console.log('   📥 Downloading video file...');
      const videoFilename = `video_${video.id}_${Date.now()}.mp4`;
      const videoPath = path.join(VIDEOS_TEMP, videoFilename);
      
      await downloadFile(video.video_url, videoPath);
      console.log('   ✅ Video downloaded');
      
      console.log('   📤 Uploading video to Namecheap...');
      const videoResult = await uploadToNamecheap(videoPath, 'video', video.user_id);
      updates.video_url = videoResult.url;
      console.log(`   ✅ Video uploaded: ${videoResult.url}`);
      
      // Clean up temp file
      fs.unlinkSync(videoPath);
    }
    
    // Download and upload thumbnail file
    if (video.thumbnail_url && video.thumbnail_url.includes('supabase.co')) {
      console.log('   📥 Downloading thumbnail...');
      const thumbnailFilename = `thumb_${video.id}_${Date.now()}.jpg`;
      const thumbnailPath = path.join(THUMBNAILS_TEMP, thumbnailFilename);
      
      await downloadFile(video.thumbnail_url, thumbnailPath);
      console.log('   ✅ Thumbnail downloaded');
      
      console.log('   📤 Uploading thumbnail to Namecheap...');
      const thumbnailResult = await uploadToNamecheap(thumbnailPath, 'image', video.user_id);
      updates.thumbnail_url = thumbnailResult.url;
      console.log(`   ✅ Thumbnail uploaded: ${thumbnailResult.url}`);
      
      // Clean up temp file
      fs.unlinkSync(thumbnailPath);
    }
    
    // Update database with new URLs
    if (Object.keys(updates).length > 0) {
      console.log('   💾 Updating database...');
      const { error: updateError } = await supabase
        .from('videos')
        .update(updates)
        .eq('id', video.id);
      
      if (updateError) {
        throw new Error(`Database update failed: ${updateError.message}`);
      }
      
      console.log('   ✅ Database updated');
    }
    
    return { success: true, video: video.id, updates };
    
  } catch (error) {
    console.error(`   ❌ Migration failed: ${error.message}`);
    return { success: false, video: video.id, error: error.message };
  }
}

// Main migration function
async function migrateStorage() {
  console.log('🚀 Starting storage migration from Supabase to Namecheap\n');
  
  // Validate environment variables
  if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
  }
  
  if (NAMECHEAP_DOMAIN === 'yourdomain.com') {
    console.error('❌ Please set NAMECHEAP_DOMAIN environment variable');
    process.exit(1);
  }
  
  try {
    // Create temp directories
    createTempDirectories();
    
    // Get all videos
    const videos = await getAllVideos();
    
    if (videos.length === 0) {
      console.log('✅ No videos to migrate');
      return;
    }
    
    // Migrate each video
    const results = [];
    for (let i = 0; i < videos.length; i++) {
      const result = await migrateVideo(videos[i], i, videos.length);
      results.push(result);
      
      // Add delay between migrations to avoid overwhelming the server
      if (i < videos.length - 1) {
        console.log('   ⏳ Waiting 2 seconds before next migration...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    // Summary
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log('\n📊 Migration Summary:');
    console.log(`   ✅ Successful: ${successful}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   📁 Total: ${videos.length}`);
    
    if (failed > 0) {
      console.log('\n❌ Failed migrations:');
      results.filter(r => !r.success).forEach(r => {
        console.log(`   - Video ${r.video}: ${r.error}`);
      });
    }
    
    // Clean up temp directory
    console.log('\n🧹 Cleaning up temporary files...');
    fs.rmSync(TEMP_DIR, { recursive: true, force: true });
    
    console.log('\n🎉 Migration completed!');
    
  } catch (error) {
    console.error('💥 Migration failed:', error.message);
    process.exit(1);
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  migrateStorage().catch(console.error);
}

export { migrateStorage };
