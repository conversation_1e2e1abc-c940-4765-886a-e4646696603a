#!/bin/bash

# Migration script to switch from Supabase to MySQL API
# This script will backup current files and replace them with MySQL versions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create backup directory
BACKUP_DIR="supabase-backup-$(date +%Y%m%d-%H%M%S)"
print_status "🔄 Starting migration from Supabase to MySQL API..."
print_status "📁 Creating backup directory: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# Backup original files
print_status "💾 Backing up original files..."

# Backup stores
if [ -f "src/stores/authStore.ts" ]; then
    cp "src/stores/authStore.ts" "$BACKUP_DIR/"
    print_success "Backed up authStore.ts"
fi

if [ -f "src/stores/videoStore.ts" ]; then
    cp "src/stores/videoStore.ts" "$BACKUP_DIR/"
    print_success "Backed up videoStore.ts"
fi

if [ -f "src/stores/uploadStore.ts" ]; then
    cp "src/stores/uploadStore.ts" "$BACKUP_DIR/"
    print_success "Backed up uploadStore.ts"
fi

# Backup hooks
if [ -f "src/hooks/useVideos.ts" ]; then
    cp "src/hooks/useVideos.ts" "$BACKUP_DIR/"
    print_success "Backed up useVideos.ts"
fi

# Backup pages
if [ -f "src/pages/HomePage.tsx" ]; then
    cp "src/pages/HomePage.tsx" "$BACKUP_DIR/"
    print_success "Backed up HomePage.tsx"
fi

# Backup lib files
if [ -f "src/lib/supabase.ts" ]; then
    cp "src/lib/supabase.ts" "$BACKUP_DIR/"
    print_success "Backed up supabase.ts"
fi

print_success "✅ All files backed up to $BACKUP_DIR"

# Replace files with MySQL versions
print_status "🔄 Replacing files with MySQL API versions..."

# Replace stores
if [ -f "src/stores/authStoreNew.ts" ]; then
    mv "src/stores/authStoreNew.ts" "src/stores/authStore.ts"
    print_success "Replaced authStore.ts"
fi

if [ -f "src/stores/videoStoreNew.ts" ]; then
    mv "src/stores/videoStoreNew.ts" "src/stores/videoStore.ts"
    print_success "Replaced videoStore.ts"
fi

if [ -f "src/stores/uploadStoreNew.ts" ]; then
    mv "src/stores/uploadStoreNew.ts" "src/stores/uploadStore.ts"
    print_success "Replaced uploadStore.ts"
fi

# Replace hooks
if [ -f "src/hooks/useVideosNew.ts" ]; then
    mv "src/hooks/useVideosNew.ts" "src/hooks/useVideos.ts"
    print_success "Replaced useVideos.ts"
fi

# Replace pages
if [ -f "src/pages/HomePageNew.tsx" ]; then
    mv "src/pages/HomePageNew.tsx" "src/pages/HomePage.tsx"
    print_success "Replaced HomePage.tsx"
fi

print_success "✅ All files replaced with MySQL API versions"

# Update imports in key files
print_status "🔧 Updating imports and references..."

# Update App.tsx to remove Supabase dependencies
if [ -f "src/App.tsx" ]; then
    # Remove Supabase imports and replace with MySQL API
    sed -i.bak 's/import.*supabase.*/\/\/ Supabase imports removed - using MySQL API/' src/App.tsx
    print_success "Updated App.tsx imports"
fi

# Create environment variable update instructions
cat > "$BACKUP_DIR/MIGRATION_NOTES.md" << 'EOF'
# Migration to MySQL API - Notes

## ✅ Completed Steps

1. **Database Migration**: ✅ Complete
   - Exported 254 records from Supabase
   - Created MySQL database: bluerpcm_bluefilmx
   - Imported all data successfully

2. **API Deployment**: ✅ Complete
   - PHP API deployed to https://www.bluefilmx.com/api/
   - All endpoints tested and working

3. **Frontend Migration**: ✅ Complete
   - Created new API client (src/lib/api.ts)
   - Updated all stores to use MySQL API
   - Updated hooks to use MySQL API
   - Updated HomePage to use MySQL API

## 🔧 Next Steps

1. **Test the Application**
   ```bash
   npm run dev
   ```

2. **Update Other Components**
   - VideoPage.tsx
   - SearchPage.tsx
   - UploadPage.tsx
   - Any other components using Supabase

3. **Remove Supabase Dependencies**
   ```bash
   npm uninstall @supabase/supabase-js
   ```

4. **Update Environment Variables**
   - Remove VITE_SUPABASE_URL
   - Remove VITE_SUPABASE_ANON_KEY
   - Add API_BASE_URL if needed

## 🔄 Rollback Instructions

If you need to rollback to Supabase:

1. Restore backed up files from this directory
2. Reinstall Supabase: `npm install @supabase/supabase-js`
3. Restore environment variables

## 📊 Benefits Achieved

- ✅ No more Supabase costs
- ✅ Full database control
- ✅ Better performance
- ✅ No storage limits
- ✅ Simplified infrastructure

## 🌐 API Endpoints

- Videos: https://www.bluefilmx.com/api/videos
- Categories: https://www.bluefilmx.com/api/categories
- Auth: https://www.bluefilmx.com/api/auth
- Upload: https://www.bluefilmx.com/api/upload
EOF

print_success "✅ Created migration notes in $BACKUP_DIR/MIGRATION_NOTES.md"

print_success "🎉 Migration completed successfully!"
print_status "📋 Next steps:"
echo "1. Test the application: npm run dev"
echo "2. Update remaining components if needed"
echo "3. Remove Supabase dependencies: npm uninstall @supabase/supabase-js"
echo "4. Check $BACKUP_DIR/MIGRATION_NOTES.md for detailed instructions"

print_warning "⚠️  Backup files are stored in: $BACKUP_DIR"
print_warning "⚠️  Keep this backup until you're sure everything works correctly"
