#!/bin/bash

# Create Default Thumbnails for Videos Without Thumbnails
# Assigns placeholder thumbnails or creates default ones

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"

print_status "🖼️  Creating default thumbnails for videos without thumbnails..."

# Create default thumbnail solution
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST << 'EOF'
#!/bin/bash

echo "🔍 Setting up default thumbnail solution..."

# Create a default thumbnail directory
mkdir -p public_html/media/thumbnails/defaults

# Create a simple default thumbnail using ImageMagick (if available) or use existing thumbnail
if command -v convert >/dev/null 2>&1; then
    echo "🎨 Creating default thumbnail with ImageMagick..."
    convert -size 320x240 xc:black \
        -fill white -gravity center \
        -pointsize 24 -annotate +0+0 "Video\nThumbnail" \
        public_html/media/thumbnails/defaults/default-video.jpg
    echo "✅ Default thumbnail created"
else
    echo "📋 Using existing thumbnail as default..."
    # Use the first available thumbnail as default
    first_thumb=$(find public_html/media/thumbnails -name "*.jpg" -o -name "*.png" | head -1)
    if [ -n "$first_thumb" ]; then
        cp "$first_thumb" public_html/media/thumbnails/defaults/default-video.jpg
        echo "✅ Default thumbnail copied from existing file"
    else
        echo "⚠️  No thumbnails available for default"
    fi
fi

# Create PHP script to assign default thumbnails
cat > /tmp/assign_defaults.php << 'PHPEOF'
<?php
$host = 'localhost';
$dbname = 'bluerpcm_bluefilmx';
$username = 'bluerpcm_dbuser';
$password = 'kingpatrick100';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔗 Connected to database\n";
    
    // Check if default thumbnail exists
    $default_thumbnail_path = 'public_html/media/thumbnails/defaults/default-video.jpg';
    $default_thumbnail_url = 'https://www.bluefilmx.com/media/thumbnails/defaults/default-video.jpg';
    
    if (!file_exists($default_thumbnail_path)) {
        echo "⚠️  Default thumbnail not found, using first available thumbnail\n";
        
        // Find first available thumbnail
        $thumbnails = glob('public_html/media/thumbnails/*.{jpg,png}', GLOB_BRACE);
        if (!empty($thumbnails)) {
            $first_thumbnail = basename($thumbnails[0]);
            $default_thumbnail_url = 'https://www.bluefilmx.com/media/thumbnails/' . $first_thumbnail;
            echo "📋 Using: $first_thumbnail\n";
        } else {
            echo "❌ No thumbnails available\n";
            exit(1);
        }
    }
    
    // Get videos without thumbnails
    $stmt = $pdo->query("SELECT id, title FROM videos WHERE thumbnail_url IS NULL");
    $videos_without_thumbnails = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📊 Found " . count($videos_without_thumbnails) . " videos without thumbnails\n";
    
    if (count($videos_without_thumbnails) > 0) {
        // Update videos without thumbnails to use default
        $stmt = $pdo->prepare("UPDATE videos SET thumbnail_url = ? WHERE thumbnail_url IS NULL");
        $stmt->execute([$default_thumbnail_url]);
        
        $updated_count = $stmt->rowCount();
        echo "✅ Updated $updated_count videos with default thumbnail\n";
    }
    
    // Verify final state
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE thumbnail_url IS NOT NULL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "\n🔍 Final verification:\n";
    echo "   🖼️  Videos with thumbnails: " . $result['count'] . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE thumbnail_url IS NULL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   ❌ Videos without thumbnails: " . $result['count'] . "\n";
    
    // Show breakdown
    $stmt = $pdo->query("
        SELECT 
            CASE 
                WHEN thumbnail_url LIKE '%/defaults/%' THEN 'Default Thumbnail'
                ELSE 'Custom Thumbnail'
            END as thumbnail_type,
            COUNT(*) as count
        FROM videos 
        WHERE thumbnail_url IS NOT NULL
        GROUP BY thumbnail_type
    ");
    $breakdown = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n📊 Thumbnail breakdown:\n";
    foreach ($breakdown as $row) {
        echo "   " . $row['thumbnail_type'] . ": " . $row['count'] . "\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
}
PHPEOF

echo "🚀 Running default thumbnail assignment..."
php /tmp/assign_defaults.php

echo "🗑️  Cleaning up..."
rm -f /tmp/assign_defaults.php

echo "✅ Default thumbnail assignment completed!"
EOF

print_success "🎉 Default thumbnail assignment completed!"

# Test the default thumbnail
print_status "🧪 Testing default thumbnail accessibility..."
default_url="https://www.bluefilmx.com/media/thumbnails/defaults/default-video.jpg"
status=$(curl -s -o /dev/null -w "%{http_code}" "$default_url" 2>/dev/null || echo "000")

if [[ "$status" == "200" ]]; then
    print_success "✅ Default thumbnail is accessible"
elif [[ "$status" == "404" ]]; then
    print_warning "⚠️  Default thumbnail not found, using existing thumbnail as fallback"
else
    print_warning "⚠️  Default thumbnail status: $status"
fi

# Verify final counts
print_status "🔍 Verifying final thumbnail counts..."
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "mysql -u bluerpcm_dbuser -pkingpatrick100 bluerpcm_bluefilmx -e 'SELECT COUNT(*) as total_with_thumbnails FROM videos WHERE thumbnail_url IS NOT NULL;'"

print_success "✅ Default thumbnail process completed!"
print_status "🌐 Your website should now show thumbnails for ALL videos!"
print_status "🔗 Test your website: https://www.bluefilmx.com"
