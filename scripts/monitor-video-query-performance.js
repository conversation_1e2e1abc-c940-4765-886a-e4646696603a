// Script to monitor video query performance
// Run with: node scripts/monitor-video-query-performance.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Create Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

async function monitorPerformance() {
  console.log('=== Video Query Performance Monitor ===');
  console.log('Timestamp:', new Date().toISOString());
  console.log('');

  try {
    // Get materialized view performance stats
    console.log('Materialized View Performance:');
    const { data: mvStats, error: mvError } = await supabase.rpc('monitor_mv_performance');
    
    if (mvError) {
      console.error('Error fetching materialized view stats:', mvError.message);
    } else {
      console.log('Last refresh:', mvStats.last_refresh ? new Date(mvStats.last_refresh).toISOString() : 'Never');
      console.log('Row count:', mvStats.row_count);
      console.log('Size:', formatBytes(mvStats.size_bytes));
      console.log('Index size:', formatBytes(mvStats.index_size_bytes));
      console.log('');
    }

    // Get slow queries
    console.log('Slow Queries:');
    const { data: slowQueries, error: slowError } = await supabase.rpc('get_slow_queries');
    
    if (slowError) {
      console.error('Error fetching slow queries:', slowError.message);
    } else if (slowQueries && slowQueries.length > 0) {
      slowQueries.forEach((q, i) => {
        if (q.query.includes('videos') || q.query.includes('profiles')) {
          console.log(`Query ${i + 1}:`);
          console.log(`  Duration: ${q.duration.toFixed(2)} ms`);
          console.log(`  Calls: ${q.calls}`);
          console.log(`  Rows: ${q.rows_returned}`);
          console.log(`  Query: ${truncateString(q.query, 100)}`);
          console.log('');
        }
      });
    } else {
      console.log('No slow queries related to videos found.');
      console.log('');
    }

    // Get cache hit ratio
    console.log('Cache Performance:');
    const { data: cacheHitRatio, error: cacheError } = await supabase.rpc('get_cache_hit_ratio');
    
    if (cacheError) {
      console.error('Error fetching cache hit ratio:', cacheError.message);
    } else {
      console.log('Cache hit ratio:', `${cacheHitRatio.toFixed(2)}%`);
      console.log('');
    }

    // Test query performance
    console.log('Query Performance Test:');
    
    // Test materialized view query
    console.log('Testing materialized view query...');
    const mvStart = Date.now();
    const { data: mvData, error: mvQueryError } = await supabase.rpc(
      'get_top_videos_with_profiles',
      { video_limit: 10, video_offset: 0 }
    );
    const mvDuration = Date.now() - mvStart;
    
    if (mvQueryError) {
      console.error('Error with materialized view query:', mvQueryError.message);
    } else {
      console.log(`Materialized view query: ${mvDuration} ms`);
      console.log(`Returned ${mvData.body.length} rows`);
    }
    
    // Test fallback query
    console.log('Testing fallback query...');
    const fbStart = Date.now();
    const { data: fbData, error: fbQueryError } = await supabase.rpc(
      'get_top_videos_fallback',
      { video_limit: 10, video_offset: 0 }
    );
    const fbDuration = Date.now() - fbStart;
    
    if (fbQueryError) {
      console.error('Error with fallback query:', fbQueryError.message);
    } else {
      console.log(`Fallback query: ${fbDuration} ms`);
      console.log(`Returned ${fbData.body.length} rows`);
      
      if (mvData && fbData) {
        console.log(`Performance improvement: ${((fbDuration - mvDuration) / fbDuration * 100).toFixed(2)}%`);
      }
    }
    
    console.log('');
    console.log('=== End of Report ===');
    
  } catch (error) {
    console.error('Monitoring error:', error.message);
  }
}

// Helper function to format bytes
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Helper function to truncate strings
function truncateString(str, maxLength) {
  if (str.length <= maxLength) return str;
  return str.substring(0, maxLength) + '...';
}

// Run the monitor
monitorPerformance();
