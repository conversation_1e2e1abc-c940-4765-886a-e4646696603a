#!/bin/bash

# BATCH Media Migration Script: Supabase → Namecheap
# Downloads videos in batches to minimize authentication prompts

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"
TEMP_DIR="./temp-media-batch"
BATCH_SIZE=20

print_status "🚀 Starting BATCH media migration (Reduced authentication!)"
print_status "📊 Processing $BATCH_SIZE videos per batch"

# Create temporary directory
mkdir -p "$TEMP_DIR/videos" "$TEMP_DIR/thumbnails"

# Get current video count on server
print_status "📋 Checking current videos on server..."
current_count=$(ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "find public_html/media/videos -name '*.mp4' -o -name '*.MP4' | wc -l" 2>/dev/null || echo "0")
print_status "📁 Found $current_count videos already on server"

# Get list of videos to migrate (skip existing ones)
print_status "📡 Getting videos to migrate..."
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "mysql -u bluerpcm_dbuser -pkingpatrick100 bluerpcm_bluefilmx -e \"
SELECT 
    CONCAT(id, '|', video_url, '|', thumbnail_url) as file_info
FROM videos 
WHERE video_url LIKE 'https://www.bluefilmx.com/media/%'
ORDER BY created_at DESC
LIMIT 50;
\"" | tail -n +2 > "$TEMP_DIR/videos_to_migrate.txt"

total_videos=$(wc -l < "$TEMP_DIR/videos_to_migrate.txt")
print_status "📊 Found $total_videos videos to process in this batch"

# Migration counters
success_count=0
error_count=0
batch_count=0

# Process videos in batches
while IFS='|' read -r video_id video_url thumbnail_url; do
    if [[ -n "$video_id" && -n "$video_url" && -n "$thumbnail_url" ]]; then
        ((batch_count++))
        
        # Extract filenames
        video_filename=$(basename "$video_url")
        thumbnail_filename=$(basename "$thumbnail_url")
        
        print_status "[$batch_count/$total_videos] Processing: $video_filename"
        
        # Convert to Supabase URLs
        video_supabase_path=$(echo "$video_filename" | sed 's/^[^_]*_//')
        thumbnail_supabase_path=$(echo "$thumbnail_filename" | sed 's/^[^_]*_//')
        
        # Use the main user ID from Supabase
        user_id="c2157ddd-2f88-436a-8ae7-f2b828b30145"
        
        video_supabase_url="https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/$user_id/$video_supabase_path"
        thumbnail_supabase_url="https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/$user_id/$thumbnail_supabase_path"
        
        # Download video
        if curl -s -f "$video_supabase_url" -o "$TEMP_DIR/videos/$video_filename"; then
            print_status "  ✅ Video downloaded"
        else
            print_warning "  ⚠️  Video download failed"
            continue
        fi
        
        # Download thumbnail
        if curl -s -f "$thumbnail_supabase_url" -o "$TEMP_DIR/thumbnails/$thumbnail_filename"; then
            print_status "  ✅ Thumbnail downloaded"
        else
            print_warning "  ⚠️  Thumbnail download failed"
        fi
        
        # Upload batch every BATCH_SIZE videos or at the end
        if (( batch_count % BATCH_SIZE == 0 )) || (( batch_count == total_videos )); then
            print_status "📤 Uploading batch of videos to server..."
            
            # Upload all videos in batch
            if ls "$TEMP_DIR/videos"/*.mp4 >/dev/null 2>&1; then
                scp -P $SSH_PORT "$TEMP_DIR/videos"/*.mp4 "$SSH_USER@$SSH_HOST:public_html/media/videos/" 2>/dev/null && {
                    video_count=$(ls "$TEMP_DIR/videos"/*.mp4 | wc -l)
                    print_success "  ✅ Uploaded $video_count videos"
                    ((success_count += video_count))
                    rm -f "$TEMP_DIR/videos"/*.mp4
                } || {
                    print_error "  ❌ Video batch upload failed"
                    ((error_count++))
                }
            fi
            
            # Upload all thumbnails in batch
            if ls "$TEMP_DIR/thumbnails"/*.jpg >/dev/null 2>&1 || ls "$TEMP_DIR/thumbnails"/*.png >/dev/null 2>&1; then
                scp -P $SSH_PORT "$TEMP_DIR/thumbnails"/*.{jpg,png} "$SSH_USER@$SSH_HOST:public_html/media/thumbnails/" 2>/dev/null && {
                    thumb_count=$(ls "$TEMP_DIR/thumbnails"/*.{jpg,png} 2>/dev/null | wc -l)
                    print_success "  ✅ Uploaded $thumb_count thumbnails"
                    rm -f "$TEMP_DIR/thumbnails"/*.{jpg,png}
                } || {
                    print_error "  ❌ Thumbnail batch upload failed"
                }
            fi
            
            # Progress update
            new_count=$(ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "find public_html/media/videos -name '*.mp4' -o -name '*.MP4' | wc -l" 2>/dev/null || echo "0")
            print_status "📊 Progress: $batch_count/$total_videos processed, $new_count total videos on server"
            echo ""
        fi
        
        # Small delay
        sleep 0.5
    fi
done < "$TEMP_DIR/videos_to_migrate.txt"

# Clean up
rm -rf "$TEMP_DIR"

# Final summary
print_success "🎉 Batch migration complete!"
final_count=$(ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "find public_html/media/videos -name '*.mp4' -o -name '*.MP4' | wc -l" 2>/dev/null || echo "0")
print_status "📊 Final count: $final_count videos on server"
print_status "✅ Successfully migrated: $success_count videos in this batch"

if [ "$final_count" -gt "$current_count" ]; then
    print_success "🎉 Migration successful! Added $((final_count - current_count)) new videos"
    print_status "🔄 Run this script again to continue migrating more videos"
else
    print_warning "⚠️  No new videos were added. Check the logs above for issues."
fi

print_status "🌐 Test your website: https://www.bluefilmx.com"
