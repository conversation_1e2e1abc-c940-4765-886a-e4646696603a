#!/bin/bash

# Vercel Deployment Script with Environment Variable Validation
# This script helps ensure proper deployment to Vercel with all required environment variables

echo "🚀 Starting Vercel deployment process..."

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI is not installed. Please install it first:"
    echo "npm i -g vercel"
    exit 1
fi

# Check if we're logged in to Vercel
if ! vercel whoami &> /dev/null; then
    echo "❌ Not logged in to Vercel. Please login first:"
    echo "vercel login"
    exit 1
fi

echo "✅ Vercel CLI is ready"

# Validate local environment variables
echo "🔍 Checking local environment variables..."

if [ -z "$VITE_SUPABASE_URL" ] && [ ! -f ".env.local" ]; then
    echo "❌ Missing VITE_SUPABASE_URL. Please create .env.local file or set environment variable."
    exit 1
fi

if [ -z "$VITE_SUPABASE_ANON_KEY" ] && [ ! -f ".env.local" ]; then
    echo "❌ Missing VITE_SUPABASE_ANON_KEY. Please create .env.local file or set environment variable."
    exit 1
fi

echo "✅ Local environment variables look good"

# Build the project locally first to catch any build errors
echo "🔨 Building project locally..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Local build failed. Please fix build errors before deploying."
    exit 1
fi

echo "✅ Local build successful"

# Set Vercel environment variables if they don't exist
echo "🔧 Setting up Vercel environment variables..."

# Read from .env.local if it exists
if [ -f ".env.local" ]; then
    source .env.local
fi

# Set environment variables in Vercel
vercel env add VITE_SUPABASE_URL production <<< "$VITE_SUPABASE_URL" 2>/dev/null || echo "VITE_SUPABASE_URL already exists in Vercel"
vercel env add VITE_SUPABASE_ANON_KEY production <<< "$VITE_SUPABASE_ANON_KEY" 2>/dev/null || echo "VITE_SUPABASE_ANON_KEY already exists in Vercel"

echo "✅ Environment variables configured"

# Deploy to Vercel
echo "🚀 Deploying to Vercel..."
vercel --prod

if [ $? -eq 0 ]; then
    echo "✅ Deployment successful!"
    echo "🌐 Your app should be live on Vercel"
    echo ""
    echo "📋 Post-deployment checklist:"
    echo "1. Check that the site loads correctly"
    echo "2. Verify environment variables are working (check the debug panel in production)"
    echo "3. Test authentication and database connections"
    echo "4. Check browser console for any errors"
else
    echo "❌ Deployment failed. Please check the error messages above."
    exit 1
fi
