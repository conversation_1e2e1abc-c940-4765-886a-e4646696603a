#!/bin/bash

# AUTOMATED Media Migration Script: Supabase → Namecheap
# Downloads ALL videos and thumbnails WITHOUT password prompts

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration with SSH key
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"
SSH_KEY="$HOME/.ssh/bluefilm_migration"
TEMP_DIR="./temp-media-automated"

# SSH and SCP commands with key authentication
SSH_CMD="ssh -i $SSH_KEY -p $SSH_PORT -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=ERROR"
SCP_CMD="scp -i $SSH_KEY -P $SSH_PORT -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=ERROR"

print_status "🚀 Starting AUTOMATED media migration (NO PASSWORD PROMPTS!)"
print_status "📊 Target: ALL 248 videos and thumbnails"

# Create temporary directory
mkdir -p "$TEMP_DIR/videos" "$TEMP_DIR/thumbnails"

# Test passwordless SSH
print_status "🔑 Testing passwordless SSH connection..."
if $SSH_CMD $SSH_USER@$SSH_HOST "echo 'SSH connection successful!'" >/dev/null 2>&1; then
    print_success "✅ Passwordless SSH working!"
else
    print_error "❌ SSH key authentication failed. Please check SSH setup."
    exit 1
fi

# Get list of videos already on server
print_status "📋 Getting list of videos already on server..."
$SSH_CMD $SSH_USER@$SSH_HOST "find public_html/media/videos -name '*.mp4' -o -name '*.MP4' | xargs -I {} basename {}" > "$TEMP_DIR/existing_videos.txt" 2>/dev/null || touch "$TEMP_DIR/existing_videos.txt"

existing_count=$(wc -l < "$TEMP_DIR/existing_videos.txt")
print_status "📁 Found $existing_count videos already on server"

# Get all video URLs from database
print_status "📡 Getting all video URLs from database..."
$SSH_CMD $SSH_USER@$SSH_HOST "mysql -u bluerpcm_dbuser -pkingpatrick100 bluerpcm_bluefilmx -e \"
SELECT 
    CONCAT(id, '|', video_url, '|', thumbnail_url) as file_info
FROM videos 
WHERE video_url LIKE 'https://www.bluefilmx.com/media/%'
ORDER BY created_at DESC;
\"" | tail -n +2 > "$TEMP_DIR/all_videos.txt"

total_videos=$(wc -l < "$TEMP_DIR/all_videos.txt")
print_status "📊 Found $total_videos videos in database"

# Migration counters
success_count=0
error_count=0
skipped_count=0
processed_count=0

# Function to migrate a single video
migrate_video() {
    local video_id="$1"
    local video_url="$2"
    local thumbnail_url="$3"
    
    # Extract filenames
    local video_filename=$(basename "$video_url")
    local thumbnail_filename=$(basename "$thumbnail_url")
    
    # Check if video already exists on server
    if grep -q "^$video_filename$" "$TEMP_DIR/existing_videos.txt"; then
        print_status "  ⏭️  Video already exists: $video_filename"
        ((skipped_count++))
        return 0
    fi
    
    print_status "  📥 Processing: $video_filename"
    
    # Convert to Supabase URLs
    local video_supabase_path=$(echo "$video_filename" | sed 's/^[^_]*_//')
    local thumbnail_supabase_path=$(echo "$thumbnail_filename" | sed 's/^[^_]*_//')
    
    # Use the main user ID from Supabase
    local user_id="c2157ddd-2f88-436a-8ae7-f2b828b30145"
    
    local video_supabase_url="https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/$user_id/$video_supabase_path"
    local thumbnail_supabase_url="https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/$user_id/$thumbnail_supabase_path"
    
    local video_success=false
    local thumbnail_success=false
    
    # Try to download and upload video
    if curl -s -f "$video_supabase_url" -o "$TEMP_DIR/videos/$video_filename"; then
        print_status "    ✅ Video downloaded"
        if $SCP_CMD "$TEMP_DIR/videos/$video_filename" "$SSH_USER@$SSH_HOST:public_html/media/videos/$video_filename" >/dev/null 2>&1; then
            print_success "    ✅ Video uploaded"
            video_success=true
        else
            print_error "    ❌ Video upload failed"
        fi
        rm -f "$TEMP_DIR/videos/$video_filename"
    else
        print_warning "    ⚠️  Video download failed (may not exist in Supabase)"
    fi
    
    # Try to download and upload thumbnail
    if curl -s -f "$thumbnail_supabase_url" -o "$TEMP_DIR/thumbnails/$thumbnail_filename"; then
        print_status "    ✅ Thumbnail downloaded"
        if $SCP_CMD "$TEMP_DIR/thumbnails/$thumbnail_filename" "$SSH_USER@$SSH_HOST:public_html/media/thumbnails/$thumbnail_filename" >/dev/null 2>&1; then
            print_success "    ✅ Thumbnail uploaded"
            thumbnail_success=true
        else
            print_error "    ❌ Thumbnail upload failed"
        fi
        rm -f "$TEMP_DIR/thumbnails/$thumbnail_filename"
    else
        print_warning "    ⚠️  Thumbnail download failed (may not exist in Supabase)"
    fi
    
    # Count results
    if $video_success || $thumbnail_success; then
        ((success_count++))
    else
        ((error_count++))
    fi
}

# Process all videos
print_status "🔄 Starting AUTOMATED migration of all videos..."
echo ""

while IFS='|' read -r video_id video_url thumbnail_url; do
    if [[ -n "$video_id" && -n "$video_url" && -n "$thumbnail_url" ]]; then
        ((processed_count++))
        
        # Progress indicator
        progress="[$processed_count/$total_videos]"
        print_status "$progress Processing video: $video_id"
        
        # Migrate the video
        migrate_video "$video_id" "$video_url" "$thumbnail_url"
        
        echo ""
        
        # Progress summary every 10 videos
        if (( processed_count % 10 == 0 )); then
            print_status "📊 Progress: $processed_count/$total_videos processed"
            print_status "   ✅ Successful: $success_count"
            print_status "   ⏭️  Skipped: $skipped_count"
            print_status "   ❌ Failed: $error_count"
            
            # Check current server count
            current_count=$($SSH_CMD $SSH_USER@$SSH_HOST "find public_html/media/videos -name '*.mp4' -o -name '*.MP4' | wc -l" 2>/dev/null || echo "0")
            print_status "   📁 Videos on server: $current_count"
            echo ""
        fi
        
        # Small delay to avoid overwhelming servers
        sleep 1
    fi
done < "$TEMP_DIR/all_videos.txt"

# Clean up
rm -rf "$TEMP_DIR"

# Final summary
print_success "🎉 AUTOMATED Migration complete!"
echo ""
print_status "📊 Final Summary:"
print_status "   📁 Total videos processed: $processed_count"
print_success "   ✅ Successfully migrated: $success_count"
print_status "   ⏭️  Already existed (skipped): $skipped_count"
print_warning "   ❌ Failed migrations: $error_count"

# Calculate final counts
print_status "🔍 Verifying final counts on server..."
final_video_count=$($SSH_CMD $SSH_USER@$SSH_HOST "find public_html/media/videos -name '*.mp4' -o -name '*.MP4' | wc -l" 2>/dev/null || echo "0")
final_thumbnail_count=$($SSH_CMD $SSH_USER@$SSH_HOST "find public_html/media/thumbnails -name '*.jpg' -o -name '*.png' | wc -l" 2>/dev/null || echo "0")

print_success "🎯 Final server counts:"
print_success "   📹 Videos on server: $final_video_count"
print_success "   🖼️  Thumbnails on server: $final_thumbnail_count"

if [ "$final_video_count" -gt 200 ]; then
    print_success "🎉 Excellent! Most videos have been migrated successfully!"
    print_status "🌐 Your website should now display videos and thumbnails properly."
    print_status "🔗 Test your website: https://www.bluefilmx.com"
else
    print_warning "⚠️  Some videos may still be missing. Check the logs above for details."
fi

print_success "✅ AUTOMATED migration completed WITHOUT password prompts!"
