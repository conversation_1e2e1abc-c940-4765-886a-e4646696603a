#!/usr/bin/env node

/**
 * Media Migration Script: Supabase → Namecheap
 * Downloads all videos and thumbnails from Supabase and uploads to Namecheap server
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Configuration
const SUPABASE_URL = 'https://vsnsglgyapexhwyfylic.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzbnNnbGd5YXBleGh3eWZ5bGljIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExOTEsImV4cCI6MjA2MDgzNzE5MX0.6CQWpMT14h2kaIOk1_LMECuJrfRdmiGRo3vGyEDW9tM';

const SSH_CONFIG = {
  host: 'premium34.web-hosting.com',
  port: '21098',
  user: 'bluerpcm',
  remotePath: 'public_html/media'
};

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Create local directories
const LOCAL_TEMP_DIR = path.join(__dirname, '../temp-media');
const LOCAL_VIDEOS_DIR = path.join(LOCAL_TEMP_DIR, 'videos');
const LOCAL_THUMBNAILS_DIR = path.join(LOCAL_TEMP_DIR, 'thumbnails');

function createLocalDirs() {
  [LOCAL_TEMP_DIR, LOCAL_VIDEOS_DIR, LOCAL_THUMBNAILS_DIR].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    warning: '\x1b[33m', // Yellow
    error: '\x1b[31m',   // Red
    reset: '\x1b[0m'     // Reset
  };
  
  console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
}

async function downloadFile(url, localPath) {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const buffer = await response.arrayBuffer();
    fs.writeFileSync(localPath, Buffer.from(buffer));
    return true;
  } catch (error) {
    log(`Failed to download ${url}: ${error.message}`, 'error');
    return false;
  }
}

async function uploadToServer(localPath, remotePath) {
  try {
    const scpCommand = `scp -P ${SSH_CONFIG.port} "${localPath}" ${SSH_CONFIG.user}@${SSH_CONFIG.host}:${SSH_CONFIG.remotePath}/${remotePath}`;
    await execAsync(scpCommand);
    return true;
  } catch (error) {
    log(`Failed to upload ${localPath}: ${error.message}`, 'error');
    return false;
  }
}

async function getMediaUrls() {
  try {
    const { data: videos, error } = await supabase
      .from('videos')
      .select('id, video_url, thumbnail_url')
      .not('video_url', 'is', null);

    if (error) throw error;
    
    log(`Found ${videos.length} videos to migrate`, 'info');
    return videos;
  } catch (error) {
    log(`Failed to fetch video URLs: ${error.message}`, 'error');
    return [];
  }
}

function extractFilename(url) {
  if (!url) return null;
  
  // Extract filename from Supabase URL
  const match = url.match(/\/([^\/]+)$/);
  return match ? match[1] : null;
}

async function migrateMedia() {
  log('🚀 Starting media migration from Supabase to Namecheap...', 'info');
  
  // Create local directories
  createLocalDirs();
  
  // Get all media URLs from database
  const videos = await getMediaUrls();
  if (videos.length === 0) {
    log('No videos found to migrate', 'warning');
    return;
  }
  
  let successCount = 0;
  let errorCount = 0;
  
  for (let i = 0; i < videos.length; i++) {
    const video = videos[i];
    const progress = `${i + 1}/${videos.length}`;
    
    log(`[${progress}] Processing video: ${video.id}`, 'info');
    
    // Process video file
    if (video.video_url) {
      const videoFilename = extractFilename(video.video_url);
      if (videoFilename) {
        const localVideoPath = path.join(LOCAL_VIDEOS_DIR, videoFilename);
        
        log(`  Downloading video: ${videoFilename}`, 'info');
        const videoDownloaded = await downloadFile(video.video_url, localVideoPath);
        
        if (videoDownloaded) {
          log(`  Uploading video to server...`, 'info');
          const videoUploaded = await uploadToServer(localVideoPath, `videos/${videoFilename}`);
          
          if (videoUploaded) {
            log(`  ✅ Video migrated successfully`, 'success');
          } else {
            log(`  ❌ Video upload failed`, 'error');
            errorCount++;
          }
          
          // Clean up local file
          fs.unlinkSync(localVideoPath);
        } else {
          errorCount++;
        }
      }
    }
    
    // Process thumbnail file
    if (video.thumbnail_url) {
      const thumbnailFilename = extractFilename(video.thumbnail_url);
      if (thumbnailFilename) {
        const localThumbnailPath = path.join(LOCAL_THUMBNAILS_DIR, thumbnailFilename);
        
        log(`  Downloading thumbnail: ${thumbnailFilename}`, 'info');
        const thumbnailDownloaded = await downloadFile(video.thumbnail_url, localThumbnailPath);
        
        if (thumbnailDownloaded) {
          log(`  Uploading thumbnail to server...`, 'info');
          const thumbnailUploaded = await uploadToServer(localThumbnailPath, `thumbnails/${thumbnailFilename}`);
          
          if (thumbnailUploaded) {
            log(`  ✅ Thumbnail migrated successfully`, 'success');
            successCount++;
          } else {
            log(`  ❌ Thumbnail upload failed`, 'error');
            errorCount++;
          }
          
          // Clean up local file
          fs.unlinkSync(localThumbnailPath);
        } else {
          errorCount++;
        }
      }
    }
    
    // Add small delay to avoid overwhelming the servers
    if (i < videos.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  // Clean up temp directory
  fs.rmSync(LOCAL_TEMP_DIR, { recursive: true, force: true });
  
  log(`🎉 Migration complete!`, 'success');
  log(`✅ Successfully migrated: ${successCount} files`, 'success');
  log(`❌ Failed migrations: ${errorCount} files`, errorCount > 0 ? 'warning' : 'info');
  
  if (errorCount === 0) {
    log('🔄 All files migrated successfully! You can now test your website.', 'success');
  } else {
    log('⚠️  Some files failed to migrate. Check the logs above for details.', 'warning');
  }
}

// Run migration
migrateMedia().catch(error => {
  log(`Migration failed: ${error.message}`, 'error');
  process.exit(1);
});
