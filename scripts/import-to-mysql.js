#!/usr/bin/env node

/**
 * Import exported Supabase data to MySQL
 */

import { readFileSync, writeFileSync } from 'fs';
import { resolve } from 'path';

const exportDir = resolve(process.cwd(), 'migration-export');
const outputDir = resolve(process.cwd(), 'mysql-import');

// Ensure output directory exists
import { mkdirSync } from 'fs';
try {
  mkdirSync(outputDir, { recursive: true });
} catch (error) {
  // Directory already exists
}

function escapeString(str) {
  if (str === null || str === undefined) return 'NULL';
  if (typeof str === 'boolean') return str ? '1' : '0';
  if (typeof str === 'number') return str.toString();
  
  // Escape single quotes and backslashes
  return "'" + str.toString().replace(/\\/g, '\\\\').replace(/'/g, "\\'") + "'";
}

function convertTimestamp(timestamp) {
  if (!timestamp) return 'NULL';
  try {
    const date = new Date(timestamp);
    return "'" + date.toISOString().slice(0, 19).replace('T', ' ') + "'";
  } catch (error) {
    return 'NULL';
  }
}

function generateUUID() {
  return 'UUID()';
}

function createInsertStatements(tableName, data) {
  if (!data || data.length === 0) {
    return `-- No data to import for ${tableName}\n`;
  }

  let sql = `-- Import data for ${tableName}\n`;
  sql += `DELETE FROM ${tableName};\n`;
  
  const columns = Object.keys(data[0]);
  sql += `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES\n`;
  
  const values = data.map((row, index) => {
    const rowValues = columns.map(col => {
      const value = row[col];
      
      // Handle special cases
      if (col.includes('_at') || col === 'created_at' || col === 'updated_at') {
        return convertTimestamp(value);
      }
      
      if (col === 'id' && !value) {
        return generateUUID();
      }
      
      return escapeString(value);
    });
    
    const isLast = index === data.length - 1;
    return `  (${rowValues.join(', ')})${isLast ? ';' : ','}`;
  });
  
  sql += values.join('\n') + '\n\n';
  return sql;
}

async function importData() {
  console.log('🚀 Starting MySQL import script generation...\n');
  
  const tables = [
    'profiles',
    'categories', 
    'videos',
    'collections',
    'collection_videos',
    'video_tags',
    'comments',
    'comment_replies'
  ];
  
  let fullSQL = `-- BlueFilmX Data Import Script
-- Generated on ${new Date().toISOString()}

SET FOREIGN_KEY_CHECKS = 0;

`;

  for (const tableName of tables) {
    try {
      const filePath = resolve(exportDir, `${tableName}.json`);
      const data = JSON.parse(readFileSync(filePath, 'utf8'));
      
      console.log(`📊 Processing ${tableName}: ${data.length} records`);
      
      const insertSQL = createInsertStatements(tableName, data);
      fullSQL += insertSQL;
      
    } catch (error) {
      console.log(`⚠️  Warning: Could not process ${tableName}: ${error.message}`);
      fullSQL += `-- Error processing ${tableName}: ${error.message}\n\n`;
    }
  }
  
  fullSQL += `SET FOREIGN_KEY_CHECKS = 1;

-- Update video counts in collections
UPDATE collections c SET video_count = (
  SELECT COUNT(*) FROM collection_videos cv WHERE cv.collection_id = c.id
);

COMMIT;
`;

  // Write the complete import script
  const outputPath = resolve(outputDir, 'import-data.sql');
  writeFileSync(outputPath, fullSQL);
  
  console.log(`\n✅ MySQL import script generated: ${outputPath}`);
  console.log('📋 Next steps:');
  console.log('1. Create MySQL database in Namecheap cPanel');
  console.log('2. Run the schema creation script');
  console.log('3. Run the data import script');
  
  return outputPath;
}

// Run import
importData().catch(console.error);
