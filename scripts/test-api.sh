#!/bin/bash

# Test BlueFilmX API Endpoints
# Run this script to verify your API is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

API_BASE_URL="https://www.bluefilmx.com/api"

print_status "🧪 Testing BlueFilmX API endpoints..."
echo ""

# Test 1: Videos API
print_status "1. Testing Videos API..."
response=$(curl -s -w "%{http_code}" -o /tmp/videos_response.json "$API_BASE_URL/videos.php?limit=3")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    video_count=$(cat /tmp/videos_response.json | grep -o '"videos":\[' | wc -l)
    if [ "$video_count" -gt 0 ]; then
        print_success "Videos API working! ✅"
        echo "   Response: $(cat /tmp/videos_response.json | head -c 100)..."
    else
        print_warning "Videos API responding but no videos found"
    fi
else
    print_error "Videos API failed (HTTP $http_code)"
    echo "   Response: $(cat /tmp/videos_response.json)"
fi

echo ""

# Test 2: Categories API
print_status "2. Testing Categories API..."
response=$(curl -s -w "%{http_code}" -o /tmp/categories_response.json "$API_BASE_URL/categories.php")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    category_count=$(cat /tmp/categories_response.json | grep -o '"name":' | wc -l)
    if [ "$category_count" -gt 0 ]; then
        print_success "Categories API working! ✅"
        echo "   Found $category_count categories"
    else
        print_warning "Categories API responding but no categories found"
    fi
else
    print_error "Categories API failed (HTTP $http_code)"
    echo "   Response: $(cat /tmp/categories_response.json)"
fi

echo ""

# Test 3: Single Video API
print_status "3. Testing Single Video API..."
# First get a video ID from the videos list
video_id=$(cat /tmp/videos_response.json | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

if [ -n "$video_id" ]; then
    response=$(curl -s -w "%{http_code}" -o /tmp/single_video_response.json "$API_BASE_URL/videos.php/$video_id")
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        print_success "Single Video API working! ✅"
        echo "   Tested with video ID: $video_id"
    else
        print_error "Single Video API failed (HTTP $http_code)"
        echo "   Response: $(cat /tmp/single_video_response.json)"
    fi
else
    print_warning "No video ID found to test single video API"
fi

echo ""

# Test 4: Database Connection Test
print_status "4. Testing Database Connection..."
response=$(curl -s -w "%{http_code}" -o /tmp/db_test.json "$API_BASE_URL/videos.php?limit=1")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    if grep -q "success.*true" /tmp/db_test.json; then
        print_success "Database connection working! ✅"
    else
        print_warning "API responding but check database connection"
    fi
else
    print_error "Database connection test failed"
fi

echo ""

# Summary
print_status "📊 Test Summary:"
echo ""

if [ -f /tmp/videos_response.json ] && grep -q "videos" /tmp/videos_response.json; then
    echo "✅ Videos API: Working"
else
    echo "❌ Videos API: Failed"
fi

if [ -f /tmp/categories_response.json ] && grep -q "name" /tmp/categories_response.json; then
    echo "✅ Categories API: Working"
else
    echo "❌ Categories API: Failed"
fi

if [ -f /tmp/single_video_response.json ] && grep -q "id" /tmp/single_video_response.json; then
    echo "✅ Single Video API: Working"
else
    echo "❌ Single Video API: Failed"
fi

echo ""
print_status "🌐 Next step: Test your website at https://www.bluefilmx.com"

# Clean up temp files
rm -f /tmp/videos_response.json /tmp/categories_response.json /tmp/single_video_response.json /tmp/db_test.json
