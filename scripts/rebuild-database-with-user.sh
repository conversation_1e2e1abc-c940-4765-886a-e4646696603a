#!/bin/bash

# Rebuild Database from Actual Files (with proper user_id)
# Creates database records based on the actual video files on server

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"

print_status "🔧 Starting database rebuild with proper user associations..."

# Create rebuild script with user handling
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST << 'EOF'
#!/bin/bash

echo "🔍 Analyzing actual files and database structure..."

# Get all actual files
find public_html/media/videos -name "*.mp4" -o -name "*.MP4" | sed 's|public_html/media/videos/||' | sort > /tmp/actual_videos.txt
find public_html/media/thumbnails -name "*.jpg" -o -name "*.png" | sed 's|public_html/media/thumbnails/||' | sort > /tmp/actual_thumbnails.txt

video_count=$(wc -l < /tmp/actual_videos.txt)
thumbnail_count=$(wc -l < /tmp/actual_thumbnails.txt)

echo "   📹 Found $video_count video files"
echo "   🖼️  Found $thumbnail_count thumbnail files"

# Create PHP script to rebuild database with proper user handling
cat > /tmp/rebuild_with_user.php << 'PHPEOF'
<?php
$host = 'localhost';
$dbname = 'bluerpcm_bluefilmx';
$username = 'bluerpcm_dbuser';
$password = 'kingpatrick100';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔗 Connected to database\n";
    
    // Get actual files
    $actual_videos = file('/tmp/actual_videos.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $actual_thumbnails = file('/tmp/actual_thumbnails.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    echo "📹 Processing " . count($actual_videos) . " video files\n";
    echo "🖼️  Processing " . count($actual_thumbnails) . " thumbnail files\n";
    
    // Get or create a default user
    $stmt = $pdo->query("SELECT id FROM profiles LIMIT 1");
    $default_user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$default_user) {
        echo "👤 Creating default user profile...\n";
        $default_user_id = 'c2157ddd-2f88-436a-8ae7-f2b828b30145'; // Main user ID
        $pdo->prepare("
            INSERT INTO profiles (id, email, username, full_name, created_at, updated_at) 
            VALUES (?, '<EMAIL>', 'admin', 'BlueFilmX Admin', NOW(), NOW())
            ON DUPLICATE KEY UPDATE updated_at = NOW()
        ")->execute([$default_user_id]);
        $default_user = ['id' => $default_user_id];
    }
    
    $user_id = $default_user['id'];
    echo "👤 Using user ID: $user_id\n";
    
    // Create lookup for thumbnails by UUID
    $thumbnail_lookup = [];
    foreach ($actual_thumbnails as $thumbnail) {
        if (preg_match('/^([a-f0-9-]{36})_(.+)$/', $thumbnail, $matches)) {
            $uuid = $matches[1];
            $thumbnail_lookup[$uuid] = $thumbnail;
        }
    }
    
    echo "🔍 Built thumbnail lookup: " . count($thumbnail_lookup) . " entries\n";
    
    // Backup existing videos table
    echo "💾 Creating backup of existing videos table...\n";
    $backup_table = "videos_backup_" . date('Ymd_His');
    $pdo->exec("CREATE TABLE $backup_table AS SELECT * FROM videos");
    echo "   Backup created: $backup_table\n";
    
    // Clear existing videos (disable foreign key checks temporarily)
    echo "🗑️  Clearing existing video records...\n";
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    $pdo->exec("DELETE FROM videos");
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // Process each video file
    $inserted_count = 0;
    $base_video_url = 'https://www.bluefilmx.com/media/videos/';
    $base_thumbnail_url = 'https://www.bluefilmx.com/media/thumbnails/';
    
    foreach ($actual_videos as $video_file) {
        if (preg_match('/^([a-f0-9-]{36})_(.+)$/', $video_file, $matches)) {
            $uuid = $matches[1];
            $filename_part = $matches[2];
            
            // Generate title from filename
            $title = str_replace(['_', '-', '.mp4', '.MP4'], [' ', ' ', '', ''], $filename_part);
            $title = ucwords(strtolower($title));
            $title = preg_replace('/\s+/', ' ', trim($title));
            
            // Clean up title
            $title = preg_replace('/\d{13,}/', '', $title); // Remove long timestamps
            $title = preg_replace('/\s+/', ' ', trim($title));
            
            // Limit title length
            if (strlen($title) > 100) {
                $title = substr($title, 0, 97) . '...';
            }
            
            if (empty($title)) {
                $title = 'Video ' . substr($uuid, 0, 8);
            }
            
            // Find matching thumbnail
            $thumbnail_url = null;
            if (isset($thumbnail_lookup[$uuid])) {
                $thumbnail_url = $base_thumbnail_url . $thumbnail_lookup[$uuid];
            }
            
            // Insert video record
            $stmt = $pdo->prepare("
                INSERT INTO videos (
                    id, user_id, title, description, video_url, thumbnail_url, 
                    category, views, created_at, updated_at
                ) VALUES (
                    :id, :user_id, :title, :description, :video_url, :thumbnail_url,
                    :category, :views, :created_at, :updated_at
                )
            ");
            
            $stmt->execute([
                'id' => $uuid,
                'user_id' => $user_id,
                'title' => $title,
                'description' => 'Video: ' . $title,
                'video_url' => $base_video_url . $video_file,
                'thumbnail_url' => $thumbnail_url,
                'category' => 'new',
                'views' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            $inserted_count++;
            
            if ($inserted_count % 25 == 0) {
                echo "   ✅ Inserted $inserted_count records...\n";
            }
        }
    }
    
    echo "\n📊 Database rebuild complete!\n";
    echo "   ✅ Inserted records: $inserted_count\n";
    
    // Verify results
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   📹 Total videos in database: " . $result['count'] . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE thumbnail_url IS NOT NULL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   🖼️  Videos with thumbnails: " . $result['count'] . "\n";
    
    // Show sample records
    echo "\n📋 Sample new records:\n";
    $stmt = $pdo->query("SELECT id, title, video_url FROM videos LIMIT 3");
    $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($samples as $i => $sample) {
        echo "   " . ($i+1) . ". " . $sample['title'] . "\n";
        echo "      URL: " . substr($sample['video_url'], 45) . "\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
}
PHPEOF

echo "🚀 Running database rebuild with user associations..."
php /tmp/rebuild_with_user.php

echo "🗑️  Cleaning up..."
rm -f /tmp/actual_videos.txt /tmp/actual_thumbnails.txt /tmp/rebuild_with_user.php

echo "✅ Database rebuild completed!"
EOF

print_success "🎉 Database rebuild completed!"

# Test the results
print_status "🧪 Testing rebuilt database..."
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "mysql -u bluerpcm_dbuser -pkingpatrick100 bluerpcm_bluefilmx -e 'SELECT COUNT(*) as total_videos FROM videos;'"

print_success "✅ Database rebuild process completed!"
print_status "🌐 Your website should now display all uploaded videos correctly!"
print_status "🔗 Test your website: https://www.bluefilmx.com"
