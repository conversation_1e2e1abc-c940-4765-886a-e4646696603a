#!/bin/bash

# Rebuild Database from ALL Files (flexible UUID handling)
# Creates database records for ALL video files, generating UUIDs as needed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"

print_status "🔧 Starting comprehensive database rebuild for ALL files..."

# Create comprehensive rebuild script
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST << 'EOF'
#!/bin/bash

echo "🔍 Analyzing ALL files on server..."

# Get all actual files
find public_html/media/videos -name "*.mp4" -o -name "*.MP4" | sed 's|public_html/media/videos/||' | sort > /tmp/all_videos.txt
find public_html/media/thumbnails -name "*.jpg" -o -name "*.png" | sed 's|public_html/media/thumbnails/||' | sort > /tmp/all_thumbnails.txt

video_count=$(wc -l < /tmp/all_videos.txt)
thumbnail_count=$(wc -l < /tmp/all_thumbnails.txt)

echo "   📹 Found $video_count video files"
echo "   🖼️  Found $thumbnail_count thumbnail files"

# Create PHP script to handle ALL files
cat > /tmp/rebuild_all_files.php << 'PHPEOF'
<?php
$host = 'localhost';
$dbname = 'bluerpcm_bluefilmx';
$username = 'bluerpcm_dbuser';
$password = 'kingpatrick100';

function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔗 Connected to database\n";
    
    // Get actual files
    $all_videos = file('/tmp/all_videos.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $all_thumbnails = file('/tmp/all_thumbnails.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    echo "📹 Processing " . count($all_videos) . " video files\n";
    echo "🖼️  Processing " . count($all_thumbnails) . " thumbnail files\n";
    
    // Get default user
    $stmt = $pdo->query("SELECT id FROM profiles LIMIT 1");
    $default_user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$default_user) {
        $default_user_id = 'c2157ddd-2f88-436a-8ae7-f2b828b30145';
        $pdo->prepare("
            INSERT INTO profiles (id, email, username, full_name, created_at, updated_at) 
            VALUES (?, '<EMAIL>', 'admin', 'BlueFilmX Admin', NOW(), NOW())
            ON DUPLICATE KEY UPDATE updated_at = NOW()
        ")->execute([$default_user_id]);
        $default_user = ['id' => $default_user_id];
    }
    
    $user_id = $default_user['id'];
    echo "👤 Using user ID: $user_id\n";
    
    // Create comprehensive thumbnail lookup
    $thumbnail_lookup = [];
    foreach ($all_thumbnails as $thumbnail) {
        // Try UUID pattern first
        if (preg_match('/^([a-f0-9-]{36})_(.+)$/', $thumbnail, $matches)) {
            $uuid = $matches[1];
            $thumbnail_lookup[$uuid] = $thumbnail;
        } else {
            // Use filename as key for non-UUID files
            $key = pathinfo($thumbnail, PATHINFO_FILENAME);
            $thumbnail_lookup[$key] = $thumbnail;
        }
    }
    
    echo "🔍 Built thumbnail lookup: " . count($thumbnail_lookup) . " entries\n";
    
    // Backup and clear existing videos
    echo "💾 Creating backup and clearing existing records...\n";
    $backup_table = "videos_backup_all_" . date('Ymd_His');
    $pdo->exec("CREATE TABLE $backup_table AS SELECT * FROM videos");
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    $pdo->exec("DELETE FROM videos");
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // Process each video file
    $inserted_count = 0;
    $uuid_files = 0;
    $generated_uuid_files = 0;
    $base_video_url = 'https://www.bluefilmx.com/media/videos/';
    $base_thumbnail_url = 'https://www.bluefilmx.com/media/thumbnails/';
    
    foreach ($all_videos as $video_file) {
        $uuid = null;
        $filename_part = '';
        
        // Try to extract UUID from filename
        if (preg_match('/^([a-f0-9-]{36})_(.+)$/', $video_file, $matches)) {
            $uuid = $matches[1];
            $filename_part = $matches[2];
            $uuid_files++;
        } else {
            // Generate UUID for non-UUID files
            $uuid = generateUUID();
            $filename_part = pathinfo($video_file, PATHINFO_FILENAME);
            $generated_uuid_files++;
        }
        
        // Generate title from filename
        $title = str_replace(['_', '-', '.mp4', '.MP4'], [' ', ' ', '', ''], $filename_part);
        $title = ucwords(strtolower($title));
        $title = preg_replace('/\d{13,}/', '', $title); // Remove timestamps
        $title = preg_replace('/\s+/', ' ', trim($title));
        
        if (strlen($title) > 100) {
            $title = substr($title, 0, 97) . '...';
        }
        
        if (empty($title)) {
            $title = 'Video ' . substr($uuid, 0, 8);
        }
        
        // Find matching thumbnail
        $thumbnail_url = null;
        
        // Try UUID match first
        if (isset($thumbnail_lookup[$uuid])) {
            $thumbnail_url = $base_thumbnail_url . $thumbnail_lookup[$uuid];
        } else {
            // Try filename match
            $video_base = pathinfo($video_file, PATHINFO_FILENAME);
            if (isset($thumbnail_lookup[$video_base])) {
                $thumbnail_url = $base_thumbnail_url . $thumbnail_lookup[$video_base];
            }
        }
        
        // Insert video record
        $stmt = $pdo->prepare("
            INSERT INTO videos (
                id, user_id, title, description, video_url, thumbnail_url, 
                category, views, created_at, updated_at
            ) VALUES (
                :id, :user_id, :title, :description, :video_url, :thumbnail_url,
                :category, :views, :created_at, :updated_at
            )
        ");
        
        $stmt->execute([
            'id' => $uuid,
            'user_id' => $user_id,
            'title' => $title,
            'description' => 'Video: ' . $title,
            'video_url' => $base_video_url . $video_file,
            'thumbnail_url' => $thumbnail_url,
            'category' => 'new',
            'views' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        $inserted_count++;
        
        if ($inserted_count % 50 == 0) {
            echo "   ✅ Inserted $inserted_count records...\n";
        }
    }
    
    echo "\n📊 Comprehensive database rebuild complete!\n";
    echo "   ✅ Total inserted records: $inserted_count\n";
    echo "   📹 Files with existing UUIDs: $uuid_files\n";
    echo "   🆔 Files with generated UUIDs: $generated_uuid_files\n";
    
    // Verify results
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   📹 Total videos in database: " . $result['count'] . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE thumbnail_url IS NOT NULL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   🖼️  Videos with thumbnails: " . $result['count'] . "\n";
    
    // Show sample records
    echo "\n📋 Sample records:\n";
    $stmt = $pdo->query("SELECT id, title, video_url FROM videos ORDER BY created_at DESC LIMIT 5");
    $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($samples as $i => $sample) {
        echo "   " . ($i+1) . ". " . $sample['title'] . "\n";
        echo "      File: " . basename($sample['video_url']) . "\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
}
PHPEOF

echo "🚀 Running comprehensive rebuild for ALL files..."
php /tmp/rebuild_all_files.php

echo "🗑️  Cleaning up..."
rm -f /tmp/all_videos.txt /tmp/all_thumbnails.txt /tmp/rebuild_all_files.php

echo "✅ Comprehensive rebuild completed!"
EOF

print_success "🎉 Comprehensive database rebuild completed!"

# Test the results
print_status "🧪 Testing comprehensive rebuild results..."
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "mysql -u bluerpcm_dbuser -pkingpatrick100 bluerpcm_bluefilmx -e 'SELECT COUNT(*) as total_videos FROM videos;'"

print_success "✅ Comprehensive rebuild process completed!"
print_status "🌐 Your website should now display ALL uploaded videos correctly!"
print_status "🔗 Test your website: https://www.bluefilmx.com"
