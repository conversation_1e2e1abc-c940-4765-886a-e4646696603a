#!/bin/bash

# Fix Video URLs Script
# Updates database URLs to match actual filenames on server

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"

print_status "🔧 Starting video URL fix process..."

# Create a script to run on the server
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST << 'EOF'
#!/bin/bash

echo "🔍 Analyzing video files and database URLs..."

# Get list of actual video files
echo "📁 Getting actual video files on server..."
find public_html/media/videos -name "*.mp4" -o -name "*.MP4" | sed 's|public_html/media/videos/||' > /tmp/actual_videos.txt
video_count=$(wc -l < /tmp/actual_videos.txt)
echo "   Found $video_count video files"

# Get list of actual thumbnail files  
echo "🖼️  Getting actual thumbnail files on server..."
find public_html/media/thumbnails -name "*.jpg" -o -name "*.png" | sed 's|public_html/media/thumbnails/||' > /tmp/actual_thumbnails.txt
thumbnail_count=$(wc -l < /tmp/actual_thumbnails.txt)
echo "   Found $thumbnail_count thumbnail files"

echo "📊 Creating URL mapping script..."

# Create a PHP script to fix URLs
cat > /tmp/fix_urls.php << 'PHPEOF'
<?php
$host = 'localhost';
$dbname = 'bluerpcm_bluefilmx';
$username = 'bluerpcm_dbuser';
$password = 'kingpatrick100';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔗 Connected to database\n";
    
    // Get actual files
    $actual_videos = file('/tmp/actual_videos.txt', FILE_IGNORE_NEW_LINES);
    $actual_thumbnails = file('/tmp/actual_thumbnails.txt', FILE_IGNORE_NEW_LINES);
    
    echo "📹 Processing " . count($actual_videos) . " video files\n";
    echo "🖼️  Processing " . count($actual_thumbnails) . " thumbnail files\n";
    
    // Create lookup arrays by UUID
    $video_lookup = [];
    $thumbnail_lookup = [];
    
    foreach ($actual_videos as $video) {
        if (preg_match('/^([a-f0-9-]{36})_/', $video, $matches)) {
            $uuid = $matches[1];
            $video_lookup[$uuid] = $video;
        }
    }
    
    foreach ($actual_thumbnails as $thumbnail) {
        if (preg_match('/^([a-f0-9-]{36})_/', $thumbnail, $matches)) {
            $uuid = $matches[1];
            $thumbnail_lookup[$uuid] = $thumbnail;
        }
    }
    
    echo "🔍 Built lookup tables\n";
    
    // Get all videos from database
    $stmt = $pdo->query("SELECT id, video_url, thumbnail_url FROM videos");
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $updated_count = 0;
    $base_video_url = 'https://www.bluefilmx.com/media/videos/';
    $base_thumbnail_url = 'https://www.bluefilmx.com/media/thumbnails/';
    
    foreach ($videos as $video) {
        $id = $video['id'];
        $new_video_url = null;
        $new_thumbnail_url = null;
        
        // Find matching video file
        if (isset($video_lookup[$id])) {
            $new_video_url = $base_video_url . $video_lookup[$id];
        }
        
        // Find matching thumbnail file
        if (isset($thumbnail_lookup[$id])) {
            $new_thumbnail_url = $base_thumbnail_url . $thumbnail_lookup[$id];
        }
        
        // Update if we found matches
        if ($new_video_url || $new_thumbnail_url) {
            $update_parts = [];
            $params = ['id' => $id];
            
            if ($new_video_url) {
                $update_parts[] = "video_url = :video_url";
                $params['video_url'] = $new_video_url;
            }
            
            if ($new_thumbnail_url) {
                $update_parts[] = "thumbnail_url = :thumbnail_url";
                $params['thumbnail_url'] = $new_thumbnail_url;
            }
            
            if (!empty($update_parts)) {
                $sql = "UPDATE videos SET " . implode(', ', $update_parts) . " WHERE id = :id";
                $update_stmt = $pdo->prepare($sql);
                $update_stmt->execute($params);
                $updated_count++;
                
                if ($updated_count % 50 == 0) {
                    echo "   Updated $updated_count videos...\n";
                }
            }
        }
    }
    
    echo "✅ Updated $updated_count video records\n";
    
    // Verify some updates
    echo "🔍 Verifying updates...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE video_url LIKE 'https://www.bluefilmx.com/media/videos/%'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Videos with correct URLs: " . $result['count'] . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE thumbnail_url LIKE 'https://www.bluefilmx.com/media/thumbnails/%'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Thumbnails with correct URLs: " . $result['count'] . "\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
}
PHPEOF

echo "🚀 Running URL fix script..."
php /tmp/fix_urls.php

echo "🗑️  Cleaning up temporary files..."
rm -f /tmp/actual_videos.txt /tmp/actual_thumbnails.txt /tmp/fix_urls.php

echo "✅ URL fix process completed!"
EOF

print_success "🎉 Video URL fix completed!"
print_status "🔗 Testing updated URLs..."

# Test a few URLs
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "mysql -u bluerpcm_dbuser -pkingpatrick100 bluerpcm_bluefilmx -e 'SELECT id, LEFT(video_url, 80) as video_url_preview FROM videos LIMIT 3;'"

print_success "✅ URL fix process completed successfully!"
print_status "🌐 Your website should now display videos and thumbnails correctly!"
print_status "🔗 Test your website: https://www.bluefilmx.com"
