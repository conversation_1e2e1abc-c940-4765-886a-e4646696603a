#!/usr/bin/env node

/**
 * Export all data from Supabase to prepare for migration to MySQL
 */

import { createClient } from '@supabase/supabase-js';
import { writeFileSync, mkdirSync, readFileSync } from 'fs';
import { resolve } from 'path';

// Load environment variables from .env file
const envPath = resolve(process.cwd(), '.env');
try {
  const envContent = readFileSync(envPath, 'utf8');
  const envVars = {};
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      envVars[key.trim()] = value.trim();
    }
  });
  Object.assign(process.env, envVars);
} catch (error) {
  console.error('Could not load .env file:', error.message);
}

// Initialize Supabase client
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

const exportDir = resolve(process.cwd(), 'migration-export');

// Ensure export directory exists
try {
  mkdirSync(exportDir, { recursive: true });
} catch (error) {
  // Directory already exists
}

const tables = [
  'profiles',
  'videos', 
  'categories',
  'collections',
  'collection_videos',
  'comments',
  'comment_replies',
  'video_tags'
];

async function exportTable(tableName) {
  console.log(`📊 Exporting ${tableName}...`);
  
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*');
    
    if (error) {
      console.error(`❌ Error exporting ${tableName}:`, error.message);
      return { tableName, success: false, error: error.message, count: 0 };
    }
    
    // Write to JSON file
    const filePath = resolve(exportDir, `${tableName}.json`);
    writeFileSync(filePath, JSON.stringify(data, null, 2));
    
    console.log(`✅ ${tableName}: ${data.length} records exported`);
    return { tableName, success: true, count: data.length };
    
  } catch (err) {
    console.error(`❌ Exception exporting ${tableName}:`, err.message);
    return { tableName, success: false, error: err.message, count: 0 };
  }
}

async function exportAllData() {
  console.log('🚀 Starting Supabase data export...\n');
  
  const results = [];
  
  for (const table of tables) {
    const result = await exportTable(table);
    results.push(result);
  }
  
  // Create summary
  const summary = {
    exportDate: new Date().toISOString(),
    totalTables: tables.length,
    successfulExports: results.filter(r => r.success).length,
    failedExports: results.filter(r => !r.success).length,
    totalRecords: results.reduce((sum, r) => sum + r.count, 0),
    results
  };
  
  // Write summary
  const summaryPath = resolve(exportDir, 'export-summary.json');
  writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
  
  console.log('\n📋 Export Summary:');
  console.log(`✅ Successful: ${summary.successfulExports}/${summary.totalTables} tables`);
  console.log(`📊 Total records: ${summary.totalRecords}`);
  console.log(`📁 Export directory: ${exportDir}`);
  
  if (summary.failedExports > 0) {
    console.log('\n❌ Failed exports:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  - ${r.tableName}: ${r.error}`);
    });
  }
  
  return summary;
}

// Run export
exportAllData().catch(console.error);
