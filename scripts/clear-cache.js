#!/usr/bin/env node

/**
 * Script to clear browser cache and service workers
 * Run this if you're experiencing cache-related issues
 */

console.log('🧹 Cache Clearing Instructions');
console.log('==============================');
console.log('');
console.log('To clear cache and service workers manually:');
console.log('');
console.log('1. Open Chrome DevTools (F12)');
console.log('2. Go to Application tab');
console.log('3. In the left sidebar:');
console.log('   - Click "Service Workers" and unregister all');
console.log('   - Click "Storage" and click "Clear site data"');
console.log('');
console.log('Or run this in the browser console:');
console.log('');
console.log(`
// Clear service workers
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(registrations => {
    registrations.forEach(registration => registration.unregister());
  });
}

// Clear all caches
if ('caches' in window) {
  caches.keys().then(cacheNames => {
    cacheNames.forEach(cacheName => caches.delete(cacheName));
  });
}

// Clear localStorage and sessionStorage
localStorage.clear();
sessionStorage.clear();

// Reload the page
window.location.reload();
`);
console.log('');
console.log('🔄 After clearing, refresh the page to see changes.');
