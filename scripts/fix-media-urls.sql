-- Fix Media URLs in Database
-- This script updates Supabase URLs to point to Namecheap storage

-- First, let's see what URLs we currently have
SELECT 
    'Current URLs Sample' as info,
    id,
    title,
    LEFT(video_url, 50) as video_url_start,
    LEFT(thumbnail_url, 50) as thumbnail_url_start
FROM videos 
LIMIT 5;

-- Update video URLs from Supabase to Namecheap storage
UPDATE videos 
SET video_url = REPLACE(
    video_url, 
    'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/', 
    'https://www.bluefilmx.com/media/videos/'
)
WHERE video_url LIKE 'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/%';

-- Update thumbnail URLs from Supabase to Namecheap storage  
UPDATE videos 
SET thumbnail_url = REPLACE(
    thumbnail_url, 
    'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/', 
    'https://www.bluefilmx.com/media/thumbnails/'
)
WHERE thumbnail_url LIKE 'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/%';

-- Also update any collection thumbnail URLs
UPDATE collections 
SET thumbnail_url = REPLACE(
    thumbnail_url, 
    'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/', 
    'https://www.bluefilmx.com/media/thumbnails/'
)
WHERE thumbnail_url LIKE 'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/%';

-- Update profile avatar URLs if any
UPDATE profiles 
SET avatar_url = REPLACE(
    avatar_url, 
    'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/avatars/', 
    'https://www.bluefilmx.com/media/avatars/'
)
WHERE avatar_url LIKE 'https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/avatars/%';

-- Show updated URLs sample
SELECT 
    'Updated URLs Sample' as info,
    id,
    title,
    LEFT(video_url, 50) as video_url_start,
    LEFT(thumbnail_url, 50) as thumbnail_url_start
FROM videos 
LIMIT 5;

-- Show count of updated records
SELECT 
    'Update Summary' as info,
    COUNT(*) as total_videos,
    SUM(CASE WHEN video_url LIKE 'https://www.bluefilmx.com/media/videos/%' THEN 1 ELSE 0 END) as videos_with_namecheap_urls,
    SUM(CASE WHEN thumbnail_url LIKE 'https://www.bluefilmx.com/media/thumbnails/%' THEN 1 ELSE 0 END) as thumbnails_with_namecheap_urls
FROM videos;
