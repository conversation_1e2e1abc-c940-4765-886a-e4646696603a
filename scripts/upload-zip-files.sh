#!/bin/bash

# Direct ZIP Upload Script: Upload video and thumbnail ZIP files to Namecheap
# This is much faster than downloading from Supabase!

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"

print_status "🚀 Starting DIRECT ZIP upload to Namecheap server!"
print_status "📦 This will upload all videos and thumbnails at once"

# Check if ZIP files exist
if [ ! -f "video.zip" ]; then
    print_error "❌ video.zip not found in current directory"
    exit 1
fi

if [ ! -f "thumbnail.zip" ]; then
    print_error "❌ thumbnail.zip not found in current directory"
    exit 1
fi

# Get ZIP file sizes
video_size=$(du -h video.zip | cut -f1)
thumbnail_size=$(du -h thumbnail.zip | cut -f1)

print_status "📊 ZIP file sizes:"
print_status "   📹 video.zip: $video_size"
print_status "   🖼️  thumbnail.zip: $thumbnail_size"

# Check current videos on server
print_status "📋 Checking current videos on server..."
current_videos=$(ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "find public_html/media/videos -name '*.mp4' -o -name '*.MP4' | wc -l" 2>/dev/null || echo "0")
current_thumbnails=$(ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "find public_html/media/thumbnails -name '*.jpg' -o -name '*.png' | wc -l" 2>/dev/null || echo "0")

print_status "📁 Current server content:"
print_status "   📹 Videos: $current_videos"
print_status "   🖼️  Thumbnails: $current_thumbnails"

# Upload video.zip
print_status "📤 Uploading video.zip ($video_size)..."
print_status "   This may take several minutes depending on file size..."

if scp -P $SSH_PORT video.zip $SSH_USER@$SSH_HOST:; then
    print_success "✅ video.zip uploaded successfully!"
else
    print_error "❌ Failed to upload video.zip"
    exit 1
fi

# Upload thumbnail.zip
print_status "📤 Uploading thumbnail.zip ($thumbnail_size)..."

if scp -P $SSH_PORT thumbnail.zip $SSH_USER@$SSH_HOST:; then
    print_success "✅ thumbnail.zip uploaded successfully!"
else
    print_error "❌ Failed to upload thumbnail.zip"
    exit 1
fi

# Extract files on server
print_status "📂 Extracting files on server..."

ssh -p $SSH_PORT $SSH_USER@$SSH_HOST << 'EOF'
    echo "🔧 Setting up directories..."
    mkdir -p public_html/media/videos
    mkdir -p public_html/media/thumbnails
    
    echo "📹 Extracting videos..."
    if [ -f video.zip ]; then
        unzip -o video.zip -d public_html/media/videos/
        echo "✅ Videos extracted"
        rm video.zip
        echo "🗑️  video.zip cleaned up"
    else
        echo "❌ video.zip not found"
    fi
    
    echo "🖼️  Extracting thumbnails..."
    if [ -f thumbnail.zip ]; then
        unzip -o thumbnail.zip -d public_html/media/thumbnails/
        echo "✅ Thumbnails extracted"
        rm thumbnail.zip
        echo "🗑️  thumbnail.zip cleaned up"
    else
        echo "❌ thumbnail.zip not found"
    fi
    
    echo "🔧 Setting permissions..."
    chmod 644 public_html/media/videos/*
    chmod 644 public_html/media/thumbnails/*
    
    echo "📊 Final counts:"
    video_count=$(find public_html/media/videos -name '*.mp4' -o -name '*.MP4' | wc -l)
    thumbnail_count=$(find public_html/media/thumbnails -name '*.jpg' -o -name '*.png' | wc -l)
    echo "   📹 Videos: $video_count"
    echo "   🖼️  Thumbnails: $thumbnail_count"
EOF

# Get final counts
print_status "🔍 Verifying final counts..."
final_videos=$(ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "find public_html/media/videos -name '*.mp4' -o -name '*.MP4' | wc -l" 2>/dev/null || echo "0")
final_thumbnails=$(ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "find public_html/media/thumbnails -name '*.jpg' -o -name '*.png' | wc -l" 2>/dev/null || echo "0")

print_success "🎉 ZIP upload and extraction complete!"
echo ""
print_status "📊 Final Results:"
print_success "   📹 Videos on server: $final_videos"
print_success "   🖼️  Thumbnails on server: $final_thumbnails"

if [ "$final_videos" -gt 200 ]; then
    print_success "🎉 Excellent! Most/all videos have been uploaded!"
    print_status "🌐 Your website should now display all videos and thumbnails."
    print_status "🔗 Test your website: https://www.bluefilmx.com"
else
    print_warning "⚠️  Expected more videos. Check if ZIP files contain all content."
fi

print_success "✅ Direct ZIP upload completed successfully!"
print_status "💡 This method is much faster than downloading from Supabase!"
