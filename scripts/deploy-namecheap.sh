#!/bin/bash

# Namecheap Deployment Script
# This script prepares your React/Vite project for deployment to Namecheap hosting

set -e  # Exit on any error

echo "🚀 Starting Namecheap deployment preparation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from your project root directory."
    exit 1
fi

if [ ! -f "vite.config.ts" ]; then
    print_error "vite.config.ts not found. This script is for Vite projects."
    exit 1
fi

print_status "Project validation passed ✓"

# Clean previous builds
print_status "Cleaning previous builds..."
if [ -d "dist" ]; then
    rm -rf dist
    print_status "Removed existing dist/ directory"
fi

if [ -d "namecheap-deployment" ]; then
    rm -rf namecheap-deployment
    print_status "Removed existing deployment directory"
fi

# Install dependencies
print_status "Installing dependencies..."
npm install

# Build the project
print_status "Building project for production..."
npm run build

# Check if build was successful
if [ ! -d "dist" ]; then
    print_error "Build failed - dist/ directory not created"
    exit 1
fi

if [ ! -f "dist/index.html" ]; then
    print_error "Build failed - index.html not found in dist/"
    exit 1
fi

print_success "Build completed successfully"

# Create deployment directory
print_status "Preparing deployment package..."
mkdir -p namecheap-deployment

# Copy all files from dist to deployment directory
cp -r dist/* namecheap-deployment/

# Create .htaccess file
print_status "Creating .htaccess file..."
cat > namecheap-deployment/.htaccess << 'EOF'
RewriteEngine On
RewriteBase /
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-l
RewriteRule . /index.html [L]

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>
EOF

print_success ".htaccess file created"

# Create deployment ZIP file
print_status "Creating deployment ZIP file..."
cd namecheap-deployment
zip -r ../namecheap-deployment.zip . > /dev/null 2>&1
cd ..

print_success "Deployment package created: namecheap-deployment.zip"

# Display file information
print_status "Deployment package contents:"
echo "📁 Files to upload:"
ls -la namecheap-deployment/

# Calculate package size
PACKAGE_SIZE=$(du -h namecheap-deployment.zip | cut -f1)
print_status "Package size: $PACKAGE_SIZE"

# Display next steps
echo ""
print_success "🎉 Deployment preparation complete!"
echo ""
echo "📋 Next steps:"
echo "1. Log into your Namecheap cPanel"
echo "2. Open File Manager"
echo "3. Navigate to your domain's root directory (usually public_html/)"
echo "4. Upload the file: namecheap-deployment.zip"
echo "5. Extract the ZIP file in the root directory"
echo "6. Delete the ZIP file after extraction"
echo "7. Test your website"
echo ""
print_warning "Important: Make sure to extract the ZIP contents directly into your domain's root directory, not into a subfolder."
echo ""
print_status "For detailed instructions, see: NAMECHEAP_DEPLOYMENT_GUIDE.md"

# Check for environment variables
print_status "Checking environment configuration..."
if [ -f ".env" ]; then
    print_warning "Found .env file. Make sure your production environment variables are correct."
fi

if [ -f ".env.production" ]; then
    print_success "Found .env.production file - production environment variables will be used."
fi

# Display Supabase configuration
print_status "Supabase configuration check:"
if grep -q "VITE_SUPABASE_URL" .env* 2>/dev/null; then
    print_success "Supabase URL configured"
else
    print_warning "Supabase URL not found in environment files"
fi

if grep -q "VITE_SUPABASE_ANON_KEY" .env* 2>/dev/null; then
    print_success "Supabase anon key configured"
else
    print_warning "Supabase anon key not found in environment files"
fi

echo ""
print_success "✅ Ready for deployment to Namecheap!"
