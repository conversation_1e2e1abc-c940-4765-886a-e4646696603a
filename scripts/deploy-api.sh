#!/bin/bash

# Deploy API to Namecheap hosting (Manual Upload Method)
# This script creates a ZIP package for manual upload via cPanel

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

LOCAL_API_PATH="namecheap-api"

print_status "🚀 Creating API deployment package for manual upload..."

# Check if API directory exists
if [ ! -d "$LOCAL_API_PATH" ]; then
    print_error "API directory not found: $LOCAL_API_PATH"
    exit 1
fi

# Clean previous deployment package
if [ -f "api-deployment.zip" ]; then
    rm api-deployment.zip
    print_status "Removed existing api-deployment.zip"
fi

# Create API deployment package
print_status "📦 Creating API deployment ZIP package..."
cd "$LOCAL_API_PATH"
zip -r ../api-deployment.zip . > /dev/null 2>&1
cd ..

print_success "API package created: api-deployment.zip"

# Display package contents
print_status "📁 Package contents:"
echo "Files to upload:"
unzip -l api-deployment.zip

print_success "🎉 API deployment package ready!"
echo ""
print_status "📋 Manual Upload Instructions:"
echo "1. Log into your Namecheap cPanel"
echo "2. Open File Manager"
echo "3. Navigate to public_html/"
echo "4. Create a new folder called 'api' (if it doesn't exist)"
echo "5. Enter the 'api' folder"
echo "6. Upload api-deployment.zip"
echo "7. Extract the ZIP file in the api directory"
echo "8. Delete the ZIP file after extraction"
echo ""
print_warning "⚠️  Next steps after upload:"
echo "1. Create MySQL database in cPanel"
echo "2. Update database credentials in api/config/database.php"
echo "3. Import your video data to the database"
echo "4. Test API endpoints"
