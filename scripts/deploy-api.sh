#!/bin/bash

# Deploy API to Namecheap hosting via SSH
# This script uploads the PHP API files directly to your hosting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"
REMOTE_PATH="public_html/api"
LOCAL_API_PATH="namecheap-api"

print_status "🚀 Starting API deployment to Namecheap via SSH..."

# Check if API directory exists
if [ ! -d "$LOCAL_API_PATH" ]; then
    print_error "API directory not found: $LOCAL_API_PATH"
    exit 1
fi

# Create API deployment package
print_status "📦 Creating API deployment package..."
cd "$LOCAL_API_PATH"
tar -czf ../api-deployment.tar.gz .
cd ..

print_success "API package created: api-deployment.tar.gz"

# Upload to server
print_status "📤 Uploading API to server..."
scp -P $SSH_PORT api-deployment.tar.gz $SSH_USER@$SSH_HOST:

# Extract on server
print_status "📂 Extracting API on server..."
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST << 'EOF'
    # Create API directory
    mkdir -p public_html/api

    # Extract API files
    cd public_html/api
    tar -xzf ../../api-deployment.tar.gz

    # Set proper permissions
    chmod 755 *.php
    chmod 755 config/
    chmod 644 config/*.php
    chmod 644 .htaccess

    # Clean up
    rm ../../api-deployment.tar.gz

    echo "✅ API extracted and permissions set"
EOF

# Clean up local package
rm api-deployment.tar.gz

print_success "🎉 API deployment complete!"
print_status "📋 Next steps:"
echo "1. Create MySQL database in cPanel"
echo "2. Update database credentials in api/config/database.php"
echo "3. Run the MySQL schema and data import scripts"
echo "4. Test API endpoints"

print_warning "⚠️  Remember to update the database configuration in:"
echo "   https://www.bluefilmx.com/api/config/database.php"
