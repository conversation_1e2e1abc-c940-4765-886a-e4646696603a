#!/bin/bash

# Fix URL Encoding Issues
# Properly maps database URLs to actual filenames on server

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"

print_status "🔧 Starting comprehensive URL encoding fix..."

# Create a comprehensive fix script
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST << 'EOF'
#!/bin/bash

echo "🔍 Analyzing file mismatch issues..."

# Get all actual files
echo "📁 Getting all actual files on server..."
find public_html/media/videos -name "*.mp4" -o -name "*.MP4" | sed 's|public_html/media/videos/||' | sort > /tmp/actual_videos.txt
find public_html/media/thumbnails -name "*.jpg" -o -name "*.png" | sed 's|public_html/media/thumbnails/||' | sort > /tmp/actual_thumbnails.txt

video_count=$(wc -l < /tmp/actual_videos.txt)
thumbnail_count=$(wc -l < /tmp/actual_thumbnails.txt)

echo "   📹 Found $video_count video files"
echo "   🖼️  Found $thumbnail_count thumbnail files"

# Create a comprehensive PHP script to fix all URL issues
cat > /tmp/comprehensive_fix.php << 'PHPEOF'
<?php
$host = 'localhost';
$dbname = 'bluerpcm_bluefilmx';
$username = 'bluerpcm_dbuser';
$password = 'kingpatrick100';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔗 Connected to database\n";
    
    // Get actual files
    $actual_videos = file('/tmp/actual_videos.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $actual_thumbnails = file('/tmp/actual_thumbnails.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    echo "📹 Processing " . count($actual_videos) . " video files\n";
    echo "🖼️  Processing " . count($actual_thumbnails) . " thumbnail files\n";
    
    // Create lookup arrays by UUID (more flexible matching)
    $video_lookup = [];
    $thumbnail_lookup = [];
    
    // Build video lookup
    foreach ($actual_videos as $video) {
        if (preg_match('/^([a-f0-9-]{36})_(.+)$/', $video, $matches)) {
            $uuid = $matches[1];
            $video_lookup[$uuid] = $video;
        }
    }
    
    // Build thumbnail lookup
    foreach ($actual_thumbnails as $thumbnail) {
        if (preg_match('/^([a-f0-9-]{36})_(.+)$/', $thumbnail, $matches)) {
            $uuid = $matches[1];
            $thumbnail_lookup[$uuid] = $thumbnail;
        }
    }
    
    echo "🔍 Built lookup tables:\n";
    echo "   📹 Video lookup: " . count($video_lookup) . " entries\n";
    echo "   🖼️  Thumbnail lookup: " . count($thumbnail_lookup) . " entries\n";
    
    // Get all videos from database
    $stmt = $pdo->query("SELECT id, title, video_url, thumbnail_url FROM videos");
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $updated_count = 0;
    $video_matches = 0;
    $thumbnail_matches = 0;
    $base_video_url = 'https://www.bluefilmx.com/media/videos/';
    $base_thumbnail_url = 'https://www.bluefilmx.com/media/thumbnails/';
    
    echo "🔄 Processing " . count($videos) . " database records...\n";
    
    foreach ($videos as $video) {
        $id = $video['id'];
        $new_video_url = null;
        $new_thumbnail_url = null;
        $needs_update = false;
        
        // Check if video file exists
        if (isset($video_lookup[$id])) {
            $new_video_url = $base_video_url . $video_lookup[$id];
            $video_matches++;
            
            // Check if current URL is different
            if ($video['video_url'] !== $new_video_url) {
                $needs_update = true;
            }
        } else {
            echo "⚠️  No video file found for UUID: $id\n";
        }
        
        // Check if thumbnail file exists
        if (isset($thumbnail_lookup[$id])) {
            $new_thumbnail_url = $base_thumbnail_url . $thumbnail_lookup[$id];
            $thumbnail_matches++;
            
            // Check if current URL is different
            if ($video['thumbnail_url'] !== $new_thumbnail_url) {
                $needs_update = true;
            }
        } else {
            echo "⚠️  No thumbnail file found for UUID: $id\n";
        }
        
        // Update if needed
        if ($needs_update) {
            $update_parts = [];
            $params = ['id' => $id];
            
            if ($new_video_url) {
                $update_parts[] = "video_url = :video_url";
                $params['video_url'] = $new_video_url;
            }
            
            if ($new_thumbnail_url) {
                $update_parts[] = "thumbnail_url = :thumbnail_url";
                $params['thumbnail_url'] = $new_thumbnail_url;
            }
            
            if (!empty($update_parts)) {
                $sql = "UPDATE videos SET " . implode(', ', $update_parts) . " WHERE id = :id";
                $update_stmt = $pdo->prepare($sql);
                $update_stmt->execute($params);
                $updated_count++;
                
                if ($updated_count % 25 == 0) {
                    echo "   ✅ Updated $updated_count records...\n";
                }
            }
        }
    }
    
    echo "\n📊 Final Results:\n";
    echo "   ✅ Updated records: $updated_count\n";
    echo "   📹 Video matches: $video_matches\n";
    echo "   🖼️  Thumbnail matches: $thumbnail_matches\n";
    
    // Verify final state
    echo "\n🔍 Verifying final state...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE video_url LIKE 'https://www.bluefilmx.com/media/videos/%'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Videos with correct base URL: " . $result['count'] . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE thumbnail_url LIKE 'https://www.bluefilmx.com/media/thumbnails/%'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Thumbnails with correct base URL: " . $result['count'] . "\n";
    
    // Show some sample URLs
    echo "\n📋 Sample updated URLs:\n";
    $stmt = $pdo->query("SELECT id, video_url, thumbnail_url FROM videos LIMIT 3");
    $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($samples as $i => $sample) {
        echo "   " . ($i+1) . ". Video: " . substr($sample['video_url'], 45) . "\n";
        echo "      Thumbnail: " . substr($sample['thumbnail_url'], 50) . "\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
}
PHPEOF

echo "🚀 Running comprehensive URL fix..."
php /tmp/comprehensive_fix.php

echo "🗑️  Cleaning up..."
rm -f /tmp/actual_videos.txt /tmp/actual_thumbnails.txt /tmp/comprehensive_fix.php

echo "✅ Comprehensive URL fix completed!"
EOF

print_success "🎉 Comprehensive URL fix completed!"

# Test the API after the fix
print_status "🧪 Testing API after fix..."
api_test=$(curl -s "https://www.bluefilmx.com/api/videos.php?limit=1" | head -c 100)
if [[ "$api_test" == *"videos"* ]]; then
    print_success "✅ API is responding correctly"
else
    print_warning "⚠️  API may still have issues"
fi

# Test a specific video URL
print_status "🧪 Testing video accessibility..."
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "mysql -u bluerpcm_dbuser -pkingpatrick100 bluerpcm_bluefilmx -e 'SELECT video_url FROM videos LIMIT 1;' | tail -1" > /tmp/test_url.txt
test_url=$(cat /tmp/test_url.txt)
if curl -I "$test_url" 2>/dev/null | grep -q "200"; then
    print_success "✅ Video files are accessible"
else
    print_warning "⚠️  Some video files may not be accessible"
fi
rm -f /tmp/test_url.txt

print_success "✅ URL encoding fix process completed!"
print_status "🌐 Your website should now display videos and thumbnails correctly!"
print_status "🔗 Test your website: https://www.bluefilmx.com"
