#!/bin/bash

# Targeted Media Migration Script: Supabase → Namecheap
# Downloads specific media files from Supabase and uploads to Namecheap server

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"
TEMP_DIR="./temp-media"

print_status "🚀 Starting targeted media migration from Supabase to Namecheap..."

# Create temporary directory
mkdir -p "$TEMP_DIR/videos" "$TEMP_DIR/thumbnails"

# Test with specific known files from the import data
print_status "📋 Testing with known Supabase URLs..."

# Test files based on the import data
declare -a test_files=(
    # Format: "type|supabase_url|namecheap_filename"
    "video|https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1749178321571-battalion_of_bad_bitches_0_.mp4|43561134-bb23-429e-9985-70920c609af4_1749178321571-battalion_of_bad_bitches_0_.mp4"
    "thumbnail|https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/c2157ddd-2f88-436a-8ae7-f2b828b30145/1749178343045-20250606_005527-collage.jpg|43561134-bb23-429e-9985-70920c609af4_1749178343045-20250606_005527-collage.jpg"
    "video|https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1749177901170-slim_ebony_bends_to_show_her_goodies_gosh_nothing_more_thrilling.mp4|fd851a77-fb3e-4413-ab07-22395eca827d_1749177901170-slim_ebony_bends_to_show_her_goodies_gosh_nothing_more_thrilling.mp4"
    "thumbnail|https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/c2157ddd-2f88-436a-8ae7-f2b828b30145/1749177910775-20250606_005451-collage.jpg|fd851a77-fb3e-4413-ab07-22395eca827d_1749177910775-20250606_005451-collage.jpg"
    "video|https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1749177694263-anna_says_take_a_good_look_.mp4|19243b7d-1f4b-4ad1-9407-fc2d2a03600d_1749177694263-anna_says_take_a_good_look_.mp4"
)

success_count=0
error_count=0

for file_info in "${test_files[@]}"; do
    IFS='|' read -r type supabase_url namecheap_filename <<< "$file_info"
    
    print_status "Processing $type: $namecheap_filename"
    
    local_path="$TEMP_DIR/${type}s/$namecheap_filename"
    remote_path="public_html/media/${type}s/$namecheap_filename"
    
    # Download from Supabase
    print_status "  Downloading from: $supabase_url"
    if curl -s -f "$supabase_url" -o "$local_path"; then
        print_success "  ✅ Downloaded successfully"
        
        # Upload to Namecheap server
        print_status "  Uploading to server..."
        if scp -P $SSH_PORT "$local_path" "$SSH_USER@$SSH_HOST:$remote_path"; then
            print_success "  ✅ Uploaded successfully"
            ((success_count++))
        else
            print_error "  ❌ Upload failed"
            ((error_count++))
        fi
        
        # Clean up local file
        rm -f "$local_path"
    else
        print_error "  ❌ Download failed"
        ((error_count++))
    fi
    
    echo ""
done

# Test a few more files by getting them from the database
print_status "📡 Getting more files from database..."

# Get some video URLs from the database and try to migrate them
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "mysql -u bluerpcm_dbuser -pkingpatrick100 bluerpcm_bluefilmx -e \"
SELECT 
    CONCAT(id, '|', video_url, '|', thumbnail_url) as file_info
FROM videos 
WHERE video_url LIKE 'https://www.bluefilmx.com/media/%'
LIMIT 5;
\"" | tail -n +2 | while IFS='|' read -r video_id video_url thumbnail_url; do
    
    if [[ -n "$video_id" && -n "$video_url" && -n "$thumbnail_url" ]]; then
        print_status "Processing video: $video_id"
        
        # Extract filenames
        video_filename=$(basename "$video_url")
        thumbnail_filename=$(basename "$thumbnail_url")
        
        # Convert to Supabase URLs
        # Extract the part after the UUID_
        video_supabase_path=$(echo "$video_filename" | sed 's/^[^_]*_//')
        thumbnail_supabase_path=$(echo "$thumbnail_filename" | sed 's/^[^_]*_//')
        
        # Use the main user ID
        user_id="c2157ddd-2f88-436a-8ae7-f2b828b30145"
        
        video_supabase_url="https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/$user_id/$video_supabase_path"
        thumbnail_supabase_url="https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/$user_id/$thumbnail_supabase_path"
        
        # Try video
        print_status "  Trying video: $video_supabase_url"
        if curl -s -f "$video_supabase_url" -o "$TEMP_DIR/videos/$video_filename"; then
            print_success "  ✅ Video downloaded"
            if scp -P $SSH_PORT "$TEMP_DIR/videos/$video_filename" "$SSH_USER@$SSH_HOST:public_html/media/videos/$video_filename"; then
                print_success "  ✅ Video uploaded"
                ((success_count++))
            else
                print_error "  ❌ Video upload failed"
                ((error_count++))
            fi
            rm -f "$TEMP_DIR/videos/$video_filename"
        else
            print_error "  ❌ Video download failed"
            ((error_count++))
        fi
        
        # Try thumbnail
        print_status "  Trying thumbnail: $thumbnail_supabase_url"
        if curl -s -f "$thumbnail_supabase_url" -o "$TEMP_DIR/thumbnails/$thumbnail_filename"; then
            print_success "  ✅ Thumbnail downloaded"
            if scp -P $SSH_PORT "$TEMP_DIR/thumbnails/$thumbnail_filename" "$SSH_USER@$SSH_HOST:public_html/media/thumbnails/$thumbnail_filename"; then
                print_success "  ✅ Thumbnail uploaded"
                ((success_count++))
            else
                print_error "  ❌ Thumbnail upload failed"
                ((error_count++))
            fi
            rm -f "$TEMP_DIR/thumbnails/$thumbnail_filename"
        else
            print_error "  ❌ Thumbnail download failed"
            ((error_count++))
        fi
        
        print_status "  Completed video $video_id"
        echo ""
    fi
done

# Clean up
rm -rf "$TEMP_DIR"

print_success "🎉 Migration test complete!"
print_status "✅ Successful transfers: $success_count"
print_status "❌ Failed transfers: $error_count"

if [ $success_count -gt 0 ]; then
    print_success "🔍 Some files migrated! Test your website to see if videos/thumbnails are loading."
    print_status "If working, we can run a full migration of all 248 videos."
else
    print_warning "No files were successfully migrated. Check the logs above for issues."
fi
