#!/bin/bash

# Simple Media Migration Script: Supabase → Namecheap
# Downloads media files from Supabase and uploads to Namecheap server

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SSH_HOST="premium34.web-hosting.com"
SSH_PORT="21098"
SSH_USER="bluerpcm"
TEMP_DIR="./temp-media"
BATCH_SIZE=5

print_status "🚀 Starting media migration from Supabase to Namecheap..."

# Create temporary directory
mkdir -p "$TEMP_DIR/videos" "$TEMP_DIR/thumbnails"

# Get a sample of media URLs from the database to test
print_status "📋 Getting media URLs from database..."

# Get first 10 videos for testing
ssh -p $SSH_PORT $SSH_USER@$SSH_HOST "mysql -u bluerpcm_dbuser -pkingpatrick100 bluerpcm_bluefilmx -e \"
SELECT 
    id,
    video_url,
    thumbnail_url
FROM videos 
WHERE video_url LIKE 'https://www.bluefilmx.com/media/%'
LIMIT 10;
\"" > "$TEMP_DIR/urls.txt"

print_status "📁 Sample URLs retrieved. Processing first batch..."

# Function to download and upload a single file
migrate_file() {
    local url="$1"
    local type="$2"  # 'videos' or 'thumbnails'
    local filename=$(basename "$url")
    local local_path="$TEMP_DIR/$type/$filename"
    local remote_path="public_html/media/$type/$filename"
    
    print_status "  Downloading $filename..."
    
    # Download from current URL (should be Namecheap URL but files don't exist)
    # Let's try to construct the Supabase URL
    local supabase_url
    if [[ "$type" == "videos" ]]; then
        supabase_url="https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/$(echo $filename | cut -d'_' -f2-)"
    else
        supabase_url="https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/$(echo $filename | cut -d'_' -f2-)"
    fi
    
    # Try downloading from Supabase
    if curl -s -f "$supabase_url" -o "$local_path"; then
        print_success "    Downloaded $filename"
        
        # Upload to Namecheap server
        print_status "    Uploading to server..."
        if scp -P $SSH_PORT "$local_path" "$SSH_USER@$SSH_HOST:$remote_path"; then
            print_success "    ✅ $filename migrated successfully"
            rm "$local_path"
            return 0
        else
            print_error "    ❌ Upload failed for $filename"
            rm -f "$local_path"
            return 1
        fi
    else
        print_error "    ❌ Download failed for $filename from $supabase_url"
        return 1
    fi
}

# Test with a few files first
print_status "🧪 Testing migration with sample files..."

# Let's try a direct approach - get some actual URLs from the API
print_status "📡 Getting actual video URLs from API..."
curl -s "https://www.bluefilmx.com/api/videos.php?limit=3" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'data' in data and 'videos' in data['data']:
        for video in data['data']['videos'][:3]:
            print(f\"VIDEO: {video['id']} | {video['video_url']} | {video['thumbnail_url']}\")
except:
    print('Failed to parse JSON')
" > "$TEMP_DIR/sample_urls.txt"

if [ -s "$TEMP_DIR/sample_urls.txt" ]; then
    print_status "📋 Sample URLs found. Starting test migration..."
    
    success_count=0
    error_count=0
    
    while IFS='|' read -r prefix video_id video_url thumbnail_url; do
        if [[ "$prefix" == "VIDEO: "* ]]; then
            video_id=$(echo "$video_id" | xargs)
            video_url=$(echo "$video_url" | xargs)
            thumbnail_url=$(echo "$thumbnail_url" | xargs)
            
            print_status "Processing video: $video_id"
            
            # Extract filenames from URLs
            video_filename=$(basename "$video_url")
            thumbnail_filename=$(basename "$thumbnail_url")
            
            # Try to construct Supabase URLs
            # The current URLs are like: https://www.bluefilmx.com/media/videos/43561134-bb23-429e-9985-70920c609af4_1749178321571-battalion_of_bad_bitches_0_.mp4
            # We need to convert to: https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/c2157ddd-2f88-436a-8ae7-f2b828b30145/1749178321571-battalion_of_bad_bitches_0_.mp4
            
            # Extract the part after the UUID_
            video_supabase_path=$(echo "$video_filename" | sed 's/^[^_]*_//')
            thumbnail_supabase_path=$(echo "$thumbnail_filename" | sed 's/^[^_]*_//')
            
            # Get user_id from the video_id (we'll use a common user_id)
            user_id="c2157ddd-2f88-436a-8ae7-f2b828b30145"  # Most common user from the data
            
            video_supabase_url="https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/videos/$user_id/$video_supabase_path"
            thumbnail_supabase_url="https://vsnsglgyapexhwyfylic.supabase.co/storage/v1/object/public/thumbnails/$user_id/$thumbnail_supabase_path"
            
            print_status "  Trying video: $video_supabase_url"
            print_status "  Trying thumbnail: $thumbnail_supabase_url"
            
            # Test download and upload
            if curl -s -f "$video_supabase_url" -o "$TEMP_DIR/videos/$video_filename"; then
                print_success "  ✅ Video downloaded"
                if scp -P $SSH_PORT "$TEMP_DIR/videos/$video_filename" "$SSH_USER@$SSH_HOST:public_html/media/videos/$video_filename"; then
                    print_success "  ✅ Video uploaded"
                    ((success_count++))
                else
                    print_error "  ❌ Video upload failed"
                    ((error_count++))
                fi
                rm -f "$TEMP_DIR/videos/$video_filename"
            else
                print_error "  ❌ Video download failed"
                ((error_count++))
            fi
            
            if curl -s -f "$thumbnail_supabase_url" -o "$TEMP_DIR/thumbnails/$thumbnail_filename"; then
                print_success "  ✅ Thumbnail downloaded"
                if scp -P $SSH_PORT "$TEMP_DIR/thumbnails/$thumbnail_filename" "$SSH_USER@$SSH_HOST:public_html/media/thumbnails/$thumbnail_filename"; then
                    print_success "  ✅ Thumbnail uploaded"
                    ((success_count++))
                else
                    print_error "  ❌ Thumbnail upload failed"
                    ((error_count++))
                fi
                rm -f "$TEMP_DIR/thumbnails/$thumbnail_filename"
            else
                print_error "  ❌ Thumbnail download failed"
                ((error_count++))
            fi
            
            print_status "  Completed video $video_id"
            echo ""
        fi
    done < "$TEMP_DIR/sample_urls.txt"
    
    print_success "🎉 Test migration complete!"
    print_status "✅ Successful transfers: $success_count"
    print_status "❌ Failed transfers: $error_count"
    
else
    print_error "No sample URLs found. Check API connection."
fi

# Clean up
rm -rf "$TEMP_DIR"

print_status "🔍 Testing migrated files..."
print_status "Visit your website to see if videos and thumbnails are now loading!"
