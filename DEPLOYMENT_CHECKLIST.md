# 🚀 BlueFilmX API Deployment Checklist

## 🎯 Goal
Fix the "404 Error Loading Videos" by deploying your PHP API to Namecheap hosting.

## 📋 Pre-Deployment Checklist
- [x] API deployment package created (`api-deployment.zip`)
- [x] Database schema ready (`scripts/create-mysql-schema.sql`)
- [x] Video data ready (`mysql-import/import-data.sql` - 248 videos)
- [x] Database config prepared (`namecheap-api/config/database.php`)

## 🔧 Deployment Steps

### Step 1: Upload API Files (5 minutes)
- [ ] Log into Namecheap cPanel
- [ ] Open File Manager
- [ ] Navigate to `public_html/`
- [ ] Create `api` folder (if doesn't exist)
- [ ] Upload `api-deployment.zip` to the `api` folder
- [ ] Extract ZIP file in the `api` folder
- [ ] Delete ZIP file after extraction
- [ ] Verify files are present:
  - [ ] `videos.php`
  - [ ] `auth.php`
  - [ ] `categories.php`
  - [ ] `upload.php`
  - [ ] `.htaccess`
  - [ ] `config/database.php`

### Step 2: Create MySQL Database (5 minutes)
- [ ] In cPanel, go to "MySQL Databases"
- [ ] Create database: `bluerpcm_bluefilmx`
- [ ] Create user: `bluerpcm_dbuser`
- [ ] Set password: `kingpatrick100` (or your choice)
- [ ] Add user to database with ALL PRIVILEGES

### Step 3: Import Database (5 minutes)
- [ ] In cPanel, open phpMyAdmin
- [ ] Select database `bluerpcm_bluefilmx`
- [ ] Import `scripts/create-mysql-schema.sql`
- [ ] Import `mysql-import/import-data.sql`
- [ ] Verify tables created:
  - [ ] `videos` (should have 248 records)
  - [ ] `categories` (should have 3 records)
  - [ ] `profiles` (should have 2 records)

### Step 4: Test API (2 minutes)
- [ ] Test videos: `https://www.bluefilmx.com/api/videos.php?limit=5`
- [ ] Test categories: `https://www.bluefilmx.com/api/categories.php`
- [ ] Both should return JSON data (not 404)

### Step 5: Test Website (1 minute)
- [ ] Visit `https://www.bluefilmx.com`
- [ ] Videos should load (no more "Error Loading Videos")
- [ ] Categories should work
- [ ] Search should work

## 🧪 Quick Test Commands

Run this after deployment to verify everything works:
```bash
./scripts/test-api.sh
```

## 🔍 Troubleshooting

### If API returns 404:
- Check files are in `public_html/api/` (not `public_html/api/api/`)
- Verify `.htaccess` file exists in the api folder

### If database connection fails:
- Check credentials in `config/database.php`
- Verify database user has proper privileges
- Test connection in phpMyAdmin

### If no videos show:
- Check data was imported (look in phpMyAdmin)
- Test API endpoints directly in browser
- Check browser console for errors

## ⏱️ Total Time Estimate
**15-20 minutes** for complete deployment

## 🎉 Success Criteria
- ✅ API endpoints return JSON (not 404)
- ✅ Website loads videos without errors
- ✅ All 248 videos are accessible
- ✅ Categories and search work

## 📞 Support
If you need help:
1. Check the detailed guide: `API_DEPLOYMENT_GUIDE.md`
2. Run the test script: `./scripts/test-api.sh`
3. Check browser console for specific errors

---

**Ready to deploy?** Start with Step 1 above! 🚀
