import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { visualizer } from 'rollup-plugin-visualizer';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    visualizer({
      open: false,
      gzipSize: true,
      brotliSize: true,
      filename: 'dist/stats.html',
    }),
  ],
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
    __BUILD_ID__: JSON.stringify(process.env.VERCEL_GIT_COMMIT_SHA?.substring(0, 8) || 'dev'),
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'zustand',
      '@supabase/supabase-js',
      'react-intersection-observer',
      'react-window',
      'react-virtualized-auto-sizer',
      'swr'
    ],
    exclude: ['lucide-react'],
  },
  build: {
    outDir: 'dist',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console logs in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.debug', 'console.info'],
      },
    },
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
      },
      output: {
        manualChunks: (id) => {
          // Core libraries
          if (id.includes('node_modules/react/') ||
              id.includes('node_modules/react-dom/') ||
              id.includes('node_modules/scheduler/')) {
            return 'react-core';
          }

          // React Router
          if (id.includes('node_modules/react-router') ||
              id.includes('node_modules/@remix-run')) {
            return 'router';
          }

          // Supabase
          if (id.includes('node_modules/@supabase')) {
            return 'supabase';
          }

          // State management
          if (id.includes('node_modules/zustand')) {
            return 'state';
          }

          // UI components and icons
          if (id.includes('node_modules/lucide-react')) {
            return 'icons';
          }

          // Video player related code
          if (id.includes('/src/components/VideoPlayer') ||
              id.includes('/src/components/ui/FullscreenProgressBar') ||
              id.includes('/src/components/ui/MobileProgressBar') ||
              id.includes('/src/components/ui/MinimizedVideoPlayer')) {
            return 'video-player';
          }

          // UI components
          if (id.includes('/src/components/ui/')) {
            return 'ui-components';
          }

          // Utilities
          if (id.includes('/src/utils/')) {
            return 'utils';
          }
        },
        // Ensure chunks have predictable names for better caching
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
      },
    },
    chunkSizeWarningLimit: 1000,
    sourcemap: false,
    // Enable module concatenation for better tree-shaking
    target: 'esnext',
    reportCompressedSize: true,
    // Optimize asset handling
    assetsInlineLimit: 4096, // Inline assets smaller than 4KB
    // Better asset naming for caching
    assetFileNames: (assetInfo) => {
      const info = assetInfo.name?.split('.') || [];
      const ext = info[info.length - 1];
      if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext || '')) {
        return `assets/images/[name]-[hash][extname]`;
      }
      if (/woff2?|eot|ttf|otf/i.test(ext || '')) {
        return `assets/fonts/[name]-[hash][extname]`;
      }
      return `assets/[name]-[hash][extname]`;
    },
  },
  server: {
    port: 3000,
    strictPort: true,
    host: true,
    // Enable HMR for faster development
    hmr: {
      overlay: true,
    },
  },
  // Add esbuild options for better performance
  esbuild: {
    logOverride: { 'this-is-undefined-in-esm': 'silent' },
  },
});
